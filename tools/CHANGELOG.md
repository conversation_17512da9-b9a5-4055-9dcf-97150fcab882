# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [54.0.58](https://github.com/compare/...@sage/xtrem~tools@54.0.58) (2025-06-19)

### Bug Fixes

### Features


## [54.0.57](https://github.com/compare/...@sage/xtrem~tools@54.0.57) (2025-06-16)

### Bug Fixes

### Features


## [54.0.56](https://github.com/compare/...@sage/xtrem~tools@54.0.56) (2025-06-12)

### Bug Fixes

### Features


## [54.0.55](https://github.com/compare/...@sage/xtrem~tools@54.0.55) (2025-06-05)

### Bug Fixes

### Features


## [54.0.54](https://github.com/compare/...@sage/xtrem~tools@54.0.54) (2025-06-03)

### Bug Fixes

### Features


## [54.0.53](https://github.com/compare/...@sage/xtrem~tools@54.0.53) (2025-05-21)

### Bug Fixes

### Features


## [54.0.52](https://github.com/compare/...@sage/xtrem~tools@54.0.52) (2025-05-20)

### Bug Fixes

### Features


## [54.0.51](https://github.com/compare/...@sage/xtrem~tools@54.0.51) (2025-05-16)

### Bug Fixes

### Features


## [54.0.50](https://github.com/compare/...@sage/xtrem~tools@54.0.50) (2025-05-15)

### Bug Fixes

### Features


## [54.0.49](https://github.com/compare/...@sage/xtrem~tools@54.0.49) (2025-05-14)

### Bug Fixes

### Features


## [54.0.48](https://github.com/compare/...@sage/xtrem~tools@54.0.48) (2025-05-14)

### Bug Fixes

### Features


## [54.0.47](https://github.com/compare/...@sage/xtrem~tools@54.0.47) (2025-05-12)

### Bug Fixes

### Features


## [54.0.46](https://github.com/compare/...@sage/xtrem~tools@54.0.46) (2025-05-07)

### Bug Fixes

### Features


## [54.0.45](https://github.com/compare/...@sage/xtrem~tools@54.0.45) (2025-05-07)

### Bug Fixes

### Features


## [54.0.44](https://github.com/compare/...@sage/xtrem~tools@54.0.44) (2025-05-07)

### Bug Fixes

### Features


## [54.0.43](https://github.com/compare/...@sage/xtrem~tools@54.0.43) (2025-05-06)

### Bug Fixes

### Features


## [54.0.42](https://github.com/compare/...@sage/xtrem~tools@54.0.42) (2025-05-06)

### Bug Fixes

### Features


## [54.0.41](https://github.com/compare/...@sage/xtrem~tools@54.0.41) (2025-05-06)

### Bug Fixes

### Features


## [54.0.40](https://github.com/compare/...@sage/xtrem~tools@54.0.40) (2025-05-06)

### Bug Fixes

### Features


## [54.0.39](https://github.com/compare/...@sage/xtrem~tools@54.0.39) (2025-05-02)

### Bug Fixes

### Features


## [54.0.38](https://github.com/compare/...@sage/xtrem~tools@54.0.38) (2025-04-29)

### Bug Fixes

### Features


## [54.0.37](https://github.com/compare/...@sage/xtrem~tools@54.0.37) (2025-04-28)

### Bug Fixes

### Features


## [54.0.36](https://github.com/compare/...@sage/xtrem~tools@54.0.36) (2025-04-24)

### Bug Fixes

### Features


## [54.0.35](https://github.com/compare/...@sage/xtrem~tools@54.0.35) (2025-04-18)

### Bug Fixes

### Features


## [54.0.34](https://github.com/compare/...@sage/xtrem~tools@54.0.34) (2025-04-17)

### Bug Fixes

### Features


## [54.0.33](https://github.com/compare/...@sage/xtrem~tools@54.0.33) (2025-04-16)

### Bug Fixes

### Features


## [54.0.32](https://github.com/compare/...@sage/xtrem~tools@54.0.32) (2025-04-15)

### Bug Fixes

### Features


## [54.0.31](https://github.com/compare/...@sage/xtrem~tools@54.0.31) (2025-04-14)

### Bug Fixes

### Features


## [54.0.30](https://github.com/compare/...@sage/xtrem~tools@54.0.30) (2025-04-13)

### Bug Fixes

### Features


## [54.0.29](https://github.com/compare/...@sage/xtrem~tools@54.0.29) (2025-04-12)

### Bug Fixes

### Features


## [54.0.28](https://github.com/compare/...@sage/xtrem~tools@54.0.28) (2025-04-11)

### Bug Fixes

### Features


## [54.0.27](https://github.com/compare/...@sage/xtrem~tools@54.0.27) (2025-04-10)

### Bug Fixes

### Features


## [54.0.26](https://github.com/compare/...@sage/xtrem~tools@54.0.26) (2025-04-09)

### Bug Fixes

### Features


## [54.0.25](https://github.com/compare/...@sage/xtrem~tools@54.0.25) (2025-04-08)

### Bug Fixes

### Features


## [54.0.24](https://github.com/compare/...@sage/xtrem~tools@54.0.24) (2025-04-07)

### Bug Fixes

### Features


## [54.0.23](https://github.com/compare/...@sage/xtrem~tools@54.0.23) (2025-04-06)

### Bug Fixes

### Features


## [54.0.22](https://github.com/compare/...@sage/xtrem~tools@54.0.22) (2025-04-05)

### Bug Fixes

### Features


## [54.0.21](https://github.com/compare/...@sage/xtrem~tools@54.0.21) (2025-04-04)

### Bug Fixes

### Features


## [54.0.20](https://github.com/compare/...@sage/xtrem~tools@54.0.20) (2025-04-03)

### Bug Fixes

### Features


## [54.0.19](https://github.com/compare/...@sage/xtrem~tools@54.0.19) (2025-04-02)

### Bug Fixes

### Features


## [54.0.18](https://github.com/compare/...@sage/xtrem~tools@54.0.18) (2025-04-02)

### Bug Fixes

### Features


## [54.0.17](https://github.com/compare/...@sage/xtrem~tools@54.0.17) (2025-04-01)

### Bug Fixes

### Features


## [54.0.16](https://github.com/compare/...@sage/xtrem~tools@54.0.16) (2025-03-31)

### Bug Fixes

### Features


## [54.0.15](https://github.com/compare/...@sage/xtrem~tools@54.0.15) (2025-03-31)

### Bug Fixes

### Features


## [54.0.14](https://github.com/compare/...@sage/xtrem~tools@54.0.14) (2025-03-30)

### Bug Fixes

### Features


## [54.0.13](https://github.com/compare/...@sage/xtrem~tools@54.0.13) (2025-03-29)

### Bug Fixes

### Features


## [54.0.12](https://github.com/compare/...@sage/xtrem~tools@54.0.12) (2025-03-28)

### Bug Fixes

### Features


## [54.0.11](https://github.com/compare/...@sage/xtrem~tools@54.0.11) (2025-03-27)

### Bug Fixes

### Features


## [54.0.10](https://github.com/compare/...@sage/xtrem~tools@54.0.10) (2025-03-27)

### Bug Fixes

### Features


## [54.0.9](https://github.com/compare/...@sage/xtrem~tools@54.0.9) (2025-03-27)

### Bug Fixes

### Features


## [54.0.8](https://github.com/compare/...@sage/xtrem~tools@54.0.8) (2025-03-26)

### Bug Fixes

### Features
* **notes-node:** XT-88048 - Implement the Note node ([XT-88048](https://jira.sage.com/browse/XT-88048)) ([#24674](https://github.com/Sage-ERP-X3/xtrem/issues/24674))   ([d2ffc07](https://github.com/Sage-ERP-X3/xtrem/commit/d2ffc07e0aac7e7cefdd4f5ecbae95986091069c))


## [54.0.7](https://github.com/compare/...@sage/xtrem~tools@54.0.7) (2025-03-25)

### Bug Fixes

### Features


## [54.0.6](https://github.com/compare/...@sage/xtrem~tools@54.0.6) (2025-03-24)

### Bug Fixes

### Features


## [54.0.5](https://github.com/compare/...@sage/xtrem~tools@54.0.5) (2025-03-23)

### Bug Fixes

### Features


## [54.0.4](https://github.com/compare/...@sage/xtrem~tools@54.0.4) (2025-03-22)

### Bug Fixes

### Features


## [54.0.3](https://github.com/compare/...@sage/xtrem~tools@54.0.3) (2025-03-21)

### Bug Fixes

### Features


## [54.0.2](https://github.com/compare/...@sage/xtrem~tools@54.0.2) (2025-03-20)

### Bug Fixes

### Features


## [54.0.1](https://github.com/compare/...@sage/xtrem~tools@54.0.1) (2025-03-20)

### Bug Fixes

### Features


## [54.0.0](https://github.com/compare/...@sage/xtrem~tools@54.0.0) (2025-03-20)

### Bug Fixes

### Features


## [53.0.29](https://github.com/compare/...@sage/xtrem~tools@53.0.29) (2025-03-19)

### Bug Fixes

### Features
* **xtremSystem:** XT-89637 - shopfloor device token and operator ([XT-89637](https://jira.sage.com/browse/XT-89637)) ([#24658](https://github.com/Sage-ERP-X3/xtrem/issues/24658))   ([ab8b117](https://github.com/Sage-ERP-X3/xtrem/commit/ab8b1170419a9b5f71cfd4f2eb2696d358406d67))


## [53.0.28](https://github.com/compare/...@sage/xtrem~tools@53.0.28) (2025-03-18)

### Bug Fixes

### Features


## [53.0.27](https://github.com/compare/...@sage/xtrem~tools@53.0.27) (2025-03-17)

### Bug Fixes

### Features


## [53.0.26](https://github.com/compare/...@sage/xtrem~tools@53.0.26) (2025-03-17)

### Bug Fixes

### Features


## [53.0.25](https://github.com/compare/...@sage/xtrem~tools@53.0.25) (2025-03-16)

### Bug Fixes

### Features


## [53.0.24](https://github.com/compare/...@sage/xtrem~tools@53.0.24) (2025-03-15)

### Bug Fixes

### Features


## [53.0.23](https://github.com/compare/...@sage/xtrem~tools@53.0.23) (2025-03-14)

### Bug Fixes

### Features


## [53.0.22](https://github.com/compare/...@sage/xtrem~tools@53.0.22) (2025-03-13)

### Bug Fixes

### Features


## [53.0.21](https://github.com/compare/...@sage/xtrem~tools@53.0.21) (2025-03-13)

### Bug Fixes

### Features


## [53.0.20](https://github.com/compare/...@sage/xtrem~tools@53.0.20) (2025-03-12)

### Bug Fixes

### Features


## [53.0.19](https://github.com/compare/...@sage/xtrem~tools@53.0.19) (2025-03-12)

### Bug Fixes

### Features


## [53.0.18](https://github.com/compare/...@sage/xtrem~tools@53.0.18) (2025-03-11)

### Bug Fixes

### Features


## [53.0.17](https://github.com/compare/...@sage/xtrem~tools@53.0.17) (2025-03-10)

### Bug Fixes

### Features


## [53.0.16](https://github.com/compare/...@sage/xtrem~tools@53.0.16) (2025-03-09)

### Bug Fixes

### Features


## [53.0.15](https://github.com/compare/...@sage/xtrem~tools@53.0.15) (2025-03-08)

### Bug Fixes

### Features


## [53.0.14](https://github.com/compare/...@sage/xtrem~tools@53.0.14) (2025-03-07)

### Bug Fixes
* **deps:** update dependency typescript to v5.8.2   ([#24407](https://github.com/Sage-ERP-X3/xtrem/issues/24407))   ([ef61f2f](https://github.com/Sage-ERP-X3/xtrem/commit/ef61f2ff5202e48ebf85d90690bf9a1052afc70c))

### Features


## [53.0.13](https://github.com/compare/...@sage/xtrem~tools@53.0.13) (2025-03-06)

### Bug Fixes

### Features


## [53.0.12](https://github.com/compare/...@sage/xtrem~tools@53.0.12) (2025-03-05)

### Bug Fixes

### Features


## [53.0.11](https://github.com/compare/...@sage/xtrem~tools@53.0.11) (2025-03-04)

### Bug Fixes

### Features


## [53.0.10](https://github.com/compare/...@sage/xtrem~tools@53.0.10) (2025-03-03)

### Bug Fixes

### Features


## [53.0.9](https://github.com/compare/...@sage/xtrem~tools@53.0.9) (2025-03-02)

### Bug Fixes

### Features


## [53.0.8](https://github.com/compare/...@sage/xtrem~tools@53.0.8) (2025-03-01)

### Bug Fixes

### Features


## [53.0.7](https://github.com/compare/...@sage/xtrem~tools@53.0.7) (2025-02-28)

### Bug Fixes

### Features


## [53.0.6](https://github.com/compare/...@sage/xtrem~tools@53.0.6) (2025-02-27)

### Bug Fixes

### Features


## [53.0.5](https://github.com/compare/...@sage/xtrem~tools@53.0.5) (2025-02-26)

### Bug Fixes

### Features


## [53.0.4](https://github.com/compare/...@sage/xtrem~tools@53.0.4) (2025-02-25)

### Bug Fixes

### Features


## [53.0.3](https://github.com/compare/...@sage/xtrem~tools@53.0.3) (2025-02-24)

### Bug Fixes

### Features


## [53.0.2](https://github.com/compare/...@sage/xtrem~tools@53.0.2) (2025-02-20)

### Bug Fixes

### Features


## [53.0.1](https://github.com/compare/...@sage/xtrem~tools@53.0.1) (2025-02-20)

### Bug Fixes

### Features


## [53.0.0](https://github.com/compare/...@sage/xtrem~tools@53.0.0) (2025-02-20)

### Bug Fixes

### Features


## [52.0.29](https://github.com/compare/...@sage/xtrem~tools@52.0.29) (2025-02-19)

### Bug Fixes

### Features


## [52.0.28](https://github.com/compare/...@sage/xtrem~tools@52.0.28) (2025-02-18)

### Bug Fixes

### Features


## [52.0.27](https://github.com/compare/...@sage/xtrem~tools@52.0.27) (2025-02-17)

### Bug Fixes

### Features


## [52.0.26](https://github.com/compare/...@sage/xtrem~tools@52.0.26) (2025-02-16)

### Bug Fixes

### Features


## [52.0.25](https://github.com/compare/...@sage/xtrem~tools@52.0.25) (2025-02-15)

### Bug Fixes

### Features


## [52.0.24](https://github.com/compare/...@sage/xtrem~tools@52.0.24) (2025-02-14)

### Bug Fixes

### Features


## [52.0.23](https://github.com/compare/...@sage/xtrem~tools@52.0.23) (2025-02-12)

### Bug Fixes

### Features
* redos eslint plugin XT-89040 ([XT-89040](https://jira.sage.com/browse/XT-89040)) ([#24054](https://github.com/Sage-ERP-X3/xtrem/issues/24054))   ([6641e00](https://github.com/Sage-ERP-X3/xtrem/commit/6641e00faabfe33acf8a0f2d2b9fae49beb4b5f7))


## [52.0.22](https://github.com/compare/...@sage/xtrem~tools@52.0.22) (2025-02-11)

### Bug Fixes

### Features


## [52.0.21](https://github.com/compare/...@sage/xtrem~tools@52.0.21) (2025-02-10)

### Bug Fixes

### Features


## [52.0.20](https://github.com/compare/...@sage/xtrem~tools@52.0.20) (2025-02-09)

### Bug Fixes

### Features


## [52.0.19](https://github.com/compare/...@sage/xtrem~tools@52.0.19) (2025-02-08)

### Bug Fixes

### Features


## [52.0.18](https://github.com/compare/...@sage/xtrem~tools@52.0.18) (2025-02-07)

### Bug Fixes

### Features


## [52.0.17](https://github.com/compare/...@sage/xtrem~tools@52.0.17) (2025-02-06)

### Bug Fixes

### Features


## [52.0.16](https://github.com/compare/...@sage/xtrem~tools@52.0.16) (2025-02-05)

### Bug Fixes

### Features


## [52.0.15](https://github.com/compare/...@sage/xtrem~tools@52.0.15) (2025-02-04)

### Bug Fixes
* XT-88557 lower mocha version ([XT-88557](https://jira.sage.com/browse/XT-88557)) ([#23860](https://github.com/Sage-ERP-X3/xtrem/issues/23860))   ([e7a8c25](https://github.com/Sage-ERP-X3/xtrem/commit/e7a8c25a690ee560879f4299be1f70ad71cbc0b8))

### Features


## [52.0.14](https://github.com/compare/...@sage/xtrem~tools@52.0.14) (2025-02-03)

### Bug Fixes

### Features


## [52.0.13](https://github.com/compare/...@sage/xtrem~tools@52.0.13) (2025-02-01)

### Bug Fixes

### Features


## [52.0.12](https://github.com/compare/...@sage/xtrem~tools@52.0.12) (2025-01-31)

### Bug Fixes

### Features


## [52.0.11](https://github.com/compare/...@sage/xtrem~tools@52.0.11) (2025-01-30)

### Bug Fixes

### Features


## [52.0.10](https://github.com/compare/...@sage/xtrem~tools@52.0.10) (2025-01-29)

### Bug Fixes

### Features


## [52.0.9](https://github.com/compare/...@sage/xtrem~tools@52.0.9) (2025-01-29)

### Bug Fixes

### Features


## [52.0.8](https://github.com/compare/...@sage/xtrem~tools@52.0.8) (2025-01-28)

### Bug Fixes

### Features
* add responsive dashboards   ([#23612](https://github.com/Sage-ERP-X3/xtrem/issues/23612))   ([a0ef60b](https://github.com/Sage-ERP-X3/xtrem/commit/a0ef60b8bb09e86ef5fbc0f07aa94e37b41e6087))


## [52.0.7](https://github.com/compare/...@sage/xtrem~tools@52.0.7) (2025-01-28)

### Bug Fixes

### Features


## [52.0.6](https://github.com/compare/...@sage/xtrem~tools@52.0.6) (2025-01-27)

### Bug Fixes

### Features


## [52.0.5](https://github.com/compare/...@sage/xtrem~tools@52.0.5) (2025-01-26)

### Bug Fixes

### Features


## [52.0.4](https://github.com/compare/...@sage/xtrem~tools@52.0.4) (2025-01-25)

### Bug Fixes

### Features


## [52.0.3](https://github.com/compare/...@sage/xtrem~tools@52.0.3) (2025-01-24)

### Bug Fixes

### Features


## [52.0.2](https://github.com/compare/...@sage/xtrem~tools@52.0.2) (2025-01-23)

### Bug Fixes

### Features


## [52.0.1](https://github.com/compare/...@sage/xtrem~tools@52.0.1) (2025-01-23)

### Bug Fixes

### Features


## [52.0.0](https://github.com/compare/...@sage/xtrem~tools@52.0.0) (2025-01-23)

### Bug Fixes

### Features


## [51.0.35](https://github.com/compare/...@sage/xtrem~tools@51.0.35) (2025-01-22)

### Bug Fixes

### Features


## [51.0.34](https://github.com/compare/...@sage/xtrem~tools@51.0.34) (2025-01-21)

### Bug Fixes

### Features


## [51.0.33](https://github.com/compare/...@sage/xtrem~tools@51.0.33) (2025-01-20)

### Bug Fixes

### Features


## [51.0.32](https://github.com/compare/...@sage/xtrem~tools@51.0.32) (2025-01-20)

### Bug Fixes

### Features


## [51.0.31](https://github.com/compare/...@sage/xtrem~tools@51.0.31) (2025-01-18)

### Bug Fixes

### Features


## [51.0.30](https://github.com/compare/...@sage/xtrem~tools@51.0.30) (2025-01-17)

### Bug Fixes

### Features


## [51.0.29](https://github.com/compare/...@sage/xtrem~tools@51.0.29) (2025-01-16)

### Bug Fixes

### Features


## [51.0.28](https://github.com/compare/...@sage/xtrem~tools@51.0.28) (2025-01-15)

### Bug Fixes

### Features
* XT-87326 create tag node ([XT-87326](https://jira.sage.com/browse/XT-87326)) ([#23541](https://github.com/Sage-ERP-X3/xtrem/issues/23541))   ([b034dba](https://github.com/Sage-ERP-X3/xtrem/commit/b034dbafdbca782c452e73d2ea752466ad660442))


## [51.0.27](https://github.com/compare/...@sage/xtrem~tools@51.0.27) (2025-01-14)

### Bug Fixes

### Features


## [51.0.26](https://github.com/compare/...@sage/xtrem~tools@51.0.26) (2025-01-13)

### Bug Fixes
* XT-87121 fix upgrade of uploaded files ([XT-87121](https://jira.sage.com/browse/XT-87121)) ([#23506](https://github.com/Sage-ERP-X3/xtrem/issues/23506))   ([e0da0d2](https://github.com/Sage-ERP-X3/xtrem/commit/e0da0d2ba83664269199b45bf987839a8837f11b))

### Features


## [51.0.25](https://github.com/compare/...@sage/xtrem~tools@51.0.25) (2025-01-12)

### Bug Fixes

### Features


## [51.0.24](https://github.com/compare/...@sage/xtrem~tools@51.0.24) (2025-01-11)

### Bug Fixes

### Features


## [51.0.23](https://github.com/compare/...@sage/xtrem~tools@51.0.23) (2025-01-10)

### Bug Fixes
* **interop:** XAPPSF-1029 update sys app node   ([#22850](https://github.com/Sage-ERP-X3/xtrem/issues/22850))   ([1f2ef7f](https://github.com/Sage-ERP-X3/xtrem/commit/1f2ef7f464e2ffca85bdae167e3b56cf9194ca14))

### Features


## [51.0.22](https://github.com/compare/...@sage/xtrem~tools@51.0.22) (2025-01-09)

### Bug Fixes

### Features


## [51.0.21](https://github.com/compare/...@sage/xtrem~tools@51.0.21) (2025-01-08)

### Bug Fixes
* **deps:** update dependency mocha to v11   ([#23267](https://github.com/Sage-ERP-X3/xtrem/issues/23267))   ([aad520e](https://github.com/Sage-ERP-X3/xtrem/commit/aad520e4f1226e18c486fe256ea4730dffc4bd91))

### Features


## [51.0.20](https://github.com/compare/...@sage/xtrem~tools@51.0.20) (2025-01-07)

### Bug Fixes

### Features
* **apps:** cleanup xtrem cli deps XT-85855 ([XT-85855](https://jira.sage.com/browse/XT-85855)) ([#23141](https://github.com/Sage-ERP-X3/xtrem/issues/23141))   ([6c0cdeb](https://github.com/Sage-ERP-X3/xtrem/commit/6c0cdeb31ec2c8c41eacc8703c2d1c56fbb991a0))


## [51.0.19](https://github.com/compare/...@sage/xtrem~tools@51.0.19) (2025-01-06)

### Bug Fixes

### Features


## [51.0.18](https://github.com/compare/...@sage/xtrem~tools@51.0.18) (2025-01-05)

### Bug Fixes

### Features


## [51.0.17](https://github.com/compare/...@sage/xtrem~tools@51.0.17) (2025-01-04)

### Bug Fixes

### Features


## [51.0.16](https://github.com/compare/...@sage/xtrem~tools@51.0.16) (2025-01-03)

### Bug Fixes

### Features


## [51.0.15](https://github.com/compare/...@sage/xtrem~tools@51.0.15) (2025-01-02)

### Bug Fixes

### Features


## [51.0.14](https://github.com/compare/...@sage/xtrem~tools@51.0.14) (2025-01-01)

### Bug Fixes

### Features


## [51.0.13](https://github.com/compare/...@sage/xtrem~tools@51.0.13) (2024-12-31)

### Bug Fixes

### Features


## [51.0.12](https://github.com/compare/...@sage/xtrem~tools@51.0.12) (2024-12-29)

### Bug Fixes

### Features


## [51.0.11](https://github.com/compare/...@sage/xtrem~tools@51.0.11) (2024-12-28)

### Bug Fixes

### Features


## [51.0.10](https://github.com/compare/...@sage/xtrem~tools@51.0.10) (2024-12-27)

### Bug Fixes

### Features


## [51.0.9](https://github.com/compare/...@sage/xtrem~tools@51.0.9) (2024-12-26)

### Bug Fixes

### Features


## [51.0.8](https://github.com/compare/...@sage/xtrem~tools@51.0.8) (2024-12-25)

### Bug Fixes

### Features


## [51.0.7](https://github.com/compare/...@sage/xtrem~tools@51.0.7) (2024-12-24)

### Bug Fixes

### Features


## [51.0.6](https://github.com/compare/...@sage/xtrem~tools@51.0.6) (2024-12-23)

### Bug Fixes

### Features


## [51.0.5](https://github.com/compare/...@sage/xtrem~tools@51.0.5) (2024-12-22)

### Bug Fixes

### Features


## [51.0.4](https://github.com/compare/...@sage/xtrem~tools@51.0.4) (2024-12-21)

### Bug Fixes

### Features


## [51.0.3](https://github.com/compare/...@sage/xtrem~tools@51.0.3) (2024-12-20)

### Bug Fixes

### Features


## [51.0.2](https://github.com/compare/...@sage/xtrem~tools@51.0.2) (2024-12-19)

### Bug Fixes

### Features


## [51.0.1](https://github.com/compare/...@sage/xtrem~tools@51.0.1) (2024-12-19)

### Bug Fixes

### Features


## [51.0.0](https://github.com/compare/...@sage/xtrem~tools@51.0.0) (2024-12-19)

### Bug Fixes

### Features


## [50.0.30](https://github.com/compare/...@sage/xtrem~tools@50.0.30) (2024-12-18)

### Bug Fixes

### Features
* XT-83511 Enable more entities in workflow ([XT-83511](https://jira.sage.com/browse/XT-83511)) ([#23187](https://github.com/Sage-ERP-X3/xtrem/issues/23187))   ([a71ff7a](https://github.com/Sage-ERP-X3/xtrem/commit/a71ff7afb373f7c8b7d2b03b1db52f4003e7f528))


## [50.0.29](https://github.com/compare/...@sage/xtrem~tools@50.0.29) (2024-12-17)

### Bug Fixes

### Features


## [50.0.28](https://github.com/compare/...@sage/xtrem~tools@50.0.28) (2024-12-16)

### Bug Fixes

### Features


## [50.0.27](https://github.com/compare/...@sage/xtrem~tools@50.0.27) (2024-12-15)

### Bug Fixes

### Features


## [50.0.26](https://github.com/compare/...@sage/xtrem~tools@50.0.26) (2024-12-14)

### Bug Fixes

### Features


## [50.0.25](https://github.com/compare/...@sage/xtrem~tools@50.0.25) (2024-12-13)

### Bug Fixes

### Features


## [50.0.24](https://github.com/compare/...@sage/xtrem~tools@50.0.24) (2024-12-12)

### Bug Fixes

### Features


## [50.0.23](https://github.com/compare/...@sage/xtrem~tools@50.0.23) (2024-12-11)

### Bug Fixes

### Features


## [50.0.22](https://github.com/compare/...@sage/xtrem~tools@50.0.22) (2024-12-10)

### Bug Fixes

### Features


## [50.0.21](https://github.com/compare/...@sage/xtrem~tools@50.0.21) (2024-12-09)

### Bug Fixes

### Features


## [50.0.20](https://github.com/compare/...@sage/xtrem~tools@50.0.20) (2024-12-08)

### Bug Fixes

### Features


## [50.0.19](https://github.com/compare/...@sage/xtrem~tools@50.0.19) (2024-12-07)

### Bug Fixes

### Features


## [50.0.18](https://github.com/compare/...@sage/xtrem~tools@50.0.18) (2024-12-06)

### Bug Fixes

### Features


## [50.0.17](https://github.com/compare/...@sage/xtrem~tools@50.0.17) (2024-12-05)

### Bug Fixes

### Features


## [50.0.16](https://github.com/compare/...@sage/xtrem~tools@50.0.16) (2024-12-04)

### Bug Fixes

### Features


## [50.0.15](https://github.com/compare/...@sage/xtrem~tools@50.0.15) (2024-12-03)

### Bug Fixes

### Features
* XT-84069 Merge UploadedFile and Attachment nodes ([XT-84069](https://jira.sage.com/browse/XT-84069)) ([#22586](https://github.com/Sage-ERP-X3/xtrem/issues/22586))   ([96b4a23](https://github.com/Sage-ERP-X3/xtrem/commit/96b4a235ca3ab67015f65c89726341f1e792bc7a))


## [50.0.14](https://github.com/compare/...@sage/xtrem~tools@50.0.14) (2024-12-02)

### Bug Fixes

### Features


## [50.0.13](https://github.com/compare/...@sage/xtrem~tools@50.0.13) (2024-12-01)

### Bug Fixes

### Features


## [50.0.12](https://github.com/compare/...@sage/xtrem~tools@50.0.12) (2024-11-30)

### Bug Fixes

### Features


## [50.0.11](https://github.com/compare/...@sage/xtrem~tools@50.0.11) (2024-11-29)

### Bug Fixes

### Features


## [50.0.10](https://github.com/compare/...@sage/xtrem~tools@50.0.10) (2024-11-29)

### Bug Fixes

### Features


## [50.0.9](https://github.com/compare/...@sage/xtrem~tools@50.0.9) (2024-11-28)

### Bug Fixes

### Features


## [50.0.8](https://github.com/compare/...@sage/xtrem~tools@50.0.8) (2024-11-27)

### Bug Fixes

### Features


## [50.0.7](https://github.com/compare/...@sage/xtrem~tools@50.0.7) (2024-11-26)

### Bug Fixes

### Features


## [50.0.6](https://github.com/compare/...@sage/xtrem~tools@50.0.6) (2024-11-26)

### Bug Fixes

### Features


## [50.0.5](https://github.com/compare/...@sage/xtrem~tools@50.0.5) (2024-11-25)

### Bug Fixes

### Features


## [50.0.4](https://github.com/compare/...@sage/xtrem~tools@50.0.4) (2024-11-24)

### Bug Fixes

### Features


## [50.0.3](https://github.com/compare/...@sage/xtrem~tools@50.0.3) (2024-11-23)

### Bug Fixes

### Features


## [50.0.2](https://github.com/compare/...@sage/xtrem~tools@50.0.2) (2024-11-22)

### Bug Fixes

### Features


## [50.0.1](https://github.com/compare/...@sage/xtrem~tools@50.0.1) (2024-11-22)

### Bug Fixes

### Features


## [50.0.0](https://github.com/compare/...@sage/xtrem~tools@50.0.0) (2024-11-22)

### Bug Fixes

### Features


## [49.0.30](https://github.com/compare/...@sage/xtrem~tools@49.0.30) (2024-11-22)

### Bug Fixes

### Features


## [49.0.29](https://github.com/compare/...@sage/xtrem~tools@49.0.29) (2024-11-21)

### Bug Fixes

### Features


## [49.0.28](https://github.com/compare/...@sage/xtrem~tools@49.0.28) (2024-11-20)

### Bug Fixes

### Features


## [49.0.27](https://github.com/compare/...@sage/xtrem~tools@49.0.27) (2024-11-19)

### Bug Fixes

### Features


## [49.0.26](https://github.com/compare/...@sage/xtrem~tools@49.0.26) (2024-11-18)

### Bug Fixes

### Features


## [49.0.25](https://github.com/compare/...@sage/xtrem~tools@49.0.25) (2024-11-17)

### Bug Fixes

### Features


## [49.0.24](https://github.com/compare/...@sage/xtrem~tools@49.0.24) (2024-11-16)

### Bug Fixes

### Features


## [49.0.23](https://github.com/compare/...@sage/xtrem~tools@49.0.23) (2024-11-15)

### Bug Fixes

### Features


## [49.0.22](https://github.com/compare/...@sage/xtrem~tools@49.0.22) (2024-11-14)

### Bug Fixes

### Features
* XT-81917 export by definition ([XT-81917](https://jira.sage.com/browse/XT-81917)) ([#22321](https://github.com/Sage-ERP-X3/xtrem/issues/22321))   ([2c75172](https://github.com/Sage-ERP-X3/xtrem/commit/2c751725f347a3cb89e43cbf1084f2d28a8b2451))


## [49.0.21](https://github.com/compare/...@sage/xtrem~tools@49.0.21) (2024-11-13)

### Bug Fixes

### Features


## [49.0.20](https://github.com/compare/...@sage/xtrem~tools@49.0.20) (2024-11-12)

### Bug Fixes

### Features
* XT-77310 global namespace flag and export by id mutation ([XT-77310](https://jira.sage.com/browse/XT-77310)) ([#22175](https://github.com/Sage-ERP-X3/xtrem/issues/22175))   ([87379b9](https://github.com/Sage-ERP-X3/xtrem/commit/87379b999f3c8c1982fd246f39861cf33f1e0b1e))


## [49.0.19](https://github.com/compare/...@sage/xtrem~tools@49.0.19) (2024-11-11)

### Bug Fixes

### Features


## [49.0.18](https://github.com/compare/...@sage/xtrem~tools@49.0.18) (2024-11-10)

### Bug Fixes

### Features


## [49.0.17](https://github.com/compare/...@sage/xtrem~tools@49.0.17) (2024-11-09)

### Bug Fixes

### Features


## [49.0.16](https://github.com/compare/...@sage/xtrem~tools@49.0.16) (2024-11-08)

### Bug Fixes

### Features


## [49.0.15](https://github.com/compare/...@sage/xtrem~tools@49.0.15) (2024-11-07)

### Bug Fixes

### Features


## [49.0.14](https://github.com/compare/...@sage/xtrem~tools@49.0.14) (2024-11-06)

### Bug Fixes

### Features


## [49.0.13](https://github.com/compare/...@sage/xtrem~tools@49.0.13) (2024-11-05)

### Bug Fixes

### Features


## [49.0.12](https://github.com/compare/...@sage/xtrem~tools@49.0.12) (2024-11-04)

### Bug Fixes

### Features


## [49.0.11](https://github.com/compare/...@sage/xtrem~tools@49.0.11) (2024-11-03)

### Bug Fixes

### Features


## [49.0.10](https://github.com/compare/...@sage/xtrem~tools@49.0.10) (2024-11-02)

### Bug Fixes

### Features


## [49.0.9](https://github.com/compare/...@sage/xtrem~tools@49.0.9) (2024-11-01)

### Bug Fixes

### Features


## [49.0.8](https://github.com/compare/...@sage/xtrem~tools@49.0.8) (2024-10-31)

### Bug Fixes

### Features


## [49.0.7](https://github.com/compare/...@sage/xtrem~tools@49.0.7) (2024-10-30)

### Bug Fixes

### Features


## [49.0.6](https://github.com/compare/...@sage/xtrem~tools@49.0.6) (2024-10-29)

### Bug Fixes

### Features
* XT-82152 add log messages to async mutation tracking query ([XT-82152](https://jira.sage.com/browse/XT-82152)) ([#21938](https://github.com/Sage-ERP-X3/xtrem/issues/21938))   ([2ddeabb](https://github.com/Sage-ERP-X3/xtrem/commit/2ddeabbc9609cbdbc9833b43e02440d3993e309e))


## [49.0.5](https://github.com/compare/...@sage/xtrem~tools@49.0.5) (2024-10-28)

### Bug Fixes

### Features


## [49.0.4](https://github.com/compare/...@sage/xtrem~tools@49.0.4) (2024-10-27)

### Bug Fixes

### Features


## [49.0.3](https://github.com/compare/...@sage/xtrem~tools@49.0.3) (2024-10-26)

### Bug Fixes

### Features


## [49.0.2](https://github.com/compare/...@sage/xtrem~tools@49.0.2) (2024-10-24)

### Bug Fixes

### Features


## [49.0.1](https://github.com/compare/...@sage/xtrem~tools@49.0.1) (2024-10-24)

### Bug Fixes

### Features


## [49.0.0](https://github.com/compare/...@sage/xtrem~tools@49.0.0) (2024-10-24)

### Bug Fixes

### Features


## [48.0.37](https://github.com/compare/...@sage/xtrem~tools@48.0.37) (2024-10-23)

### Bug Fixes

### Features


## [48.0.36](https://github.com/compare/...@sage/xtrem~tools@48.0.36) (2024-10-22)

### Bug Fixes

### Features


## [48.0.35](https://github.com/compare/...@sage/xtrem~tools@48.0.35) (2024-10-21)

### Bug Fixes

### Features


## [48.0.34](https://github.com/compare/...@sage/xtrem~tools@48.0.34) (2024-10-20)

### Bug Fixes

### Features


## [48.0.33](https://github.com/compare/...@sage/xtrem~tools@48.0.33) (2024-10-19)

### Bug Fixes

### Features


## [48.0.32](https://github.com/compare/...@sage/xtrem~tools@48.0.32) (2024-10-18)

### Bug Fixes

### Features


## [48.0.31](https://github.com/compare/...@sage/xtrem~tools@48.0.31) (2024-10-17)

### Bug Fixes

### Features


## [48.0.30](https://github.com/compare/...@sage/xtrem~tools@48.0.30) (2024-10-16)

### Bug Fixes

### Features


## [48.0.29](https://github.com/compare/...@sage/xtrem~tools@48.0.29) (2024-10-15)

### Bug Fixes

### Features


## [48.0.28](https://github.com/compare/...@sage/xtrem~tools@48.0.28) (2024-10-14)

### Bug Fixes

### Features


## [48.0.27](https://github.com/compare/...@sage/xtrem~tools@48.0.27) (2024-10-13)

### Bug Fixes

### Features


## [48.0.26](https://github.com/compare/...@sage/xtrem~tools@48.0.26) (2024-10-12)

### Bug Fixes

### Features


## [48.0.25](https://github.com/compare/...@sage/xtrem~tools@48.0.25) (2024-10-11)

### Bug Fixes

### Features


## [48.0.24](https://github.com/compare/...@sage/xtrem~tools@48.0.24) (2024-10-10)

### Bug Fixes

### Features


## [48.0.23](https://github.com/compare/...@sage/xtrem~tools@48.0.23) (2024-10-09)

### Bug Fixes

### Features


## [48.0.22](https://github.com/compare/...@sage/xtrem~tools@48.0.22) (2024-10-08)

### Bug Fixes

### Features


## [48.0.21](https://github.com/compare/...@sage/xtrem~tools@48.0.21) (2024-10-07)

### Bug Fixes

### Features


## [48.0.20](https://github.com/compare/...@sage/xtrem~tools@48.0.20) (2024-10-06)

### Bug Fixes

### Features


## [48.0.19](https://github.com/compare/...@sage/xtrem~tools@48.0.19) (2024-10-05)

### Bug Fixes

### Features


## [48.0.18](https://github.com/compare/...@sage/xtrem~tools@48.0.18) (2024-10-04)

### Bug Fixes

### Features


## [48.0.17](https://github.com/compare/...@sage/xtrem~tools@48.0.17) (2024-10-03)

### Bug Fixes

### Features


## [48.0.16](https://github.com/compare/...@sage/xtrem~tools@48.0.16) (2024-10-02)

### Bug Fixes

### Features


## [48.0.15](https://github.com/compare/...@sage/xtrem~tools@48.0.15) (2024-10-01)

### Bug Fixes

### Features


## [48.0.14](https://github.com/compare/...@sage/xtrem~tools@48.0.14) (2024-09-30)

### Bug Fixes

### Features


## [48.0.13](https://github.com/compare/...@sage/xtrem~tools@48.0.13) (2024-09-29)

### Bug Fixes

### Features


## [48.0.12](https://github.com/compare/...@sage/xtrem~tools@48.0.12) (2024-09-29)

### Bug Fixes

### Features


## [48.0.11](https://github.com/compare/...@sage/xtrem~tools@48.0.11) (2024-09-28)

### Bug Fixes

### Features


## [48.0.10](https://github.com/compare/...@sage/xtrem~tools@48.0.10) (2024-09-27)

### Bug Fixes

### Features


## [48.0.9](https://github.com/compare/...@sage/xtrem~tools@48.0.9) (2024-09-26)

### Bug Fixes

### Features


## [48.0.8](https://github.com/compare/...@sage/xtrem~tools@48.0.8) (2024-09-25)

### Bug Fixes
* XT-78516 alias Operation in generation of api.d.ts ([XT-78516](https://jira.sage.com/browse/XT-78516)) ([#21185](https://github.com/Sage-ERP-X3/xtrem/issues/21185))   ([f0a7ac2](https://github.com/Sage-ERP-X3/xtrem/commit/f0a7ac2f0add2c3e60343b7c90d1c49e8ab2b2b7))

### Features


## [48.0.7](https://github.com/compare/...@sage/xtrem~tools@48.0.7) (2024-09-24)

### Bug Fixes

### Features


## [48.0.6](https://github.com/compare/...@sage/xtrem~tools@48.0.6) (2024-09-23)

### Bug Fixes

### Features


## [48.0.5](https://github.com/compare/...@sage/xtrem~tools@48.0.5) (2024-09-22)

### Bug Fixes

### Features


## [48.0.4](https://github.com/compare/...@sage/xtrem~tools@48.0.4) (2024-09-21)

### Bug Fixes

### Features


## [48.0.3](https://github.com/compare/...@sage/xtrem~tools@48.0.3) (2024-09-20)

### Bug Fixes

### Features


## [48.0.2](https://github.com/compare/...@sage/xtrem~tools@48.0.2) (2024-09-19)

### Bug Fixes

### Features


## [48.0.1](https://github.com/compare/...@sage/xtrem~tools@48.0.1) (2024-09-19)

### Bug Fixes

### Features


## [48.0.0](https://github.com/compare/...@sage/xtrem~tools@48.0.0) (2024-09-19)

### Bug Fixes

### Features


## [47.0.30](https://github.com/compare/...@sage/xtrem~tools@47.0.30) (2024-09-19)

### Bug Fixes

### Features


## [47.0.29](https://github.com/compare/...@sage/xtrem~tools@47.0.29) (2024-09-18)

### Bug Fixes

### Features


## [47.0.28](https://github.com/compare/...@sage/xtrem~tools@47.0.28) (2024-09-17)

### Bug Fixes

### Features


## [47.0.27](https://github.com/compare/...@sage/xtrem~tools@47.0.27) (2024-09-16)

### Bug Fixes

### Features


## [47.0.26](https://github.com/compare/...@sage/xtrem~tools@47.0.26) (2024-09-15)

### Bug Fixes

### Features


## [47.0.25](https://github.com/compare/...@sage/xtrem~tools@47.0.25) (2024-09-14)

### Bug Fixes

### Features


## [47.0.24](https://github.com/compare/...@sage/xtrem~tools@47.0.24) (2024-09-13)

### Bug Fixes

### Features
* XT-78333 add xtrem-auditing dependency to xtrem-workflow ([XT-78333](https://jira.sage.com/browse/XT-78333)) ([#21032](https://github.com/Sage-ERP-X3/xtrem/issues/21032))   ([fbabb1c](https://github.com/Sage-ERP-X3/xtrem/commit/fbabb1c07c37e03426c6391ef284ccbd7ccd61d6))


## [47.0.23](https://github.com/compare/...@sage/xtrem~tools@47.0.23) (2024-09-12)

### Bug Fixes

### Features


## [47.0.22](https://github.com/compare/...@sage/xtrem~tools@47.0.22) (2024-09-11)

### Bug Fixes

### Features


## [47.0.21](https://github.com/compare/...@sage/xtrem~tools@47.0.21) (2024-09-10)

### Bug Fixes

### Features


## [47.0.20](https://github.com/compare/...@sage/xtrem~tools@47.0.20) (2024-09-09)

### Bug Fixes

### Features


## [47.0.19](https://github.com/compare/...@sage/xtrem~tools@47.0.19) (2024-09-08)

### Bug Fixes

### Features


## [47.0.18](https://github.com/compare/...@sage/xtrem~tools@47.0.18) (2024-09-07)

### Bug Fixes

### Features


## [47.0.17](https://github.com/compare/...@sage/xtrem~tools@47.0.17) (2024-09-06)

### Bug Fixes

### Features


## [47.0.16](https://github.com/compare/...@sage/xtrem~tools@47.0.16) (2024-09-05)

### Bug Fixes

### Features


## [47.0.15](https://github.com/compare/...@sage/xtrem~tools@47.0.15) (2024-09-04)

### Bug Fixes

### Features


## [47.0.14](https://github.com/compare/...@sage/xtrem~tools@47.0.14) (2024-09-03)

### Bug Fixes

### Features


## [47.0.13](https://github.com/compare/...@sage/xtrem~tools@47.0.13) (2024-09-02)

### Bug Fixes

### Features


## [47.0.12](https://github.com/compare/...@sage/xtrem~tools@47.0.12) (2024-09-01)

### Bug Fixes

### Features


## [47.0.11](https://github.com/compare/...@sage/xtrem~tools@47.0.11) (2024-08-31)

### Bug Fixes

### Features


## [47.0.10](https://github.com/compare/...@sage/xtrem~tools@47.0.10) (2024-08-30)

### Bug Fixes

### Features


## [47.0.9](https://github.com/compare/...@sage/xtrem~tools@47.0.9) (2024-08-29)

### Bug Fixes

### Features


## [47.0.8](https://github.com/compare/...@sage/xtrem~tools@47.0.8) (2024-08-28)

### Bug Fixes

### Features


## [47.0.7](https://github.com/compare/...@sage/xtrem~tools@47.0.7) (2024-08-27)

### Bug Fixes

### Features


## [47.0.6](https://github.com/compare/...@sage/xtrem~tools@47.0.6) (2024-08-26)

### Bug Fixes

### Features


## [47.0.5](https://github.com/compare/...@sage/xtrem~tools@47.0.5) (2024-08-25)

### Bug Fixes

### Features


## [47.0.4](https://github.com/compare/...@sage/xtrem~tools@47.0.4) (2024-08-24)

### Bug Fixes

### Features


## [47.0.3](https://github.com/compare/...@sage/xtrem~tools@47.0.3) (2024-08-23)

### Bug Fixes

### Features


## [47.0.2](https://github.com/compare/...@sage/xtrem~tools@47.0.2) (2024-08-22)

### Bug Fixes

### Features


## [47.0.1](https://github.com/compare/...@sage/xtrem~tools@47.0.1) (2024-08-22)

### Bug Fixes

### Features


## [47.0.0](https://github.com/compare/...@sage/xtrem~tools@47.0.0) (2024-08-22)

### Bug Fixes

### Features


## [46.0.38](https://github.com/compare/...@sage/xtrem~tools@46.0.38) (2024-08-22)

### Bug Fixes

### Features


## [46.0.37](https://github.com/compare/...@sage/xtrem~tools@46.0.37) (2024-08-21)

### Bug Fixes

### Features


## [46.0.36](https://github.com/compare/...@sage/xtrem~tools@46.0.36) (2024-08-20)

### Bug Fixes

### Features


## [46.0.35](https://github.com/compare/...@sage/xtrem~tools@46.0.35) (2024-08-18)

### Bug Fixes

### Features


## [46.0.34](https://github.com/compare/...@sage/xtrem~tools@46.0.34) (2024-08-17)

### Bug Fixes

### Features
* XT-76972 first brew of workflow test framework ([XT-76972](https://jira.sage.com/browse/XT-76972)) ([#20423](https://github.com/Sage-ERP-X3/xtrem/issues/20423))   ([9d32bf5](https://github.com/Sage-ERP-X3/xtrem/commit/9d32bf5a86e33c4b8dde61958f4d2b5ed81204d0))


## [46.0.33](https://github.com/compare/...@sage/xtrem~tools@46.0.33) (2024-08-16)

### Bug Fixes

### Features
* XT-76691 review and improve entity-created config page ([XT-76691](https://jira.sage.com/browse/XT-76691)) ([#20374](https://github.com/Sage-ERP-X3/xtrem/issues/20374))   ([56fe177](https://github.com/Sage-ERP-X3/xtrem/commit/56fe177e209a06f59b8c0089c37d6a12a8b0d9cd))


## [46.0.32](https://github.com/compare/...@sage/xtrem~tools@46.0.32) (2024-08-15)

### Bug Fixes

### Features


## [46.0.31](https://github.com/compare/...@sage/xtrem~tools@46.0.31) (2024-08-14)

### Bug Fixes

### Features


## [46.0.30](https://github.com/compare/...@sage/xtrem~tools@46.0.30) (2024-08-13)

### Bug Fixes

### Features


## [46.0.29](https://github.com/compare/...@sage/xtrem~tools@46.0.29) (2024-08-12)

### Bug Fixes

### Features


## [46.0.28](https://github.com/compare/...@sage/xtrem~tools@46.0.28) (2024-08-11)

### Bug Fixes

### Features


## [46.0.27](https://github.com/compare/...@sage/xtrem~tools@46.0.27) (2024-08-10)

### Bug Fixes

### Features


## [46.0.26](https://github.com/compare/...@sage/xtrem~tools@46.0.26) (2024-08-09)

### Bug Fixes

### Features


## [46.0.25](https://github.com/compare/...@sage/xtrem~tools@46.0.25) (2024-08-08)

### Bug Fixes

### Features


## [46.0.24](https://github.com/compare/...@sage/xtrem~tools@46.0.24) (2024-08-07)

### Bug Fixes

### Features


## [46.0.23](https://github.com/compare/...@sage/xtrem~tools@46.0.23) (2024-08-06)

### Bug Fixes

### Features
* XT-76250 Workflow ([XT-76250](https://jira.sage.com/browse/XT-76250)) ([#16287](https://github.com/Sage-ERP-X3/xtrem/issues/16287))   ([1d756ab](https://github.com/Sage-ERP-X3/xtrem/commit/1d756ab8ebbebdf7dad1b15e30dd129efffcfd25))


## [46.0.22](https://github.com/compare/...@sage/xtrem~tools@46.0.22) (2024-08-05)

### Bug Fixes

### Features


## [46.0.21](https://github.com/compare/...@sage/xtrem~tools@46.0.21) (2024-08-05)

### Bug Fixes

### Features


## [46.0.20](https://github.com/compare/...@sage/xtrem~tools@46.0.20) (2024-08-04)

### Bug Fixes

### Features


## [46.0.19](https://github.com/compare/...@sage/xtrem~tools@46.0.19) (2024-08-03)

### Bug Fixes

### Features


## [46.0.18](https://github.com/compare/...@sage/xtrem~tools@46.0.18) (2024-08-02)

### Bug Fixes

### Features


## [46.0.17](https://github.com/compare/...@sage/xtrem~tools@46.0.17) (2024-08-02)

### Bug Fixes

### Features


## [46.0.16](https://github.com/compare/...@sage/xtrem~tools@46.0.16) (2024-07-31)

### Bug Fixes

### Features


## [46.0.15](https://github.com/compare/...@sage/xtrem~tools@46.0.15) (2024-07-31)

### Bug Fixes

### Features


## [46.0.14](https://github.com/compare/...@sage/xtrem~tools@46.0.14) (2024-07-30)

### Bug Fixes

### Features
* **xtrem-cli-atp:** XT-74594 allure report local reporting ([XT-74594](https://jira.sage.com/browse/XT-74594)) ([#20060](https://github.com/Sage-ERP-X3/xtrem/issues/20060))   ([136b2cf](https://github.com/Sage-ERP-X3/xtrem/commit/136b2cf1866102aa2422fa23932b398046b56452))


## [46.0.13](https://github.com/compare/...@sage/xtrem~tools@46.0.13) (2024-07-29)

### Bug Fixes

### Features


## [46.0.12](https://github.com/compare/...@sage/xtrem~tools@46.0.12) (2024-07-28)

### Bug Fixes

### Features


## [46.0.11](https://github.com/compare/...@sage/xtrem~tools@46.0.11) (2024-07-27)

### Bug Fixes

### Features


## [46.0.10](https://github.com/compare/...@sage/xtrem~tools@46.0.10) (2024-07-26)

### Bug Fixes

### Features


## [46.0.9](https://github.com/compare/...@sage/xtrem~tools@46.0.9) (2024-07-25)

### Bug Fixes

### Features


## [46.0.8](https://github.com/compare/...@sage/xtrem~tools@46.0.8) (2024-07-24)

### Bug Fixes

### Features
* **shopfloor:** XT-74347 enum mapping page ([XT-74347](https://jira.sage.com/browse/XT-74347)) ([#19831](https://github.com/Sage-ERP-X3/xtrem/issues/19831))   ([7d8365d](https://github.com/Sage-ERP-X3/xtrem/commit/7d8365d85860e8f1afd98b7dc7b1100f18d21552))


## [46.0.7](https://github.com/compare/...@sage/xtrem~tools@46.0.7) (2024-07-23)

### Bug Fixes

### Features


## [46.0.6](https://github.com/compare/...@sage/xtrem~tools@46.0.6) (2024-07-22)

### Bug Fixes

### Features


## [46.0.5](https://github.com/compare/...@sage/xtrem~tools@46.0.5) (2024-07-21)

### Bug Fixes

### Features


## [46.0.4](https://github.com/compare/...@sage/xtrem~tools@46.0.4) (2024-07-20)

### Bug Fixes

### Features


## [46.0.3](https://github.com/compare/...@sage/xtrem~tools@46.0.3) (2024-07-19)

### Bug Fixes

### Features


## [46.0.2](https://github.com/compare/...@sage/xtrem~tools@46.0.2) (2024-07-18)

### Bug Fixes

### Features


## [46.0.1](https://github.com/compare/...@sage/xtrem~tools@46.0.1) (2024-07-18)

### Bug Fixes

### Features


## [46.0.0](https://github.com/compare/...@sage/xtrem~tools@46.0.0) (2024-07-18)

### Bug Fixes

### Features


## [45.0.30](https://github.com/compare/...@sage/xtrem~tools@45.0.30) (2024-07-17)

### Bug Fixes

### Features


## [45.0.29](https://github.com/compare/...@sage/xtrem~tools@45.0.29) (2024-07-16)

### Bug Fixes

### Features


## [45.0.28](https://github.com/compare/...@sage/xtrem~tools@45.0.28) (2024-07-15)

### Bug Fixes

### Features


## [45.0.27](https://github.com/compare/...@sage/xtrem~tools@45.0.27) (2024-07-15)

### Bug Fixes

### Features


## [45.0.26](https://github.com/compare/...@sage/xtrem~tools@45.0.26) (2024-07-14)

### Bug Fixes

### Features


## [45.0.25](https://github.com/compare/...@sage/xtrem~tools@45.0.25) (2024-07-13)

### Bug Fixes

### Features


## [45.0.24](https://github.com/compare/...@sage/xtrem~tools@45.0.24) (2024-07-12)

### Bug Fixes

### Features
* XT-72634 extend node structure to implement mapping ([XT-72634](https://jira.sage.com/browse/XT-72634)) ([#19794](https://github.com/Sage-ERP-X3/xtrem/issues/19794))   ([49588d9](https://github.com/Sage-ERP-X3/xtrem/commit/49588d94bcc055a30bf20be80309c2ea0690ac30))


## [45.0.23](https://github.com/compare/...@sage/xtrem~tools@45.0.23) (2024-07-11)

### Bug Fixes

### Features


## [45.0.22](https://github.com/compare/...@sage/xtrem~tools@45.0.22) (2024-07-10)

### Bug Fixes

### Features


## [45.0.21](https://github.com/compare/...@sage/xtrem~tools@45.0.21) (2024-07-09)

### Bug Fixes

### Features


## [45.0.20](https://github.com/compare/...@sage/xtrem~tools@45.0.20) (2024-07-08)

### Bug Fixes

### Features


## [45.0.19](https://github.com/compare/...@sage/xtrem~tools@45.0.19) (2024-07-07)

### Bug Fixes

### Features


## [45.0.18](https://github.com/compare/...@sage/xtrem~tools@45.0.18) (2024-07-06)

### Bug Fixes

### Features


## [45.0.17](https://github.com/compare/...@sage/xtrem~tools@45.0.17) (2024-07-05)

### Bug Fixes

### Features


## [45.0.16](https://github.com/compare/...@sage/xtrem~tools@45.0.16) (2024-07-04)

### Bug Fixes

### Features
* **queues:** sort routing to avoid diff XT-72346 ([XT-72346](https://jira.sage.com/browse/XT-72346)) ([#19718](https://github.com/Sage-ERP-X3/xtrem/issues/19718))   ([6871c73](https://github.com/Sage-ERP-X3/xtrem/commit/6871c73b2e6e1124e1036467320619a3b0cad41a))
* XT-69536 generate api.d.ts files with _factory property ([XT-69536](https://jira.sage.com/browse/XT-69536)) ([#19715](https://github.com/Sage-ERP-X3/xtrem/issues/19715))   ([be8ec04](https://github.com/Sage-ERP-X3/xtrem/commit/be8ec04c82b99358c72487dfc609f9dcd4a88f16))


## [45.0.15](https://github.com/compare/...@sage/xtrem~tools@45.0.15) (2024-07-03)

### Bug Fixes

### Features
* **queues:** regen queue conf XT-72236 ([XT-72236](https://jira.sage.com/browse/XT-72236)) ([#19677](https://github.com/Sage-ERP-X3/xtrem/issues/19677))   ([c530021](https://github.com/Sage-ERP-X3/xtrem/commit/c53002183312eb6983c30cfdebe68d007f80743d))


## [45.0.14](https://github.com/compare/...@sage/xtrem~tools@45.0.14) (2024-07-02)

### Bug Fixes

### Features


## [45.0.13](https://github.com/compare/...@sage/xtrem~tools@45.0.13) (2024-07-01)

### Bug Fixes

### Features


## [45.0.12](https://github.com/compare/...@sage/xtrem~tools@45.0.12) (2024-06-30)

### Bug Fixes

### Features


## [45.0.11](https://github.com/compare/...@sage/xtrem~tools@45.0.11) (2024-06-29)

### Bug Fixes

### Features


## [45.0.10](https://github.com/compare/...@sage/xtrem~tools@45.0.10) (2024-06-28)

### Bug Fixes

### Features


## [45.0.9](https://github.com/compare/...@sage/xtrem~tools@45.0.9) (2024-06-27)

### Bug Fixes

### Features


## [45.0.8](https://github.com/compare/...@sage/xtrem~tools@45.0.8) (2024-06-26)

### Bug Fixes

### Features


## [45.0.7](https://github.com/compare/...@sage/xtrem~tools@45.0.7) (2024-06-25)

### Bug Fixes

### Features


## [45.0.6](https://github.com/compare/...@sage/xtrem~tools@45.0.6) (2024-06-24)

### Bug Fixes

### Features


## [45.0.5](https://github.com/compare/...@sage/xtrem~tools@45.0.5) (2024-06-23)

### Bug Fixes

### Features


## [45.0.4](https://github.com/compare/...@sage/xtrem~tools@45.0.4) (2024-06-22)

### Bug Fixes

### Features


## [45.0.3](https://github.com/compare/...@sage/xtrem~tools@45.0.3) (2024-06-21)

### Bug Fixes

### Features


## [45.0.2](https://github.com/compare/...@sage/xtrem~tools@45.0.2) (2024-06-20)

### Bug Fixes

### Features


## [45.0.1](https://github.com/compare/...@sage/xtrem~tools@45.0.1) (2024-06-20)

### Bug Fixes

### Features


## [45.0.0](https://github.com/compare/...@sage/xtrem~tools@45.0.0) (2024-06-20)

### Bug Fixes

### Features


## [44.0.31](https://github.com/compare/...@sage/xtrem~tools@44.0.31) (2024-06-19)

### Bug Fixes

### Features
* XT-72289 dataType metadata and clean up ([XT-72289](https://jira.sage.com/browse/XT-72289)) ([#19495](https://github.com/Sage-ERP-X3/xtrem/issues/19495))   ([c22242c](https://github.com/Sage-ERP-X3/xtrem/commit/c22242c51fa318de6a43b873cc530c06b35da327))


## [44.0.30](https://github.com/compare/...@sage/xtrem~tools@44.0.30) (2024-06-18)

### Bug Fixes

### Features


## [44.0.29](https://github.com/compare/...@sage/xtrem~tools@44.0.29) (2024-06-18)

### Bug Fixes

### Features


## [44.0.28](https://github.com/compare/...@sage/xtrem~tools@44.0.28) (2024-06-17)

### Bug Fixes

### Features


## [44.0.27](https://github.com/compare/...@sage/xtrem~tools@44.0.27) (2024-06-16)

### Bug Fixes

### Features


## [44.0.26](https://github.com/compare/...@sage/xtrem~tools@44.0.26) (2024-06-15)

### Bug Fixes

### Features


## [44.0.25](https://github.com/compare/...@sage/xtrem~tools@44.0.25) (2024-06-14)

### Bug Fixes

### Features


## [44.0.24](https://github.com/compare/...@sage/xtrem~tools@44.0.24) (2024-06-13)

### Bug Fixes

### Features


## [44.0.23](https://github.com/compare/...@sage/xtrem~tools@44.0.23) (2024-06-12)

### Bug Fixes

### Features


## [44.0.22](https://github.com/compare/...@sage/xtrem~tools@44.0.22) (2024-06-11)

### Bug Fixes

### Features


## [44.0.21](https://github.com/compare/...@sage/xtrem~tools@44.0.21) (2024-06-10)

### Bug Fixes

### Features


## [44.0.20](https://github.com/compare/...@sage/xtrem~tools@44.0.20) (2024-06-09)

### Bug Fixes

### Features


## [44.0.19](https://github.com/compare/...@sage/xtrem~tools@44.0.19) (2024-06-08)

### Bug Fixes

### Features


## [44.0.18](https://github.com/compare/...@sage/xtrem~tools@44.0.18) (2024-06-07)

### Bug Fixes

### Features


## [44.0.17](https://github.com/compare/...@sage/xtrem~tools@44.0.17) (2024-06-06)

### Bug Fixes

### Features


## [44.0.16](https://github.com/compare/...@sage/xtrem~tools@44.0.16) (2024-06-05)

### Bug Fixes

### Features


## [44.0.15](https://github.com/compare/...@sage/xtrem~tools@44.0.15) (2024-06-04)

### Bug Fixes

### Features


## [44.0.14](https://github.com/compare/...@sage/xtrem~tools@44.0.14) (2024-06-03)

### Bug Fixes

### Features
* changelog page   ([#19259](https://github.com/Sage-ERP-X3/xtrem/issues/19259))   ([18a49bd](https://github.com/Sage-ERP-X3/xtrem/commit/18a49bdd72f45e23dfc6875d1bdfd6f75188c6cb))


## [44.0.13](https://github.com/compare/...@sage/xtrem~tools@44.0.13) (2024-06-02)

### Bug Fixes

### Features


## [44.0.12](https://github.com/compare/...@sage/xtrem~tools@44.0.12) (2024-06-01)

### Bug Fixes

### Features


## [44.0.11](https://github.com/compare/...@sage/xtrem~tools@44.0.11) (2024-05-31)

### Bug Fixes

### Features


## [44.0.10](https://github.com/compare/...@sage/xtrem~tools@44.0.10) (2024-05-30)

### Bug Fixes

### Features
* adds order page XT-61569 ([XT-61569](https://jira.sage.com/browse/XT-61569)) ([#19015](https://github.com/Sage-ERP-X3/xtrem/issues/19015))   ([e7b908f](https://github.com/Sage-ERP-X3/xtrem/commit/e7b908f31ee1c535e1f8e08a314a4541ec995a08))


## [44.0.9](https://github.com/compare/...@sage/xtrem~tools@44.0.9) (2024-05-30)

### Bug Fixes

### Features


## [44.0.8](https://github.com/compare/...@sage/xtrem~tools@44.0.8) (2024-05-29)

### Bug Fixes

### Features


## [44.0.7](https://github.com/compare/...@sage/xtrem~tools@44.0.7) (2024-05-29)

### Bug Fixes

### Features


## [44.0.6](https://github.com/compare/...@sage/xtrem~tools@44.0.6) (2024-05-27)

### Bug Fixes

### Features


## [44.0.5](https://github.com/compare/...@sage/xtrem~tools@44.0.5) (2024-05-26)

### Bug Fixes

### Features


## [44.0.4](https://github.com/compare/...@sage/xtrem~tools@44.0.4) (2024-05-25)

### Bug Fixes

### Features


## [44.0.3](https://github.com/compare/...@sage/xtrem~tools@44.0.3) (2024-05-24)

### Bug Fixes

### Features


## [44.0.2](https://github.com/compare/...@sage/xtrem~tools@44.0.2) (2024-05-23)

### Bug Fixes

### Features


## [44.0.1](https://github.com/compare/...@sage/xtrem~tools@44.0.1) (2024-05-23)

### Bug Fixes

### Features


## [44.0.0](https://github.com/compare/...@sage/xtrem~tools@44.0.0) (2024-05-23)

### Bug Fixes

### Features


## [43.0.34](https://github.com/compare/...@sage/xtrem~tools@43.0.34) (2024-05-22)

### Bug Fixes

### Features
* **xtrem-System:** XT-63636 System authorization refactoring ([XT-63636](https://jira.sage.com/browse/XT-63636)) ([#18930](https://github.com/Sage-ERP-X3/xtrem/issues/18930))   ([5fb5982](https://github.com/Sage-ERP-X3/xtrem/commit/5fb59822828e6ed8db1f5c67b1024b86996e6521))
* **xtrem-Authorization:** XT-63280 Authorization refactoring ([XT-63280](https://jira.sage.com/browse/XT-63280)) ([#18927](https://github.com/Sage-ERP-X3/xtrem/issues/18927))   ([52f8857](https://github.com/Sage-ERP-X3/xtrem/commit/52f8857ea898506006d4fa06c947350efe92db9f))


## [43.0.33](https://github.com/compare/...@sage/xtrem~tools@43.0.33) (2024-05-21)

### Bug Fixes

### Features
* page fragments   ([#17857](https://github.com/Sage-ERP-X3/xtrem/issues/17857))   ([d0ab9bd](https://github.com/Sage-ERP-X3/xtrem/commit/d0ab9bdf858079bd649dde9f09082d36a5f7ee57))


## [43.0.32](https://github.com/compare/...@sage/xtrem~tools@43.0.32) (2024-05-20)

### Bug Fixes

### Features


## [43.0.31](https://github.com/compare/...@sage/xtrem~tools@43.0.31) (2024-05-19)

### Bug Fixes

### Features


## [43.0.30](https://github.com/compare/...@sage/xtrem~tools@43.0.30) (2024-05-18)

### Bug Fixes

### Features


## [43.0.29](https://github.com/compare/...@sage/xtrem~tools@43.0.29) (2024-05-17)

### Bug Fixes

### Features
* XT-70071 interop enum mapping ([XT-70071](https://jira.sage.com/browse/XT-70071)) ([#19072](https://github.com/Sage-ERP-X3/xtrem/issues/19072))   ([923d3c8](https://github.com/Sage-ERP-X3/xtrem/commit/923d3c8b7e86de7c8784ca81c28840694db38ac3))


## [43.0.28](https://github.com/compare/...@sage/xtrem~tools@43.0.28) (2024-05-16)

### Bug Fixes

### Features


## [43.0.27](https://github.com/compare/...@sage/xtrem~tools@43.0.27) (2024-05-15)

### Bug Fixes

### Features


## [43.0.26](https://github.com/compare/...@sage/xtrem~tools@43.0.26) (2024-05-14)

### Bug Fixes

### Features


## [43.0.25](https://github.com/compare/...@sage/xtrem~tools@43.0.25) (2024-05-13)

### Bug Fixes

### Features


## [43.0.24](https://github.com/compare/...@sage/xtrem~tools@43.0.24) (2024-05-12)

### Bug Fixes

### Features


## [43.0.23](https://github.com/compare/...@sage/xtrem~tools@43.0.23) (2024-05-11)

### Bug Fixes

### Features


## [43.0.22](https://github.com/compare/...@sage/xtrem~tools@43.0.22) (2024-05-10)

### Bug Fixes

### Features


## [43.0.21](https://github.com/compare/...@sage/xtrem~tools@43.0.21) (2024-05-09)

### Bug Fixes

### Features


## [43.0.20](https://github.com/compare/...@sage/xtrem~tools@43.0.20) (2024-05-08)

### Bug Fixes

### Features


## [43.0.19](https://github.com/compare/...@sage/xtrem~tools@43.0.19) (2024-05-07)

### Bug Fixes

### Features


## [43.0.18](https://github.com/compare/...@sage/xtrem~tools@43.0.18) (2024-05-06)

### Bug Fixes

### Features


## [43.0.17](https://github.com/compare/...@sage/xtrem~tools@43.0.17) (2024-05-06)

### Bug Fixes

### Features


## [43.0.16](https://github.com/compare/...@sage/xtrem~tools@43.0.16) (2024-05-02)

### Bug Fixes

### Features


## [43.0.15](https://github.com/compare/...@sage/xtrem~tools@43.0.15) (2024-05-01)

### Bug Fixes

### Features


## [43.0.14](https://github.com/compare/...@sage/xtrem~tools@43.0.14) (2024-04-30)

### Bug Fixes

### Features
* XT-69683 move SysApp node to xtrem-interop ([XT-69683](https://jira.sage.com/browse/XT-69683)) ([#18772](https://github.com/Sage-ERP-X3/xtrem/issues/18772))   ([0fc4115](https://github.com/Sage-ERP-X3/xtrem/commit/0fc4115447c9364c2fdd1e8444f14587d1e84118))


## [43.0.13](https://github.com/compare/...@sage/xtrem~tools@43.0.13) (2024-04-29)

### Bug Fixes

### Features


## [43.0.12](https://github.com/compare/...@sage/xtrem~tools@43.0.12) (2024-04-28)

### Bug Fixes

### Features


## [43.0.11](https://github.com/compare/...@sage/xtrem~tools@43.0.11) (2024-04-27)

### Bug Fixes

### Features


## [43.0.10](https://github.com/compare/...@sage/xtrem~tools@43.0.10) (2024-04-26)

### Bug Fixes

### Features
* **load-data:** ignore 777777777777777777777 directory XT-99999 ([XT-99999](https://jira.sage.com/browse/XT-99999)) ([#18733](https://github.com/Sage-ERP-X3/xtrem/issues/18733))   ([cc5ef47](https://github.com/Sage-ERP-X3/xtrem/commit/cc5ef472cd9eb699f7998570267415f84462afd4))


## [43.0.9](https://github.com/compare/...@sage/xtrem~tools@43.0.9) (2024-04-25)

### Bug Fixes

### Features


## [43.0.8](https://github.com/compare/...@sage/xtrem~tools@43.0.8) (2024-04-24)

### Bug Fixes

### Features


## [43.0.7](https://github.com/compare/...@sage/xtrem~tools@43.0.7) (2024-04-23)

### Bug Fixes

### Features


## [43.0.6](https://github.com/compare/...@sage/xtrem~tools@43.0.6) (2024-04-22)

### Bug Fixes

### Features


## [43.0.5](https://github.com/compare/...@sage/xtrem~tools@43.0.5) (2024-04-21)

### Bug Fixes

### Features


## [43.0.4](https://github.com/compare/...@sage/xtrem~tools@43.0.4) (2024-04-20)

### Bug Fixes

### Features


## [43.0.3](https://github.com/compare/...@sage/xtrem~tools@43.0.3) (2024-04-19)

### Bug Fixes

### Features
* store client settings on the server   ([#18599](https://github.com/Sage-ERP-X3/xtrem/issues/18599))   ([ac27cac](https://github.com/Sage-ERP-X3/xtrem/commit/ac27cac958118119ee9c9362628da90ab4a5e37c))


## [43.0.2](https://github.com/compare/...@sage/xtrem~tools@43.0.2) (2024-04-18)

### Bug Fixes
* XT-69422 add files attribute to all main packages package.json ([XT-69422](https://jira.sage.com/browse/XT-69422)) ([#18595](https://github.com/Sage-ERP-X3/xtrem/issues/18595))   ([92f4833](https://github.com/Sage-ERP-X3/xtrem/commit/92f483369b1280bf9e9f48fe1f22d97823693fd0))

### Features


## [43.0.1](https://github.com/compare/...@sage/xtrem~tools@43.0.1) (2024-04-18)

### Bug Fixes

### Features


## [43.0.0](https://github.com/compare/...@sage/xtrem~tools@43.0.0) (2024-04-18)

### Bug Fixes

### Features


## [42.0.36](https://github.com/compare/...@sage/xtrem~tools@42.0.36) (2024-04-17)

### Bug Fixes

### Features


## [42.0.35](https://github.com/compare/...@sage/xtrem~tools@42.0.35) (2024-04-16)

### Bug Fixes
* XT-68376 fix i18n and implement check if parameters are correctly declared ([XT-68376](https://jira.sage.com/browse/XT-68376)) ([#18497](https://github.com/Sage-ERP-X3/xtrem/issues/18497))   ([6d10369](https://github.com/Sage-ERP-X3/xtrem/commit/6d10369c4c85df65fdaa4b49cd392deb23903f9f))

### Features


## [42.0.34](https://github.com/compare/...@sage/xtrem~tools@42.0.34) (2024-04-15)

### Bug Fixes

### Features


## [42.0.33](https://github.com/compare/...@sage/xtrem~tools@42.0.33) (2024-04-14)

### Bug Fixes

### Features


## [42.0.32](https://github.com/compare/...@sage/xtrem~tools@42.0.32) (2024-04-13)

### Bug Fixes

### Features


## [42.0.31](https://github.com/compare/...@sage/xtrem~tools@42.0.31) (2024-04-12)

### Bug Fixes

### Features
* **attachment:** add test data loader XT-68487 ([XT-68487](https://jira.sage.com/browse/XT-68487)) ([#18417](https://github.com/Sage-ERP-X3/xtrem/issues/18417))   ([8232870](https://github.com/Sage-ERP-X3/xtrem/commit/823287082d89e9cdbbafecf06db2f39d8763a69c))


## [42.0.30](https://github.com/compare/...@sage/xtrem~tools@42.0.30) (2024-04-11)

### Bug Fixes

### Features


## [42.0.29](https://github.com/compare/...@sage/xtrem~tools@42.0.29) (2024-04-10)

### Bug Fixes

### Features


## [42.0.28](https://github.com/compare/...@sage/xtrem~tools@42.0.28) (2024-04-09)

### Bug Fixes

### Features


## [42.0.27](https://github.com/compare/...@sage/xtrem~tools@42.0.27) (2024-04-08)

### Bug Fixes

### Features


## [42.0.26](https://github.com/compare/...@sage/xtrem~tools@42.0.26) (2024-04-07)

### Bug Fixes

### Features


## [42.0.25](https://github.com/compare/...@sage/xtrem~tools@42.0.25) (2024-04-06)

### Bug Fixes

### Features


## [42.0.24](https://github.com/compare/...@sage/xtrem~tools@42.0.24) (2024-04-05)

### Bug Fixes

### Features


## [42.0.23](https://github.com/compare/...@sage/xtrem~tools@42.0.23) (2024-04-04)

### Bug Fixes

### Features


## [42.0.22](https://github.com/compare/...@sage/xtrem~tools@42.0.22) (2024-04-03)

### Bug Fixes

### Features


## [42.0.21](https://github.com/compare/...@sage/xtrem~tools@42.0.21) (2024-04-02)

### Bug Fixes

### Features


## [42.0.20](https://github.com/compare/...@sage/xtrem~tools@42.0.20) (2024-04-01)

### Bug Fixes

### Features


## [42.0.19](https://github.com/compare/...@sage/xtrem~tools@42.0.19) (2024-03-31)

### Bug Fixes

### Features


## [42.0.18](https://github.com/compare/...@sage/xtrem~tools@42.0.18) (2024-03-30)

### Bug Fixes

### Features


## [42.0.17](https://github.com/compare/...@sage/xtrem~tools@42.0.17) (2024-03-29)

### Bug Fixes

### Features


## [42.0.16](https://github.com/compare/...@sage/xtrem~tools@42.0.16) (2024-03-28)

### Bug Fixes
* XT-67974 add asyncExport params i18n ([XT-67974](https://jira.sage.com/browse/XT-67974)) ([#18206](https://github.com/Sage-ERP-X3/xtrem/issues/18206))   ([69ef7ec](https://github.com/Sage-ERP-X3/xtrem/commit/69ef7ec0d7761473fa058ddb9a4fc46dd961d21a))

### Features


## [42.0.15](https://github.com/compare/...@sage/xtrem~tools@42.0.15) (2024-03-27)

### Bug Fixes

### Features


## [42.0.14](https://github.com/compare/...@sage/xtrem~tools@42.0.14) (2024-03-26)

### Bug Fixes

### Features


## [42.0.13](https://github.com/compare/...@sage/xtrem~tools@42.0.13) (2024-03-25)

### Bug Fixes
* XT-99999 Fix issue with imports ([XT-99999](https://jira.sage.com/browse/XT-99999)) ([#18130](https://github.com/Sage-ERP-X3/xtrem/issues/18130))   ([3076f1a](https://github.com/Sage-ERP-X3/xtrem/commit/3076f1ad800410071c8014a6a5b5da7ac9b1b139))

### Features


## [42.0.12](https://github.com/compare/...@sage/xtrem~tools@42.0.12) (2024-03-24)

### Bug Fixes

### Features


## [42.0.11](https://github.com/compare/...@sage/xtrem~tools@42.0.11) (2024-03-23)

### Bug Fixes

### Features


## [42.0.10](https://github.com/compare/...@sage/xtrem~tools@42.0.10) (2024-03-22)

### Bug Fixes

### Features


## [42.0.9](https://github.com/compare/...@sage/xtrem~tools@42.0.9) (2024-03-21)

### Bug Fixes

### Features


## [42.0.8](https://github.com/compare/...@sage/xtrem~tools@42.0.8) (2024-03-20)

### Bug Fixes

### Features


## [42.0.7](https://github.com/compare/...@sage/xtrem~tools@42.0.7) (2024-03-19)

### Bug Fixes

### Features


## [42.0.6](https://github.com/compare/...@sage/xtrem~tools@42.0.6) (2024-03-18)

### Bug Fixes

### Features


## [42.0.5](https://github.com/compare/...@sage/xtrem~tools@42.0.5) (2024-03-17)

### Bug Fixes

### Features


## [42.0.4](https://github.com/compare/...@sage/xtrem~tools@42.0.4) (2024-03-16)

### Bug Fixes

### Features


## [42.0.3](https://github.com/compare/...@sage/xtrem~tools@42.0.3) (2024-03-15)

### Bug Fixes

### Features


## [42.0.2](https://github.com/compare/...@sage/xtrem~tools@42.0.2) (2024-03-14)

### Bug Fixes

### Features


## [42.0.1](https://github.com/compare/...@sage/xtrem~tools@42.0.1) (2024-03-14)

### Bug Fixes

### Features


## [42.0.0](https://github.com/compare/...@sage/xtrem~tools@42.0.0) (2024-03-14)

### Bug Fixes

### Features


## [41.0.25](https://github.com/compare/...@sage/xtrem~tools@41.0.25) (2024-03-13)

### Bug Fixes

### Features


## [41.0.24](https://github.com/compare/...@sage/xtrem~tools@41.0.24) (2024-03-12)

### Bug Fixes

### Features


## [41.0.23](https://github.com/compare/...@sage/xtrem~tools@41.0.23) (2024-03-11)

### Bug Fixes

### Features


## [41.0.22](https://github.com/compare/...@sage/xtrem~tools@41.0.22) (2024-03-11)

### Bug Fixes

### Features


## [41.0.21](https://github.com/compare/...@sage/xtrem~tools@41.0.21) (2024-03-10)

### Bug Fixes

### Features


## [41.0.20](https://github.com/compare/...@sage/xtrem~tools@41.0.20) (2024-03-09)

### Bug Fixes

### Features


## [41.0.19](https://github.com/compare/...@sage/xtrem~tools@41.0.19) (2024-03-08)

### Bug Fixes

### Features


## [41.0.18](https://github.com/compare/...@sage/xtrem~tools@41.0.18) (2024-03-07)

### Bug Fixes

### Features


## [41.0.17](https://github.com/compare/...@sage/xtrem~tools@41.0.17) (2024-03-07)

### Bug Fixes

### Features


## [41.0.16](https://github.com/compare/...@sage/xtrem~tools@41.0.16) (2024-03-06)

### Bug Fixes

### Features
* XT-66624 improved the synchronization transform page ([XT-66624](https://jira.sage.com/browse/XT-66624)) ([#17655](https://github.com/Sage-ERP-X3/xtrem/issues/17655))   ([81bbc1e](https://github.com/Sage-ERP-X3/xtrem/commit/81bbc1e1e24867911a75aa19fe3eadffd4a76a17))
* XT-64328 attachment access rights ([XT-64328](https://jira.sage.com/browse/XT-64328)) ([#17638](https://github.com/Sage-ERP-X3/xtrem/issues/17638))   ([da50c08](https://github.com/Sage-ERP-X3/xtrem/commit/da50c0812de182f544b17526f6e84e196be8ae67))


## [41.0.15](https://github.com/compare/...@sage/xtrem~tools@41.0.15) (2024-03-05)

### Bug Fixes

### Features


## [41.0.14](https://github.com/compare/...@sage/xtrem~tools@41.0.14) (2024-03-04)

### Bug Fixes

### Features
* **xtrem-cli-atp:** XT-66435 cucumber feature auto formatting ([XT-66435](https://jira.sage.com/browse/XT-66435)) ([#17538](https://github.com/Sage-ERP-X3/xtrem/issues/17538))   ([bb65ceb](https://github.com/Sage-ERP-X3/xtrem/commit/bb65cebb1b42c596cfc16ed48c941a48137d3569))


## [41.0.13](https://github.com/compare/...@sage/xtrem~tools@41.0.13) (2024-03-03)

### Bug Fixes

### Features


## [41.0.12](https://github.com/compare/...@sage/xtrem~tools@41.0.12) (2024-03-02)

### Bug Fixes

### Features


## [41.0.11](https://github.com/compare/...@sage/xtrem~tools@41.0.11) (2024-03-01)

### Bug Fixes

### Features
* XT-66120 synchronize showcase apps ([XT-66120](https://jira.sage.com/browse/XT-66120)) ([#17449](https://github.com/Sage-ERP-X3/xtrem/issues/17449))   ([e408cfe](https://github.com/Sage-ERP-X3/xtrem/commit/e408cfe41d14c8433c3d9003c97854d6ec8f2dd9))


## [41.0.10](https://github.com/compare/...@sage/xtrem~tools@41.0.10) (2024-02-29)

### Bug Fixes

### Features


## [41.0.9](https://github.com/compare/...@sage/xtrem~tools@41.0.9) (2024-02-29)

### Bug Fixes

### Features
* **attachment:** garbage collector XT-64288 ([XT-64288](https://jira.sage.com/browse/XT-64288)) ([#17313](https://github.com/Sage-ERP-X3/xtrem/issues/17313))   ([d5f284a](https://github.com/Sage-ERP-X3/xtrem/commit/d5f284a4ebe9fafc45eacc0aaceef75321baf410))


## [41.0.8](https://github.com/compare/...@sage/xtrem~tools@41.0.8) (2024-02-28)

### Bug Fixes

### Features


## [41.0.7](https://github.com/compare/...@sage/xtrem~tools@41.0.7) (2024-02-27)

### Bug Fixes

### Features
* XT-66139 adapt generation of elasticmq.conf files for biz apps ([XT-66139](https://jira.sage.com/browse/XT-66139)) ([#17408](https://github.com/Sage-ERP-X3/xtrem/issues/17408))   ([fc256bc](https://github.com/Sage-ERP-X3/xtrem/commit/fc256bc3d3c1e0cd9891ec17ecead78e096701c2))
* move duplicate to server + display dialog to show mandatory properties XT-61492 ([XT-61492](https://jira.sage.com/browse/XT-61492)) ([#17320](https://github.com/Sage-ERP-X3/xtrem/issues/17320))   ([a714764](https://github.com/Sage-ERP-X3/xtrem/commit/a7147644e29ae64294e350a56bde0263f8d2b1ea))


## [41.0.6](https://github.com/compare/...@sage/xtrem~tools@41.0.6) (2024-02-26)

### Bug Fixes

### Features


## [41.0.5](https://github.com/compare/...@sage/xtrem~tools@41.0.5) (2024-02-26)

### Bug Fixes

### Features
* **businessEntity_refactoring:** authorization package   ([#17291](https://github.com/Sage-ERP-X3/xtrem/issues/17291))   ([718a6d9](https://github.com/Sage-ERP-X3/xtrem/commit/718a6d9f12922de589213cf7f538b2513a32fa5a))


## [41.0.4](https://github.com/compare/...@sage/xtrem~tools@41.0.4) (2024-02-26)

### Bug Fixes

### Features


## [41.0.3](https://github.com/compare/...@sage/xtrem~tools@41.0.3) (2024-02-23)

### Bug Fixes

### Features


## [41.0.2](https://github.com/compare/...@sage/xtrem~tools@41.0.2) (2024-02-22)

### Bug Fixes

### Features
* move duplicate to server + display dialog to show mandatory properties XT-61492 ([XT-61492](https://jira.sage.com/browse/XT-61492)) ([#16873](https://github.com/Sage-ERP-X3/xtrem/issues/16873))   ([a5b086c](https://github.com/Sage-ERP-X3/xtrem/commit/a5b086cf1f99f61fea218361dc66f36d5fc8aa36))


## [41.0.1](https://github.com/compare/...@sage/xtrem~tools@41.0.1) (2024-02-22)

### Bug Fixes

### Features


## [41.0.0](https://github.com/compare/...@sage/xtrem~tools@41.0.0) (2024-02-22)

### Bug Fixes

### Features


## [40.0.40](https://github.com/compare/...@sage/xtrem~tools@40.0.40) (2024-02-21)

### Bug Fixes

### Features


## [40.0.39](https://github.com/compare/...@sage/xtrem~tools@40.0.39) (2024-02-20)

### Bug Fixes

### Features


## [40.0.38](https://github.com/compare/...@sage/xtrem~tools@40.0.38) (2024-02-20)

### Bug Fixes

### Features


## [40.0.37](https://github.com/compare/...@sage/xtrem~tools@40.0.37) (2024-02-20)

### Bug Fixes

### Features


## [40.0.36](https://github.com/compare/...@sage/xtrem~tools@40.0.36) (2024-02-19)

### Bug Fixes

### Features


## [40.0.35](https://github.com/compare/...@sage/xtrem~tools@40.0.35) (2024-02-18)

### Bug Fixes

### Features


## [40.0.34](https://github.com/compare/...@sage/xtrem~tools@40.0.34) (2024-02-17)

### Bug Fixes

### Features


## [40.0.33](https://github.com/compare/...@sage/xtrem~tools@40.0.33) (2024-02-17)

### Bug Fixes

### Features


## [40.0.32](https://github.com/compare/...@sage/xtrem~tools@40.0.32) (2024-02-17)

### Bug Fixes

### Features


## [40.0.31](https://github.com/compare/...@sage/xtrem~tools@40.0.31) (2024-02-16)

### Bug Fixes

### Features


## [40.0.30](https://github.com/compare/...@sage/xtrem~tools@40.0.30) (2024-02-15)

### Bug Fixes

### Features


## [40.0.29](https://github.com/compare/...@sage/xtrem~tools@40.0.29) (2024-02-14)

### Bug Fixes

### Features


## [40.0.28](https://github.com/compare/...@sage/xtrem~tools@40.0.28) (2024-02-13)

### Bug Fixes

### Features


## [40.0.27](https://github.com/compare/...@sage/xtrem~tools@40.0.27) (2024-02-12)

### Bug Fixes

### Features


## [40.0.26](https://github.com/compare/...@sage/xtrem~tools@40.0.26) (2024-02-11)

### Bug Fixes

### Features


## [40.0.25](https://github.com/compare/...@sage/xtrem~tools@40.0.25) (2024-02-10)

### Bug Fixes

### Features


## [40.0.24](https://github.com/compare/...@sage/xtrem~tools@40.0.24) (2024-02-09)

### Bug Fixes

### Features


## [40.0.23](https://github.com/compare/...@sage/xtrem~tools@40.0.23) (2024-02-08)

### Bug Fixes

### Features


## [40.0.22](https://github.com/compare/...@sage/xtrem~tools@40.0.22) (2024-02-07)

### Bug Fixes

### Features


## [40.0.21](https://github.com/compare/...@sage/xtrem~tools@40.0.21) (2024-02-06)

### Bug Fixes

### Features
* XT-63829 Publish create and update user ([XT-63829](https://jira.sage.com/browse/XT-63829)) ([#16790](https://github.com/Sage-ERP-X3/xtrem/issues/16790))   ([2ce940e](https://github.com/Sage-ERP-X3/xtrem/commit/2ce940e9a21d46bb17b9315c253bb1f94a1f487a))


## [40.0.20](https://github.com/compare/...@sage/xtrem~tools@40.0.20) (2024-02-05)

### Bug Fixes

### Features


## [40.0.19](https://github.com/compare/...@sage/xtrem~tools@40.0.19) (2024-02-04)

### Bug Fixes

### Features


## [40.0.18](https://github.com/compare/...@sage/xtrem~tools@40.0.18) (2024-02-03)

### Bug Fixes

### Features


## [40.0.17](https://github.com/compare/...@sage/xtrem~tools@40.0.17) (2024-02-02)

### Bug Fixes

### Features
* **attachment:** add attachment and attachment-association nodes XT-63352 ([XT-63352](https://jira.sage.com/browse/XT-63352)) ([#16760](https://github.com/Sage-ERP-X3/xtrem/issues/16760))   ([9b35c29](https://github.com/Sage-ERP-X3/xtrem/commit/9b35c29421524f412760ebae27079212350b185a))
* X3-308753 refactor gen api to update how enum types are created in api.d.ts files ([X3-308753](https://jira.sage.com/browse/X3-308753)) ([#16808](https://github.com/Sage-ERP-X3/xtrem/issues/16808))   ([e629c04](https://github.com/Sage-ERP-X3/xtrem/commit/e629c04e5f5bc65006f175bcce4a04ec894eb204))


## [40.0.16](https://github.com/compare/...@sage/xtrem~tools@40.0.16) (2024-02-01)

### Bug Fixes

### Features


## [40.0.15](https://github.com/compare/...@sage/xtrem~tools@40.0.15) (2024-01-31)

### Bug Fixes

### Features


## [40.0.14](https://github.com/compare/...@sage/xtrem~tools@40.0.14) (2024-01-30)

### Bug Fixes

### Features


## [40.0.13](https://github.com/compare/...@sage/xtrem~tools@40.0.13) (2024-01-29)

### Bug Fixes

### Features


## [40.0.12](https://github.com/compare/...@sage/xtrem~tools@40.0.12) (2024-01-28)

### Bug Fixes

### Features


## [40.0.11](https://github.com/compare/...@sage/xtrem~tools@40.0.11) (2024-01-27)

### Bug Fixes

### Features


## [40.0.10](https://github.com/compare/...@sage/xtrem~tools@40.0.10) (2024-01-26)

### Bug Fixes

### Features


## [40.0.9](https://github.com/compare/...@sage/xtrem~tools@40.0.9) (2024-01-25)

### Bug Fixes

### Features
* loading xtrem pages and dashboard for x3 poc X3-302515 X3-288551 ([X3-302515](https://jira.sage.com/browse/X3-302515); [X3-288551](https://jira.sage.com/browse/X3-288551)) ([#15842](https://github.com/Sage-ERP-X3/xtrem/issues/15842))   ([a2e5cc3](https://github.com/Sage-ERP-X3/xtrem/commit/a2e5cc388820b8b16fa7903ef27fb67059ae9800))


## [40.0.8](https://github.com/compare/...@sage/xtrem~tools@40.0.8) (2024-01-24)

### Bug Fixes

### Features


## [40.0.7](https://github.com/compare/...@sage/xtrem~tools@40.0.7) (2024-01-23)

### Bug Fixes

### Features


## [40.0.6](https://github.com/compare/...@sage/xtrem~tools@40.0.6) (2024-01-22)

### Bug Fixes

### Features


## [40.0.5](https://github.com/compare/...@sage/xtrem~tools@40.0.5) (2024-01-21)

### Bug Fixes

### Features


## [40.0.4](https://github.com/compare/...@sage/xtrem~tools@40.0.4) (2024-01-20)

### Bug Fixes

### Features


## [40.0.3](https://github.com/compare/...@sage/xtrem~tools@40.0.3) (2024-01-19)

### Bug Fixes

### Features


## [40.0.2](https://github.com/compare/...@sage/xtrem~tools@40.0.2) (2024-01-18)

### Bug Fixes

### Features


## [40.0.1](https://github.com/compare/...@sage/xtrem~tools@40.0.1) (2024-01-18)

### Bug Fixes

### Features


## [40.0.0](https://github.com/compare/...@sage/xtrem~tools@40.0.0) (2024-01-18)

### Bug Fixes

### Features


## [39.0.31](https://github.com/compare/...@sage/xtrem~tools@39.0.31) (2024-01-17)

### Bug Fixes

### Features


## [39.0.30](https://github.com/compare/...@sage/xtrem~tools@39.0.30) (2024-01-17)

### Bug Fixes

### Features


## [39.0.29](https://github.com/compare/...@sage/xtrem~tools@39.0.29) (2024-01-16)

### Bug Fixes

### Features


## [39.0.28](https://github.com/compare/...@sage/xtrem~tools@39.0.28) (2024-01-15)

### Bug Fixes

### Features


## [39.0.27](https://github.com/compare/...@sage/xtrem~tools@39.0.27) (2024-01-14)

### Bug Fixes

### Features


## [39.0.26](https://github.com/compare/...@sage/xtrem~tools@39.0.26) (2024-01-13)

### Bug Fixes

### Features


## [39.0.25](https://github.com/compare/...@sage/xtrem~tools@39.0.25) (2024-01-12)

### Bug Fixes

### Features
* **pipelines:** XT-62688 - Set telemetrySalt ([XT-62688](https://jira.sage.com/browse/XT-62688)) ([#16412](https://github.com/Sage-ERP-X3/xtrem/issues/16412))   ([d9f5c1d](https://github.com/Sage-ERP-X3/xtrem/commit/d9f5c1dcbe589f53b904427c8e3602c10ed751d1))


## [39.0.24](https://github.com/compare/...@sage/xtrem~tools@39.0.24) (2024-01-12)

### Bug Fixes

### Features


## [39.0.23](https://github.com/compare/...@sage/xtrem~tools@39.0.23) (2024-01-11)

### Bug Fixes

### Features


## [39.0.22](https://github.com/compare/...@sage/xtrem~tools@39.0.22) (2024-01-10)

### Bug Fixes

### Features


## [39.0.21](https://github.com/compare/...@sage/xtrem~tools@39.0.21) (2024-01-09)

### Bug Fixes

### Features


## [39.0.20](https://github.com/compare/...@sage/xtrem~tools@39.0.20) (2024-01-08)

### Bug Fixes

### Features
* redos linting XT-62323 ([XT-62323](https://jira.sage.com/browse/XT-62323)) ([#16307](https://github.com/Sage-ERP-X3/xtrem/issues/16307))   ([490fddc](https://github.com/Sage-ERP-X3/xtrem/commit/490fddc3bbfde3f07a621805c09aa38fe224703a))


## [39.0.19](https://github.com/compare/...@sage/xtrem~tools@39.0.19) (2024-01-07)

### Bug Fixes

### Features


## [39.0.18](https://github.com/compare/...@sage/xtrem~tools@39.0.18) (2024-01-06)

### Bug Fixes

### Features
* XT-62396 MetaApp node ([XT-62396](https://jira.sage.com/browse/XT-62396)) ([#16327](https://github.com/Sage-ERP-X3/xtrem/issues/16327))   ([3289854](https://github.com/Sage-ERP-X3/xtrem/commit/328985495dbb9f01459a709f241cd62915700af5))


## [39.0.17](https://github.com/compare/...@sage/xtrem~tools@39.0.17) (2024-01-05)

### Bug Fixes
* csv layers test XT-62387 ([XT-62387](https://jira.sage.com/browse/XT-62387)) ([#16326](https://github.com/Sage-ERP-X3/xtrem/issues/16326))   ([916c2c1](https://github.com/Sage-ERP-X3/xtrem/commit/916c2c1917f5b09eb6b6513edc10a4ad088c8b47))

### Features


## [39.0.16](https://github.com/compare/...@sage/xtrem~tools@39.0.16) (2024-01-04)

### Bug Fixes

### Features


## [39.0.15](https://github.com/compare/...@sage/xtrem~tools@39.0.15) (2024-01-04)

### Bug Fixes

### Features


## [39.0.14](https://github.com/compare/...@sage/xtrem~tools@39.0.14) (2024-01-02)

### Bug Fixes

### Features


## [39.0.13](https://github.com/compare/...@sage/xtrem~tools@39.0.13) (2024-01-01)

### Bug Fixes

### Features


## [39.0.12](https://github.com/compare/...@sage/xtrem~tools@39.0.12) (2023-12-31)

### Bug Fixes

### Features


## [39.0.11](https://github.com/compare/...@sage/xtrem~tools@39.0.11) (2023-12-30)

### Bug Fixes

### Features


## [39.0.10](https://github.com/compare/...@sage/xtrem~tools@39.0.10) (2023-12-29)

### Bug Fixes

### Features


## [39.0.9](https://github.com/compare/...@sage/xtrem~tools@39.0.9) (2023-12-28)

### Bug Fixes

### Features


## [39.0.8](https://github.com/compare/...@sage/xtrem~tools@39.0.8) (2023-12-27)

### Bug Fixes

### Features


## [39.0.7](https://github.com/compare/...@sage/xtrem~tools@39.0.7) (2023-12-26)

### Bug Fixes

### Features


## [39.0.6](https://github.com/compare/...@sage/xtrem~tools@39.0.6) (2023-12-25)

### Bug Fixes

### Features


## [39.0.5](https://github.com/compare/...@sage/xtrem~tools@39.0.5) (2023-12-24)

### Bug Fixes

### Features


## [39.0.4](https://github.com/compare/...@sage/xtrem~tools@39.0.4) (2023-12-23)

### Bug Fixes

### Features


## [39.0.3](https://github.com/compare/...@sage/xtrem~tools@39.0.3) (2023-12-22)

### Bug Fixes

### Features
* XT-61848 remove abstract node and add sub node to routing.json and add asyncExport to subnode ([XT-61848](https://jira.sage.com/browse/XT-61848)) ([#16199](https://github.com/Sage-ERP-X3/xtrem/issues/16199))   ([88a0371](https://github.com/Sage-ERP-X3/xtrem/commit/88a037130a4430f23a8c4e20827764a11d45f5e7))


## [39.0.2](https://github.com/compare/...@sage/xtrem~tools@39.0.2) (2023-12-21)

### Bug Fixes

### Features


## [39.0.1](https://github.com/compare/...@sage/xtrem~tools@39.0.1) (2023-12-21)

### Bug Fixes

### Features


## [39.0.0](https://github.com/compare/...@sage/xtrem~tools@39.0.0) (2023-12-21)

### Bug Fixes

### Features


## [38.0.37](https://github.com/compare/...@sage/xtrem~tools@38.0.37) (2023-12-21)

### Bug Fixes

### Features


## [38.0.36](https://github.com/compare/...@sage/xtrem~tools@38.0.36) (2023-12-20)

### Bug Fixes

### Features
* XT-56278 MetaNodeDataType -> MetaDataType renaming ([XT-56278](https://jira.sage.com/browse/XT-56278)) ([#16169](https://github.com/Sage-ERP-X3/xtrem/issues/16169))   ([8de92e5](https://github.com/Sage-ERP-X3/xtrem/commit/8de92e5971a54f4535e0f05c65da75165085fd2d))


## [38.0.35](https://github.com/compare/...@sage/xtrem~tools@38.0.35) (2023-12-20)

### Bug Fixes

### Features


## [38.0.34](https://github.com/compare/...@sage/xtrem~tools@38.0.34) (2023-12-19)

### Bug Fixes

### Features


## [38.0.33](https://github.com/compare/...@sage/xtrem~tools@38.0.33) (2023-12-18)

### Bug Fixes

### Features


## [38.0.32](https://github.com/compare/...@sage/xtrem~tools@38.0.32) (2023-12-17)

### Bug Fixes

### Features


## [38.0.31](https://github.com/compare/...@sage/xtrem~tools@38.0.31) (2023-12-16)

### Bug Fixes

### Features


## [38.0.30](https://github.com/compare/...@sage/xtrem~tools@38.0.30) (2023-12-15)

### Bug Fixes

### Features


## [38.0.29](https://github.com/compare/...@sage/xtrem~tools@38.0.29) (2023-12-15)

### Bug Fixes

### Features


## [38.0.28](https://github.com/compare/...@sage/xtrem~tools@38.0.28) (2023-12-14)

### Bug Fixes

### Features
* **system:** add custom parameters node XT-56271 ([XT-56271](https://jira.sage.com/browse/XT-56271)) ([#15643](https://github.com/Sage-ERP-X3/xtrem/issues/15643))   ([559191f](https://github.com/Sage-ERP-X3/xtrem/commit/559191fa3bacff5000e0fbea257b51d21b74953f))


## [38.0.27](https://github.com/compare/...@sage/xtrem~tools@38.0.27) (2023-12-13)

### Bug Fixes

### Features


## [38.0.26](https://github.com/compare/...@sage/xtrem~tools@38.0.26) (2023-12-13)

### Bug Fixes

### Features


## [38.0.25](https://github.com/compare/...@sage/xtrem~tools@38.0.25) (2023-12-12)

### Bug Fixes

### Features


## [38.0.24](https://github.com/compare/...@sage/xtrem~tools@38.0.24) (2023-12-12)

### Bug Fixes

### Features


## [38.0.23](https://github.com/compare/...@sage/xtrem~tools@38.0.23) (2023-12-11)

### Bug Fixes

### Features


## [38.0.22](https://github.com/compare/...@sage/xtrem~tools@38.0.22) (2023-12-11)

### Bug Fixes

### Features
* XT-59054 Custom SQL scripts ([XT-59054](https://jira.sage.com/browse/XT-59054)) ([#15947](https://github.com/Sage-ERP-X3/xtrem/issues/15947))   ([44ac1b4](https://github.com/Sage-ERP-X3/xtrem/commit/44ac1b486fb90e9fdcfcd37927afbf9fd1236234))


## [38.0.21](https://github.com/compare/...@sage/xtrem~tools@38.0.21) (2023-12-10)

### Bug Fixes

### Features


## [38.0.20](https://github.com/compare/...@sage/xtrem~tools@38.0.20) (2023-12-09)

### Bug Fixes

### Features


## [38.0.19](https://github.com/compare/...@sage/xtrem~tools@38.0.19) (2023-12-08)

### Bug Fixes

### Features


## [38.0.18](https://github.com/compare/...@sage/xtrem~tools@38.0.18) (2023-12-07)

### Bug Fixes

### Features
* **xtrem-cli-atp:** XT-59880 - Clicks step definitions refactoring ([XT-59880](https://jira.sage.com/browse/XT-59880)) ([#15711](https://github.com/Sage-ERP-X3/xtrem/issues/15711))   ([b2c17f2](https://github.com/Sage-ERP-X3/xtrem/commit/b2c17f2b111d8fd18744d1969aac5dc50da65b46))


## [38.0.17](https://github.com/compare/...@sage/xtrem~tools@38.0.17) (2023-12-06)

### Bug Fixes

### Features


## [38.0.16](https://github.com/compare/...@sage/xtrem~tools@38.0.16) (2023-12-05)

### Bug Fixes

### Features


## [38.0.15](https://github.com/compare/...@sage/xtrem~tools@38.0.15) (2023-12-04)

### Bug Fixes

### Features


## [38.0.14](https://github.com/compare/...@sage/xtrem~tools@38.0.14) (2023-12-03)

### Bug Fixes

### Features


## [38.0.13](https://github.com/compare/...@sage/xtrem~tools@38.0.13) (2023-12-02)

### Bug Fixes

### Features


## [38.0.12](https://github.com/compare/...@sage/xtrem~tools@38.0.12) (2023-12-01)

### Bug Fixes

### Features


## [38.0.11](https://github.com/compare/...@sage/xtrem~tools@38.0.11) (2023-12-01)

### Bug Fixes

### Features


## [38.0.10](https://github.com/compare/...@sage/xtrem~tools@38.0.10) (2023-11-30)

### Bug Fixes

### Features


## [38.0.9](https://github.com/compare/...@sage/xtrem~tools@38.0.9) (2023-11-30)

### Bug Fixes

### Features


## [38.0.8](https://github.com/compare/...@sage/xtrem~tools@38.0.8) (2023-11-29)

### Bug Fixes

### Features
* **custom-parameters:** add BaseParameter node XT-56271 ([XT-56271](https://jira.sage.com/browse/XT-56271)) ([#15804](https://github.com/Sage-ERP-X3/xtrem/issues/15804))   ([c591ced](https://github.com/Sage-ERP-X3/xtrem/commit/c591ced4e475e25585e80a8b28ab228885129e7e))


## [38.0.7](https://github.com/compare/...@sage/xtrem~tools@38.0.7) (2023-11-28)

### Bug Fixes

### Features


## [38.0.6](https://github.com/compare/...@sage/xtrem~tools@38.0.6) (2023-11-28)

### Bug Fixes

### Features


## [38.0.5](https://github.com/compare/...@sage/xtrem~tools@38.0.5) (2023-11-27)

### Bug Fixes

### Features


## [38.0.4](https://github.com/compare/...@sage/xtrem~tools@38.0.4) (2023-11-26)

### Bug Fixes

### Features


## [38.0.3](https://github.com/compare/...@sage/xtrem~tools@38.0.3) (2023-11-25)

### Bug Fixes

### Features


## [38.0.2](https://github.com/compare/...@sage/xtrem~tools@38.0.2) (2023-11-24)

### Bug Fixes

### Features
* **xtrem-cli-atp:** XT-57613 - Dialog step definitions refactoring ([XT-57613](https://jira.sage.com/browse/XT-57613)) ([#15651](https://github.com/Sage-ERP-X3/xtrem/issues/15651))   ([2f4cf19](https://github.com/Sage-ERP-X3/xtrem/commit/2f4cf194a803afd1ae511ecfe1a59d1fd38224b1))


## [38.0.1](https://github.com/compare/...@sage/xtrem~tools@38.0.1) (2023-11-24)

### Bug Fixes

### Features


## [38.0.0](https://github.com/compare/...@sage/xtrem~tools@38.0.0) (2023-11-23)

### Bug Fixes

### Features


## [37.0.35](https://github.com/compare/...@sage/xtrem~tools@37.0.35) (2023-11-22)

### Bug Fixes

### Features


## [37.0.34](https://github.com/compare/...@sage/xtrem~tools@37.0.34) (2023-11-22)

### Bug Fixes

### Features
* XT-56265 Make upgrader of cli-layers compatible with SQL files ([XT-56265](https://jira.sage.com/browse/XT-56265)) ([#15631](https://github.com/Sage-ERP-X3/xtrem/issues/15631))   ([fc6c748](https://github.com/Sage-ERP-X3/xtrem/commit/fc6c74888fb64ee8ac7ec289bb725562fae5b6bf))


## [37.0.33](https://github.com/compare/...@sage/xtrem~tools@37.0.33) (2023-11-20)

### Bug Fixes

### Features


## [37.0.32](https://github.com/compare/...@sage/xtrem~tools@37.0.32) (2023-11-19)

### Bug Fixes

### Features


## [37.0.31](https://github.com/compare/...@sage/xtrem~tools@37.0.31) (2023-11-18)

### Bug Fixes

### Features


## [37.0.30](https://github.com/compare/...@sage/xtrem~tools@37.0.30) (2023-11-17)

### Bug Fixes

### Features


## [37.0.29](https://github.com/compare/...@sage/xtrem~tools@37.0.29) (2023-11-16)

### Bug Fixes

### Features
* XT-55911 GraphQL lookup access security ([XT-55911](https://jira.sage.com/browse/XT-55911)) ([#15366](https://github.com/Sage-ERP-X3/xtrem/issues/15366))   ([2ccc1a9](https://github.com/Sage-ERP-X3/xtrem/commit/2ccc1a9a4318c6a46b026af9bffcdd84dfc3ae6d))


## [37.0.28](https://github.com/compare/...@sage/xtrem~tools@37.0.28) (2023-11-16)

### Bug Fixes

### Features


## [37.0.27](https://github.com/compare/...@sage/xtrem~tools@37.0.27) (2023-11-16)

### Bug Fixes

### Features


## [37.0.26](https://github.com/compare/...@sage/xtrem~tools@37.0.26) (2023-11-15)

### Bug Fixes

### Features


## [37.0.25](https://github.com/compare/...@sage/xtrem~tools@37.0.25) (2023-11-15)

### Bug Fixes

### Features


## [37.0.24](https://github.com/compare/...@sage/xtrem~tools@37.0.24) (2023-11-14)

### Bug Fixes

### Features


## [37.0.23](https://github.com/compare/...@sage/xtrem~tools@37.0.23) (2023-11-13)

### Bug Fixes

### Features


## [37.0.22](https://github.com/compare/...@sage/xtrem~tools@37.0.22) (2023-11-12)

### Bug Fixes

### Features


## [37.0.21](https://github.com/compare/...@sage/xtrem~tools@37.0.21) (2023-11-11)

### Bug Fixes

### Features


## [37.0.20](https://github.com/compare/...@sage/xtrem~tools@37.0.20) (2023-11-10)

### Bug Fixes

### Features


## [37.0.19](https://github.com/compare/...@sage/xtrem~tools@37.0.19) (2023-11-10)

### Bug Fixes

### Features


## [37.0.18](https://github.com/compare/...@sage/xtrem~tools@37.0.18) (2023-11-10)

### Bug Fixes

### Features


## [37.0.17](https://github.com/compare/...@sage/xtrem~tools@37.0.17) (2023-11-09)

### Bug Fixes

### Features
* XT-56625 reformat SQL files ([XT-56625](https://jira.sage.com/browse/XT-56625)) ([#15501](https://github.com/Sage-ERP-X3/xtrem/issues/15501))   ([d704877](https://github.com/Sage-ERP-X3/xtrem/commit/d704877375976b973163ce4d9cd5f81522622c91))


## [37.0.16](https://github.com/compare/...@sage/xtrem~tools@37.0.16) (2023-11-08)

### Bug Fixes
* XT-55228 remove unnecessary import-data node ([XT-55228](https://jira.sage.com/browse/XT-55228)) ([#15475](https://github.com/Sage-ERP-X3/xtrem/issues/15475))   ([023103e](https://github.com/Sage-ERP-X3/xtrem/commit/023103e6e7e5b68fb840c76b5ebfd2e45eec7ee2))

### Features


## [37.0.15](https://github.com/compare/...@sage/xtrem~tools@37.0.15) (2023-11-08)

### Bug Fixes

### Features
* XT-49755 second step ([XT-49755](https://jira.sage.com/browse/XT-49755)) ([#15329](https://github.com/Sage-ERP-X3/xtrem/issues/15329))   ([1de132c](https://github.com/Sage-ERP-X3/xtrem/commit/1de132c7ef10d3607358fd831e2afee164b49f7f))
* add turborepo & pnpm   ([#12887](https://github.com/Sage-ERP-X3/xtrem/issues/12887))   ([19f784f](https://github.com/Sage-ERP-X3/xtrem/commit/19f784f88afe1868d4dd4e3c7da235ddc5a37a08))


## [37.0.14](https://github.com/compare/...@sage/xtrem~tools@37.0.14) (2023-11-05)

### Bug Fixes

### Features


## [37.0.13](https://github.com/compare/...@sage/xtrem~tools@37.0.13) (2023-11-04)

### Bug Fixes

### Features


## [37.0.12](https://github.com/compare/...@sage/xtrem~tools@37.0.12) (2023-11-03)

### Bug Fixes

### Features


## [37.0.11](https://github.com/compare/...@sage/xtrem~tools@37.0.11) (2023-11-03)

### Bug Fixes

### Features


## [37.0.10](https://github.com/compare/...@sage/xtrem~tools@37.0.10) (2023-11-01)

### Bug Fixes

### Features


## [37.0.9](https://github.com/compare/...@sage/xtrem~tools@37.0.9) (2023-10-31)

### Bug Fixes

### Features


## [37.0.8](https://github.com/compare/...@sage/xtrem~tools@37.0.8) (2023-10-31)

### Bug Fixes

### Features


## [37.0.7](https://github.com/compare/...@sage/xtrem~tools@37.0.7) (2023-10-29)

### Bug Fixes

### Features


## [37.0.6](https://github.com/compare/...@sage/xtrem~tools@37.0.6) (2023-10-28)

### Bug Fixes

### Features


## [37.0.5](https://github.com/compare/...@sage/xtrem~tools@37.0.5) (2023-10-27)

### Bug Fixes

### Features


## [37.0.4](https://github.com/compare/...@sage/xtrem~tools@37.0.4) (2023-10-27)

### Bug Fixes

### Features


## [37.0.3](https://github.com/compare/...@sage/xtrem~tools@37.0.3) (2023-10-27)

### Bug Fixes

### Features


## [37.0.2](https://github.com/compare/...@sage/xtrem~tools@37.0.2) (2023-10-26)

### Bug Fixes

### Features


## [37.0.1](https://github.com/compare/...@sage/xtrem~tools@37.0.1) (2023-10-26)

### Bug Fixes

### Features


## [37.0.0](https://github.com/compare/...@sage/xtrem~tools@37.0.0) (2023-10-26)

### Bug Fixes

### Features


## [36.0.32](https://github.com/compare/...@sage/xtrem~tools@36.0.32) (2023-10-25)

### Bug Fixes

### Features


## [36.0.31](https://github.com/compare/...@sage/xtrem~tools@36.0.31) (2023-10-24)

### Bug Fixes

### Features


## [36.0.30](https://github.com/compare/...@sage/xtrem~tools@36.0.30) (2023-10-23)

### Bug Fixes

### Features


## [36.0.29](https://github.com/compare/...@sage/xtrem~tools@36.0.29) (2023-10-22)

### Bug Fixes

### Features


## [36.0.28](https://github.com/compare/...@sage/xtrem~tools@36.0.28) (2023-10-21)

### Bug Fixes

### Features


## [36.0.27](https://github.com/compare/...@sage/xtrem~tools@36.0.27) (2023-10-20)

### Bug Fixes

### Features


## [36.0.26](https://github.com/compare/...@sage/xtrem~tools@36.0.26) (2023-10-19)

### Bug Fixes

### Features


## [36.0.25](https://github.com/compare/...@sage/xtrem~tools@36.0.25) (2023-10-18)

### Bug Fixes

### Features


## [36.0.24](https://github.com/compare/...@sage/xtrem~tools@36.0.24) (2023-10-17)

### Bug Fixes

### Features


## [36.0.23](https://github.com/compare/...@sage/xtrem~tools@36.0.23) (2023-10-16)

### Bug Fixes

### Features


## [36.0.22](https://github.com/compare/...@sage/xtrem~tools@36.0.22) (2023-10-15)

### Bug Fixes

### Features


## [36.0.21](https://github.com/compare/...@sage/xtrem~tools@36.0.21) (2023-10-14)

### Bug Fixes

### Features


## [36.0.20](https://github.com/compare/...@sage/xtrem~tools@36.0.20) (2023-10-13)

### Bug Fixes

### Features


## [36.0.19](https://github.com/compare/...@sage/xtrem~tools@36.0.19) (2023-10-12)

### Bug Fixes

### Features


## [36.0.18](https://github.com/compare/...@sage/xtrem~tools@36.0.18) (2023-10-11)

### Bug Fixes

### Features


## [36.0.17](https://github.com/compare/...@sage/xtrem~tools@36.0.17) (2023-10-11)

### Bug Fixes

### Features


## [36.0.16](https://github.com/compare/...@sage/xtrem~tools@36.0.16) (2023-10-11)

### Bug Fixes

### Features


## [36.0.15](https://github.com/compare/...@sage/xtrem~tools@36.0.15) (2023-10-10)

### Bug Fixes

### Features


## [36.0.14](https://github.com/compare/...@sage/xtrem~tools@36.0.14) (2023-10-09)

### Bug Fixes

### Features


## [36.0.13](https://github.com/compare/...@sage/xtrem~tools@36.0.13) (2023-10-08)

### Bug Fixes

### Features


## [36.0.12](https://github.com/compare/...@sage/xtrem~tools@36.0.12) (2023-10-07)

### Bug Fixes

### Features


## [36.0.11](https://github.com/compare/...@sage/xtrem~tools@36.0.11) (2023-10-06)

### Bug Fixes

### Features


## [36.0.10](https://github.com/compare/...@sage/xtrem~tools@36.0.10) (2023-10-05)

### Bug Fixes

### Features


## [36.0.9](https://github.com/compare/...@sage/xtrem~tools@36.0.9) (2023-10-05)

### Bug Fixes

### Features


## [36.0.8](https://github.com/compare/...@sage/xtrem~tools@36.0.8) (2023-10-04)

### Bug Fixes

### Features


## [36.0.7](https://github.com/compare/...@sage/xtrem~tools@36.0.7) (2023-10-03)

### Bug Fixes

### Features
* **notifications:** delete old notifications job XT-54151 ([XT-54151](https://jira.sage.com/browse/XT-54151)) ([#14878](https://github.com/Sage-ERP-X3/xtrem/issues/14878))   ([7d6be76](https://github.com/Sage-ERP-X3/xtrem/commit/7d6be76139a9a33beff7e9b996c8c38a4399f9c5))
* XT-50680 add queue attribute to mutations decorators ([XT-50680](https://jira.sage.com/browse/XT-50680)) ([#14734](https://github.com/Sage-ERP-X3/xtrem/issues/14734))   ([3786559](https://github.com/Sage-ERP-X3/xtrem/commit/378655964894058d1f5d1a69eb38d4fa8260582f))


## [36.0.6](https://github.com/compare/...@sage/xtrem~tools@36.0.6) (2023-10-02)

### Bug Fixes
* get func name redos XT-55681 ([XT-55681](https://jira.sage.com/browse/XT-55681)) ([#14871](https://github.com/Sage-ERP-X3/xtrem/issues/14871))   ([872ff77](https://github.com/Sage-ERP-X3/xtrem/commit/872ff77795cb1271d7135dbd01780139226ad179))

### Features


## [36.0.5](https://github.com/compare/...@sage/xtrem~tools@36.0.5) (2023-10-01)

### Bug Fixes

### Features


## [36.0.4](https://github.com/compare/...@sage/xtrem~tools@36.0.4) (2023-09-30)

### Bug Fixes

### Features


## [36.0.3](https://github.com/compare/...@sage/xtrem~tools@36.0.3) (2023-09-29)

### Bug Fixes

### Features


## [36.0.2](https://github.com/compare/...@sage/xtrem~tools@36.0.2) (2023-09-28)

### Bug Fixes

### Features


## [36.0.1](https://github.com/compare/...@sage/xtrem~tools@36.0.1) (2023-09-28)

### Bug Fixes

### Features


## [36.0.0](https://github.com/compare/...@sage/xtrem~tools@36.0.0) (2023-09-28)

### Bug Fixes

### Features


## [35.0.28](https://github.com/compare/...@sage/xtrem~tools@35.0.28) (2023-09-27)

### Bug Fixes

### Features


## [35.0.27](https://github.com/compare/...@sage/xtrem~tools@35.0.27) (2023-09-26)

### Bug Fixes

### Features


## [35.0.26](https://github.com/compare/...@sage/xtrem~tools@35.0.26) (2023-09-25)

### Bug Fixes

### Features


## [35.0.25](https://github.com/compare/...@sage/xtrem~tools@35.0.25) (2023-09-24)

### Bug Fixes

### Features


## [35.0.24](https://github.com/compare/...@sage/xtrem~tools@35.0.24) (2023-09-23)

### Bug Fixes

### Features


## [35.0.23](https://github.com/compare/...@sage/xtrem~tools@35.0.23) (2023-09-22)

### Bug Fixes

### Features


## [35.0.22](https://github.com/compare/...@sage/xtrem~tools@35.0.22) (2023-09-22)

### Bug Fixes

### Features


## [35.0.21](https://github.com/compare/...@sage/xtrem~tools@35.0.21) (2023-09-20)

### Bug Fixes

### Features


## [35.0.20](https://github.com/compare/...@sage/xtrem~tools@35.0.20) (2023-09-19)

### Bug Fixes

### Features


## [35.0.19](https://github.com/compare/...@sage/xtrem~tools@35.0.19) (2023-09-18)

### Bug Fixes

### Features


## [35.0.18](https://github.com/compare/...@sage/xtrem~tools@35.0.18) (2023-09-17)

### Bug Fixes

### Features


## [35.0.17](https://github.com/compare/...@sage/xtrem~tools@35.0.17) (2023-09-16)

### Bug Fixes

### Features


## [35.0.16](https://github.com/compare/...@sage/xtrem~tools@35.0.16) (2023-09-15)

### Bug Fixes

### Features
* XT-999999 fix misleading error message ([XT-999999](https://jira.sage.com/browse/XT-999999)) ([#14546](https://github.com/Sage-ERP-X3/xtrem/issues/14546))   ([bc05462](https://github.com/Sage-ERP-X3/xtrem/commit/bc054627fd4234bacb595f8e1c1eba58048a170e))


## [35.0.15](https://github.com/compare/...@sage/xtrem~tools@35.0.15) (2023-09-14)

### Bug Fixes

### Features


## [35.0.14](https://github.com/compare/...@sage/xtrem~tools@35.0.14) (2023-09-13)

### Bug Fixes

### Features


## [35.0.13](https://github.com/compare/...@sage/xtrem~tools@35.0.13) (2023-09-12)

### Bug Fixes

### Features


## [35.0.12](https://github.com/compare/...@sage/xtrem~tools@35.0.12) (2023-09-11)

### Bug Fixes

### Features


## [35.0.11](https://github.com/compare/...@sage/xtrem~tools@35.0.11) (2023-09-10)

### Bug Fixes

### Features


## [35.0.10](https://github.com/compare/...@sage/xtrem~tools@35.0.10) (2023-09-09)

### Bug Fixes

### Features


## [35.0.9](https://github.com/compare/...@sage/xtrem~tools@35.0.9) (2023-09-08)

### Bug Fixes

### Features
* XT-36030 remove checksums from csv files ([XT-36030](https://jira.sage.com/browse/XT-36030)) ([#14457](https://github.com/Sage-ERP-X3/xtrem/issues/14457))   ([ec81171](https://github.com/Sage-ERP-X3/xtrem/commit/ec81171f22f0a2a45d5da391cf432249603dd799))
* **notification:** add new client notifications node XT-54100 ([XT-54100](https://jira.sage.com/browse/XT-54100)) ([#14445](https://github.com/Sage-ERP-X3/xtrem/issues/14445))   ([cb49e66](https://github.com/Sage-ERP-X3/xtrem/commit/cb49e660d4ea218abdaa43c436927c711f4d8787))


## [35.0.8](https://github.com/compare/...@sage/xtrem~tools@35.0.8) (2023-09-07)

### Bug Fixes

### Features


## [35.0.7](https://github.com/compare/...@sage/xtrem~tools@35.0.7) (2023-09-06)

### Bug Fixes

### Features


## [35.0.6](https://github.com/compare/...@sage/xtrem~tools@35.0.6) (2023-09-06)

### Bug Fixes

### Features


## [35.0.5](https://github.com/compare/...@sage/xtrem~tools@35.0.5) (2023-09-05)

### Bug Fixes

### Features
* **xtrem-services:** XT-53368 rename Sage Intacct Manufacturing to Sage DMO ([XT-53368](https://jira.sage.com/browse/XT-53368)) ([#14311](https://github.com/Sage-ERP-X3/xtrem/issues/14311))   ([151d477](https://github.com/Sage-ERP-X3/xtrem/commit/151d477fc6c51e9404aae42e628b13e7b91ad24b))


## [35.0.4](https://github.com/compare/...@sage/xtrem~tools@35.0.4) (2023-09-04)

### Bug Fixes

### Features


## [35.0.3](https://github.com/compare/...@sage/xtrem~tools@35.0.3) (2023-09-03)

### Bug Fixes

### Features


## [35.0.2](https://github.com/compare/...@sage/xtrem~tools@35.0.2) (2023-09-02)

### Bug Fixes

### Features


## [35.0.1](https://github.com/compare/...@sage/xtrem~tools@35.0.1) (2023-09-01)

### Bug Fixes

### Features


## [35.0.0](https://github.com/compare/...@sage/xtrem~tools@35.0.0) (2023-09-01)

### Bug Fixes

### Features


## [34.0.40](https://github.com/compare/...@sage/xtrem~tools@34.0.40) (2023-09-01)

### Bug Fixes

### Features


## [34.0.39](https://github.com/compare/...@sage/xtrem~tools@34.0.39) (2023-08-31)

### Bug Fixes

### Features


## [34.0.38](https://github.com/compare/...@sage/xtrem~tools@34.0.38) (2023-08-31)

### Bug Fixes

### Features


## [34.0.37](https://github.com/compare/...@sage/xtrem~tools@34.0.37) (2023-08-30)

### Bug Fixes

### Features


## [34.0.36](https://github.com/compare/...@sage/xtrem~tools@34.0.36) (2023-08-29)

### Bug Fixes

### Features


## [34.0.35](https://github.com/compare/...@sage/xtrem~tools@34.0.35) (2023-08-28)

### Bug Fixes

### Features


## [34.0.34](https://github.com/compare/...@sage/xtrem~tools@34.0.34) (2023-08-28)

### Bug Fixes

### Features


## [34.0.33](https://github.com/compare/...@sage/xtrem~tools@34.0.33) (2023-08-27)

### Bug Fixes

### Features


## [34.0.32](https://github.com/compare/...@sage/xtrem~tools@34.0.32) (2023-08-26)

### Bug Fixes

### Features
* nyc to c8 migration   ([#14259](https://github.com/Sage-ERP-X3/xtrem/issues/14259))   ([1ce3941](https://github.com/Sage-ERP-X3/xtrem/commit/1ce3941ab544f6f7f42239c25932fa6c5265eefa))


## [34.0.31](https://github.com/compare/...@sage/xtrem~tools@34.0.31) (2023-08-25)

### Bug Fixes

### Features


## [34.0.30](https://github.com/compare/...@sage/xtrem~tools@34.0.30) (2023-08-25)

### Bug Fixes

### Features


## [34.0.29](https://github.com/compare/...@sage/xtrem~tools@34.0.29) (2023-08-23)

### Bug Fixes

### Features


## [34.0.28](https://github.com/compare/...@sage/xtrem~tools@34.0.28) (2023-08-22)

### Bug Fixes

### Features
* XT-53156 convert batchImport to Async ([XT-53156](https://jira.sage.com/browse/XT-53156)) ([#14145](https://github.com/Sage-ERP-X3/xtrem/issues/14145))   ([4171eea](https://github.com/Sage-ERP-X3/xtrem/commit/4171eeaebaef61b9466bfa9ff3a828704adf3c62))


## [34.0.27](https://github.com/compare/...@sage/xtrem~tools@34.0.27) (2023-08-21)

### Bug Fixes

### Features


## [34.0.26](https://github.com/compare/...@sage/xtrem~tools@34.0.26) (2023-08-20)

### Bug Fixes

### Features


## [34.0.25](https://github.com/compare/...@sage/xtrem~tools@34.0.25) (2023-08-19)

### Bug Fixes

### Features


## [34.0.24](https://github.com/compare/...@sage/xtrem~tools@34.0.24) (2023-08-18)

### Bug Fixes

### Features


## [34.0.23](https://github.com/compare/...@sage/xtrem~tools@34.0.23) (2023-08-17)

### Bug Fixes

### Features


## [34.0.22](https://github.com/compare/...@sage/xtrem~tools@34.0.22) (2023-08-17)

### Bug Fixes

### Features


## [34.0.21](https://github.com/compare/...@sage/xtrem~tools@34.0.21) (2023-08-16)

### Bug Fixes

### Features


## [34.0.20](https://github.com/compare/...@sage/xtrem~tools@34.0.20) (2023-08-15)

### Bug Fixes

### Features


## [34.0.19](https://github.com/compare/...@sage/xtrem~tools@34.0.19) (2023-08-14)

### Bug Fixes

### Features


## [34.0.18](https://github.com/compare/...@sage/xtrem~tools@34.0.18) (2023-08-13)

### Bug Fixes

### Features


## [34.0.17](https://github.com/compare/...@sage/xtrem~tools@34.0.17) (2023-08-12)

### Bug Fixes

### Features


## [34.0.16](https://github.com/compare/...@sage/xtrem~tools@34.0.16) (2023-08-11)

### Bug Fixes

### Features


## [34.0.15](https://github.com/compare/...@sage/xtrem~tools@34.0.15) (2023-08-10)

### Bug Fixes

### Features


## [34.0.14](https://github.com/compare/...@sage/xtrem~tools@34.0.14) (2023-08-09)

### Bug Fixes

### Features


## [34.0.13](https://github.com/compare/...@sage/xtrem~tools@34.0.13) (2023-08-08)

### Bug Fixes
* package and activity title localization XT-52100 ([XT-52100](https://jira.sage.com/browse/XT-52100)) ([#13942](https://github.com/Sage-ERP-X3/xtrem/issues/13942))   ([891aa52](https://github.com/Sage-ERP-X3/xtrem/commit/891aa527d1044334a4b9ce230bcecadbcc03509a))

### Features


## [34.0.12](https://github.com/compare/...@sage/xtrem~tools@34.0.12) (2023-08-06)

### Bug Fixes

### Features


## [34.0.11](https://github.com/compare/...@sage/xtrem~tools@34.0.11) (2023-08-05)

### Bug Fixes

### Features


## [34.0.10](https://github.com/compare/...@sage/xtrem~tools@34.0.10) (2023-08-04)

### Bug Fixes
* generate metadata and localization for activities (back-end side) XT-52100 ([XT-52100](https://jira.sage.com/browse/XT-52100)) ([#13925](https://github.com/Sage-ERP-X3/xtrem/issues/13925))   ([e58a105](https://github.com/Sage-ERP-X3/xtrem/commit/e58a105c5882161fff4aca916320edf24d808300))

### Features


## [34.0.9](https://github.com/compare/...@sage/xtrem~tools@34.0.9) (2023-08-03)

### Bug Fixes

### Features


## [34.0.8](https://github.com/compare/...@sage/xtrem~tools@34.0.8) (2023-08-02)

### Bug Fixes

### Features


## [34.0.7](https://github.com/compare/...@sage/xtrem~tools@34.0.7) (2023-08-01)

### Bug Fixes

### Features


## [34.0.6](https://github.com/compare/...@sage/xtrem~tools@34.0.6) (2023-07-31)

### Bug Fixes

### Features


## [34.0.5](https://github.com/compare/...@sage/xtrem~tools@34.0.5) (2023-07-30)

### Bug Fixes

### Features


## [34.0.4](https://github.com/compare/...@sage/xtrem~tools@34.0.4) (2023-07-29)

### Bug Fixes

### Features


## [34.0.3](https://github.com/compare/...@sage/xtrem~tools@34.0.3) (2023-07-28)

### Bug Fixes

### Features
* XT-47452 custom data for standard import ([XT-47452](https://jira.sage.com/browse/XT-47452)) ([#13829](https://github.com/Sage-ERP-X3/xtrem/issues/13829))   ([7267c2d](https://github.com/Sage-ERP-X3/xtrem/commit/7267c2d0a08709c6446cf5990d1be7cc6853b98d))


## [34.0.2](https://github.com/compare/...@sage/xtrem~tools@34.0.2) (2023-07-27)

### Bug Fixes

### Features


## [34.0.1](https://github.com/compare/...@sage/xtrem~tools@34.0.1) (2023-07-27)

### Bug Fixes

### Features


## [34.0.0](https://github.com/compare/...@sage/xtrem~tools@34.0.0) (2023-07-27)

### Bug Fixes

### Features


## [33.0.28](https://github.com/compare/...@sage/xtrem~tools@33.0.28) (2023-07-26)

### Bug Fixes

### Features


## [33.0.27](https://github.com/compare/...@sage/xtrem~tools@33.0.27) (2023-07-25)

### Bug Fixes
* XT-48450 fixed capitalization of metadata titles in base.json files ([XT-48450](https://jira.sage.com/browse/XT-48450)) ([#13728](https://github.com/Sage-ERP-X3/xtrem/issues/13728))   ([3767009](https://github.com/Sage-ERP-X3/xtrem/commit/376700936005589831230a7c9fb1987a9615bca6))

### Features


## [33.0.26](https://github.com/compare/...@sage/xtrem~tools@33.0.26) (2023-07-24)

### Bug Fixes

### Features


## [33.0.25](https://github.com/compare/...@sage/xtrem~tools@33.0.25) (2023-07-23)

### Bug Fixes

### Features


## [33.0.24](https://github.com/compare/...@sage/xtrem~tools@33.0.24) (2023-07-22)

### Bug Fixes

### Features


## [33.0.23](https://github.com/compare/...@sage/xtrem~tools@33.0.23) (2023-07-21)

### Bug Fixes

### Features


## [33.0.22](https://github.com/compare/...@sage/xtrem~tools@33.0.22) (2023-07-20)

### Bug Fixes

### Features


## [33.0.21](https://github.com/compare/...@sage/xtrem~tools@33.0.21) (2023-07-19)

### Bug Fixes

### Features


## [33.0.20](https://github.com/compare/...@sage/xtrem~tools@33.0.20) (2023-07-18)

### Bug Fixes

### Features


## [33.0.19](https://github.com/compare/...@sage/xtrem~tools@33.0.19) (2023-07-17)

### Bug Fixes

### Features
* XT-37845 manage upgrade from prod backups ([XT-37845](https://jira.sage.com/browse/XT-37845)) ([#13613](https://github.com/Sage-ERP-X3/xtrem/issues/13613))   ([d71a4ed](https://github.com/Sage-ERP-X3/xtrem/commit/d71a4ed26eaa48016aa980ec8c8b55092b9664c4))


## [33.0.18](https://github.com/compare/...@sage/xtrem~tools@33.0.18) (2023-07-16)

### Bug Fixes

### Features


## [33.0.17](https://github.com/compare/...@sage/xtrem~tools@33.0.17) (2023-07-15)

### Bug Fixes

### Features


## [33.0.16](https://github.com/compare/...@sage/xtrem~tools@33.0.16) (2023-07-13)

### Bug Fixes

### Features


## [33.0.15](https://github.com/compare/...@sage/xtrem~tools@33.0.15) (2023-07-12)

### Bug Fixes

### Features
* **xtrem-import-export:** XT-47412 import export template page business logic ([XT-47412](https://jira.sage.com/browse/XT-47412)) ([#13556](https://github.com/Sage-ERP-X3/xtrem/issues/13556))   ([167175a](https://github.com/Sage-ERP-X3/xtrem/commit/167175a0c5cc8bb7ade33bdb75c97a5fd271ad1d))


## [33.0.14](https://github.com/compare/...@sage/xtrem~tools@33.0.14) (2023-07-11)

### Bug Fixes
* **master-data:** XT-50244 fix access control ([XT-50244](https://jira.sage.com/browse/XT-50244)) ([#13510](https://github.com/Sage-ERP-X3/xtrem/issues/13510))   ([b0bb7af](https://github.com/Sage-ERP-X3/xtrem/commit/b0bb7af3eda273aa04ee58c9c3381f08faf99622))

### Features
* XT-46023 modify getNodeExportTemplates return type and rename asyncExport parameter ([XT-46023](https://jira.sage.com/browse/XT-46023)) ([#13588](https://github.com/Sage-ERP-X3/xtrem/issues/13588))   ([cac9108](https://github.com/Sage-ERP-X3/xtrem/commit/cac910868164d55186a58776f28a24bbfd33b15e))


## [33.0.13](https://github.com/compare/...@sage/xtrem~tools@33.0.13) (2023-07-10)

### Bug Fixes

### Features


## [33.0.12](https://github.com/compare/...@sage/xtrem~tools@33.0.12) (2023-07-09)

### Bug Fixes

### Features


## [33.0.11](https://github.com/compare/...@sage/xtrem~tools@33.0.11) (2023-07-08)

### Bug Fixes

### Features


## [33.0.10](https://github.com/compare/...@sage/xtrem~tools@33.0.10) (2023-07-07)

### Bug Fixes

### Features


## [33.0.9](https://github.com/compare/...@sage/xtrem~tools@33.0.9) (2023-07-06)

### Bug Fixes

### Features
* XT-46023 settle async export frame ([XT-46023](https://jira.sage.com/browse/XT-46023)) ([#13437](https://github.com/Sage-ERP-X3/xtrem/issues/13437))   ([7fd6fd8](https://github.com/Sage-ERP-X3/xtrem/commit/7fd6fd8d72609d3c8e073cb2ce461d2f8c187551))


## [33.0.8](https://github.com/compare/...@sage/xtrem~tools@33.0.8) (2023-07-05)

### Bug Fixes

### Features


## [33.0.7](https://github.com/compare/...@sage/xtrem~tools@33.0.7) (2023-07-04)

### Bug Fixes

### Features


## [33.0.6](https://github.com/compare/...@sage/xtrem~tools@33.0.6) (2023-07-03)

### Bug Fixes

### Features
* **scheduler:** Scheduler page & wizard   XT-48282 ([XT-48282](https://jira.sage.com/browse/XT-48282)) ([#13433](https://github.com/Sage-ERP-X3/xtrem/issues/13433))   ([12ba2e1](https://github.com/Sage-ERP-X3/xtrem/commit/12ba2e1cc357dd4b6dcbd5c5924137341e46b153))


## [33.0.5](https://github.com/compare/...@sage/xtrem~tools@33.0.5) (2023-07-02)

### Bug Fixes

### Features


## [33.0.4](https://github.com/compare/...@sage/xtrem~tools@33.0.4) (2023-07-01)

### Bug Fixes

### Features


## [33.0.3](https://github.com/compare/...@sage/xtrem~tools@33.0.3) (2023-06-30)

### Bug Fixes

### Features
* **xtrem-cli:** XT-39028 Data table refactoring with static store ([XT-39028](https://jira.sage.com/browse/XT-39028)) ([#12444](https://github.com/Sage-ERP-X3/xtrem/issues/12444))   ([3a99605](https://github.com/Sage-ERP-X3/xtrem/commit/3a99605243cb47ffae63cb1dc7eb08e40cbcb68a))


## [33.0.2](https://github.com/compare/...@sage/xtrem~tools@33.0.2) (2023-06-29)

### Bug Fixes

### Features


## [33.0.1](https://github.com/compare/...@sage/xtrem~tools@33.0.1) (2023-06-29)

### Bug Fixes

### Features


## [33.0.0](https://github.com/compare/...@sage/xtrem~tools@33.0.0) (2023-06-29)

### Bug Fixes

### Features


## [32.0.37](https://github.com/compare/...@sage/xtrem~tools@32.0.37) (2023-06-28)

### Bug Fixes

### Features


## [32.0.36](https://github.com/compare/...@sage/xtrem~tools@32.0.36) (2023-06-27)

### Bug Fixes
* XT-49417 enable bulk delete on glossary node ([XT-49417](https://jira.sage.com/browse/XT-49417)) ([#13352](https://github.com/Sage-ERP-X3/xtrem/issues/13352))   ([cce642e](https://github.com/Sage-ERP-X3/xtrem/commit/cce642e8d593f02cb21755046b0125c7d5f6cb6b))

### Features


## [32.0.35](https://github.com/compare/...@sage/xtrem~tools@32.0.35) (2023-06-26)

### Bug Fixes
* XT-49417 generate i18n entries for bulkDelete and bulkUpdate, correction ([XT-49417](https://jira.sage.com/browse/XT-49417)) ([#13336](https://github.com/Sage-ERP-X3/xtrem/issues/13336))   ([7bac65a](https://github.com/Sage-ERP-X3/xtrem/commit/7bac65a1961dfdb04db75bfc0b2ff82b8b1af95f))

### Features


## [32.0.34](https://github.com/compare/...@sage/xtrem~tools@32.0.34) (2023-06-25)

### Bug Fixes

### Features


## [32.0.33](https://github.com/compare/...@sage/xtrem~tools@32.0.33) (2023-06-24)

### Bug Fixes

### Features


## [32.0.32](https://github.com/compare/...@sage/xtrem~tools@32.0.32) (2023-06-23)

### Bug Fixes
* XT-49417 generate i18n entries for bulkDelete and bulkUpdate ([XT-49417](https://jira.sage.com/browse/XT-49417)) ([#13316](https://github.com/Sage-ERP-X3/xtrem/issues/13316))   ([bfe3c0a](https://github.com/Sage-ERP-X3/xtrem/commit/bfe3c0abe00efd849f7fe547c51fb2eccefc2abe))

### Features


## [32.0.31](https://github.com/compare/...@sage/xtrem~tools@32.0.31) (2023-06-22)

### Bug Fixes

### Features


## [32.0.30](https://github.com/compare/...@sage/xtrem~tools@32.0.30) (2023-06-21)

### Bug Fixes
* XT-48923 Remove setup data ([XT-48923](https://jira.sage.com/browse/XT-48923)) ([#13243](https://github.com/Sage-ERP-X3/xtrem/issues/13243))   ([154506f](https://github.com/Sage-ERP-X3/xtrem/commit/154506f9c89a09225237d2725c0701e85ffdb4b6))

### Features


## [32.0.29](https://github.com/compare/...@sage/xtrem~tools@32.0.29) (2023-06-20)

### Bug Fixes

### Features
* **glossary:** xgl 306 transfer or delete   ([#13245](https://github.com/Sage-ERP-X3/xtrem/issues/13245))   ([1f7c6eb](https://github.com/Sage-ERP-X3/xtrem/commit/1f7c6ebb2eff2d6075f55545a574868ed1ac3ffd))


## [32.0.28](https://github.com/compare/...@sage/xtrem~tools@32.0.28) (2023-06-19)

### Bug Fixes

### Features


## [32.0.27](https://github.com/compare/...@sage/xtrem~tools@32.0.27) (2023-06-18)

### Bug Fixes

### Features


## [32.0.26](https://github.com/compare/...@sage/xtrem~tools@32.0.26) (2023-06-17)

### Bug Fixes

### Features


## [32.0.25](https://github.com/compare/...@sage/xtrem~tools@32.0.25) (2023-06-16)

### Bug Fixes

### Features


## [32.0.24](https://github.com/compare/...@sage/xtrem~tools@32.0.24) (2023-06-15)

### Bug Fixes

### Features


## [32.0.23](https://github.com/compare/...@sage/xtrem~tools@32.0.23) (2023-06-14)

### Bug Fixes

### Features


## [32.0.22](https://github.com/compare/...@sage/xtrem~tools@32.0.22) (2023-06-13)

### Bug Fixes

### Features


## [32.0.21](https://github.com/compare/...@sage/xtrem~tools@32.0.21) (2023-06-12)

### Bug Fixes

### Features


## [32.0.20](https://github.com/compare/...@sage/xtrem~tools@32.0.20) (2023-06-11)

### Bug Fixes

### Features


## [32.0.19](https://github.com/compare/...@sage/xtrem~tools@32.0.19) (2023-06-10)

### Bug Fixes

### Features


## [32.0.18](https://github.com/compare/...@sage/xtrem~tools@32.0.18) (2023-06-09)

### Bug Fixes

### Features


## [32.0.17](https://github.com/compare/...@sage/xtrem~tools@32.0.17) (2023-06-08)

### Bug Fixes

### Features


## [32.0.16](https://github.com/compare/...@sage/xtrem~tools@32.0.16) (2023-06-07)

### Bug Fixes

### Features


## [32.0.15](https://github.com/compare/...@sage/xtrem~tools@32.0.15) (2023-06-07)

### Bug Fixes

### Features
* **scheduler:** POC Notification center XT-47813 ([XT-47813](https://jira.sage.com/browse/XT-47813)) ([#13010](https://github.com/Sage-ERP-X3/xtrem/issues/13010))   ([4880261](https://github.com/Sage-ERP-X3/xtrem/commit/4880261bf922c142dd5383eade34d79f51e4361c))


## [32.0.14](https://github.com/compare/...@sage/xtrem~tools@32.0.14) (2023-06-06)

### Bug Fixes

### Features
* **glossary:** XGL-303 creation transfer all   ([#13054](https://github.com/Sage-ERP-X3/xtrem/issues/13054))   ([4b6f71b](https://github.com/Sage-ERP-X3/xtrem/commit/4b6f71b3f840a31f039a28fa82c4a92aa1d9a2f9))


## [32.0.13](https://github.com/compare/...@sage/xtrem~tools@32.0.13) (2023-06-05)

### Bug Fixes

### Features


## [32.0.12](https://github.com/compare/...@sage/xtrem~tools@32.0.12) (2023-06-04)

### Bug Fixes

### Features


## [32.0.11](https://github.com/compare/...@sage/xtrem~tools@32.0.11) (2023-06-03)

### Bug Fixes

### Features


## [32.0.10](https://github.com/compare/...@sage/xtrem~tools@32.0.10) (2023-06-02)

### Bug Fixes

### Features


## [32.0.9](https://github.com/compare/...@sage/xtrem~tools@32.0.9) (2023-06-01)

### Bug Fixes

### Features


## [32.0.8](https://github.com/compare/...@sage/xtrem~tools@32.0.8) (2023-05-31)

### Bug Fixes

### Features


## [32.0.7](https://github.com/compare/...@sage/xtrem~tools@32.0.7) (2023-05-30)

### Bug Fixes

### Features


## [32.0.6](https://github.com/compare/...@sage/xtrem~tools@32.0.6) (2023-05-29)

### Bug Fixes

### Features


## [32.0.5](https://github.com/compare/...@sage/xtrem~tools@32.0.5) (2023-05-28)

### Bug Fixes

### Features


## [32.0.4](https://github.com/compare/...@sage/xtrem~tools@32.0.4) (2023-05-27)

### Bug Fixes

### Features


## [32.0.3](https://github.com/compare/...@sage/xtrem~tools@32.0.3) (2023-05-26)

### Bug Fixes

### Features
* **batch:** Can delete notification state   ([#12893](https://github.com/Sage-ERP-X3/xtrem/issues/12893))   ([acf1cf6](https://github.com/Sage-ERP-X3/xtrem/commit/acf1cf6e5a84d0e3c89ef5148ace30f080c483d4))


## [32.0.2](https://github.com/compare/...@sage/xtrem~tools@32.0.2) (2023-05-25)

### Bug Fixes

### Features


## [32.0.1](https://github.com/compare/...@sage/xtrem~tools@32.0.1) (2023-05-25)

### Bug Fixes

### Features


## [32.0.0](https://github.com/compare/...@sage/xtrem~tools@32.0.0) (2023-05-25)

### Bug Fixes

### Features


## [31.0.32](https://github.com/compare/...@sage/xtrem~tools@31.0.32) (2023-05-24)

### Bug Fixes
* async bulkDelete mutation XT-46094 ([XT-46094](https://jira.sage.com/browse/XT-46094)) ([#12845](https://github.com/Sage-ERP-X3/xtrem/issues/12845))   ([371cfa1](https://github.com/Sage-ERP-X3/xtrem/commit/371cfa164aab2c5c70823a8887a8caa14b8f565b))

### Features


## [31.0.31](https://github.com/compare/...@sage/xtrem~tools@31.0.31) (2023-05-24)

### Bug Fixes

### Features


## [31.0.30](https://github.com/compare/...@sage/xtrem~tools@31.0.30) (2023-05-22)

### Bug Fixes

### Features


## [31.0.29](https://github.com/compare/...@sage/xtrem~tools@31.0.29) (2023-05-21)

### Bug Fixes

### Features


## [31.0.28](https://github.com/compare/...@sage/xtrem~tools@31.0.28) (2023-05-20)

### Bug Fixes

### Features


## [31.0.27](https://github.com/compare/...@sage/xtrem~tools@31.0.27) (2023-05-19)

### Bug Fixes

### Features


## [31.0.26](https://github.com/compare/...@sage/xtrem~tools@31.0.26) (2023-05-18)

### Bug Fixes

### Features


## [31.0.25](https://github.com/compare/...@sage/xtrem~tools@31.0.25) (2023-05-17)

### Bug Fixes

### Features


## [31.0.24](https://github.com/compare/...@sage/xtrem~tools@31.0.24) (2023-05-17)

### Bug Fixes

### Features


## [31.0.23](https://github.com/compare/...@sage/xtrem~tools@31.0.23) (2023-05-15)

### Bug Fixes

### Features


## [31.0.22](https://github.com/compare/...@sage/xtrem~tools@31.0.22) (2023-05-15)

### Bug Fixes

### Features


## [31.0.21](https://github.com/compare/...@sage/xtrem~tools@31.0.21) (2023-05-15)

### Bug Fixes

### Features


## [31.0.20](https://github.com/compare/...@sage/xtrem~tools@31.0.20) (2023-05-13)

### Bug Fixes

### Features


## [31.0.19](https://github.com/compare/...@sage/xtrem~tools@31.0.19) (2023-05-12)

### Bug Fixes

### Features


## [31.0.18](https://github.com/compare/...@sage/xtrem~tools@31.0.18) (2023-05-12)

### Bug Fixes

### Features


## [31.0.17](https://github.com/compare/...@sage/xtrem~tools@31.0.17) (2023-05-11)

### Bug Fixes

### Features


## [31.0.16](https://github.com/compare/...@sage/xtrem~tools@31.0.16) (2023-05-11)

### Bug Fixes

### Features


## [31.0.15](https://github.com/compare/...@sage/xtrem~tools@31.0.15) (2023-05-10)

### Bug Fixes

### Features


## [31.0.14](https://github.com/compare/...@sage/xtrem~tools@31.0.14) (2023-05-09)

### Bug Fixes

### Features


## [31.0.13](https://github.com/compare/...@sage/xtrem~tools@31.0.13) (2023-05-08)

### Bug Fixes

### Features


## [31.0.12](https://github.com/compare/...@sage/xtrem~tools@31.0.12) (2023-05-07)

### Bug Fixes

### Features


## [31.0.11](https://github.com/compare/...@sage/xtrem~tools@31.0.11) (2023-05-06)

### Bug Fixes

### Features


## [31.0.10](https://github.com/compare/...@sage/xtrem~tools@31.0.10) (2023-05-05)

### Bug Fixes

### Features


## [31.0.9](https://github.com/compare/...@sage/xtrem~tools@31.0.9) (2023-05-04)

### Bug Fixes

### Features


## [31.0.8](https://github.com/compare/...@sage/xtrem~tools@31.0.8) (2023-05-03)

### Bug Fixes

### Features
* batching/XT-41162 operation localisation ([XT-41162](https://jira.sage.com/browse/XT-41162)) ([#12516](https://github.com/Sage-ERP-X3/xtrem/issues/12516))   ([76482f5](https://github.com/Sage-ERP-X3/xtrem/commit/76482f509af9c5c3c11ef5ab27faa9ab74a76aed))


## [31.0.7](https://github.com/compare/...@sage/xtrem~tools@31.0.7) (2023-05-02)

### Bug Fixes
* **xtrem-glossary:** xgl-301 term creation   ([#12582](https://github.com/Sage-ERP-X3/xtrem/issues/12582))   ([7ce10b2](https://github.com/Sage-ERP-X3/xtrem/commit/7ce10b261f6893f27d6fea4983b197e9ba435ff1))

### Features


## [31.0.6](https://github.com/compare/...@sage/xtrem~tools@31.0.6) (2023-05-01)

### Bug Fixes

### Features


## [31.0.5](https://github.com/compare/...@sage/xtrem~tools@31.0.5) (2023-04-30)

### Bug Fixes

### Features


## [31.0.4](https://github.com/compare/...@sage/xtrem~tools@31.0.4) (2023-04-29)

### Bug Fixes

### Features


## [31.0.3](https://github.com/compare/...@sage/xtrem~tools@31.0.3) (2023-04-28)

### Bug Fixes

### Features
* **scheduler:** Mutation for purging notification history XT-45920 ([XT-45920](https://jira.sage.com/browse/XT-45920)) ([#12487](https://github.com/Sage-ERP-X3/xtrem/issues/12487))   ([0f02fdc](https://github.com/Sage-ERP-X3/xtrem/commit/0f02fdce93a540c86f4bca5ad4c27fd9f671d113))
* **scheduler:** confirm stop   ([#12535](https://github.com/Sage-ERP-X3/xtrem/issues/12535))   ([ed453d6](https://github.com/Sage-ERP-X3/xtrem/commit/ed453d61a131e9e35a7f924156964c8a919aa1a5))


## [31.0.2](https://github.com/compare/...@sage/xtrem~tools@31.0.2) (2023-04-27)

### Bug Fixes

### Features


## [31.0.1](https://github.com/compare/...@sage/xtrem~tools@31.0.1) (2023-04-27)

### Bug Fixes

### Features


## [31.0.0](https://github.com/compare/...@sage/xtrem~tools@31.0.0) (2023-04-27)

### Bug Fixes

### Features


## [30.0.32](https://github.com/compare/...@sage/xtrem~tools@30.0.32) (2023-04-27)

### Bug Fixes

### Features


## [30.0.31](https://github.com/compare/...@sage/xtrem~tools@30.0.31) (2023-04-26)

### Bug Fixes

### Features


## [30.0.30](https://github.com/compare/...@sage/xtrem~tools@30.0.30) (2023-04-25)

### Bug Fixes

### Features


## [30.0.29](https://github.com/compare/...@sage/xtrem~tools@30.0.29) (2023-04-24)

### Bug Fixes

### Features
* **xtrem glosssary:** xgl-259 various changes   ([#12466](https://github.com/Sage-ERP-X3/xtrem/issues/12466))   ([7ec202a](https://github.com/Sage-ERP-X3/xtrem/commit/7ec202a06abdfa205cde47a6a301e865a6823a87))


## [30.0.28](https://github.com/compare/...@sage/xtrem~tools@30.0.28) (2023-04-23)

### Bug Fixes

### Features


## [30.0.27](https://github.com/compare/...@sage/xtrem~tools@30.0.27) (2023-04-22)

### Bug Fixes

### Features


## [30.0.26](https://github.com/compare/...@sage/xtrem~tools@30.0.26) (2023-04-21)

### Bug Fixes
* XT-42197 fix glossary SQL file ([XT-42197](https://jira.sage.com/browse/XT-42197)) ([#12468](https://github.com/Sage-ERP-X3/xtrem/issues/12468))   ([89641dd](https://github.com/Sage-ERP-X3/xtrem/commit/89641dd4fadbe3bffac27c2d258dfd2c86326b15))

### Features


## [30.0.25](https://github.com/compare/...@sage/xtrem~tools@30.0.25) (2023-04-20)

### Bug Fixes

### Features
* **xtrem-glossary:** xgl-259 area and columns glossary removed in staging   ([#12450](https://github.com/Sage-ERP-X3/xtrem/issues/12450))   ([d6e115a](https://github.com/Sage-ERP-X3/xtrem/commit/d6e115aea7302e04f77fa5a679d6909bd0fc1bad))


## [30.0.24](https://github.com/compare/...@sage/xtrem~tools@30.0.24) (2023-04-20)

### Bug Fixes

### Features


## [30.0.23](https://github.com/compare/...@sage/xtrem~tools@30.0.23) (2023-04-20)

### Bug Fixes

### Features


## [30.0.22](https://github.com/compare/...@sage/xtrem~tools@30.0.22) (2023-04-18)

### Bug Fixes

### Features
* **xtrem glossary:** xgl-259 adding transfer function   ([#12409](https://github.com/Sage-ERP-X3/xtrem/issues/12409))   ([0934328](https://github.com/Sage-ERP-X3/xtrem/commit/09343281d02683d0701e05226b7d17c710530ac5))


## [30.0.21](https://github.com/compare/...@sage/xtrem~tools@30.0.21) (2023-04-17)

### Bug Fixes
* **xtrem-cli:** XT-42923 - scope pipelines refactoring ([XT-42923](https://jira.sage.com/browse/XT-42923)) ([#12244](https://github.com/Sage-ERP-X3/xtrem/issues/12244))   ([23b2b25](https://github.com/Sage-ERP-X3/xtrem/commit/23b2b257d80722390fb2a85288e444ab21af135b))

### Features


## [30.0.20](https://github.com/compare/...@sage/xtrem~tools@30.0.20) (2023-04-16)

### Bug Fixes

### Features


## [30.0.19](https://github.com/compare/...@sage/xtrem~tools@30.0.19) (2023-04-15)

### Bug Fixes

### Features
* **xtrem-glossary:** XGL-229 term integration   ([#12367](https://github.com/Sage-ERP-X3/xtrem/issues/12367))   ([00a94fa](https://github.com/Sage-ERP-X3/xtrem/commit/00a94face8379fe4b5091719a35681a2c315b3a3))


## [30.0.18](https://github.com/compare/...@sage/xtrem~tools@30.0.18) (2023-04-14)

### Bug Fixes
* **xtrem-glossary:** some last fixes on integration bulk method and page   ([#12357](https://github.com/Sage-ERP-X3/xtrem/issues/12357))   ([d4f35e0](https://github.com/Sage-ERP-X3/xtrem/commit/d4f35e06304056f9b2aef1d767cfffc200582b2b))

### Features


## [30.0.17](https://github.com/compare/...@sage/xtrem~tools@30.0.17) (2023-04-13)

### Bug Fixes

### Features
* **xtrem-glossary:** creation of glossary entry from staging   ([#12322](https://github.com/Sage-ERP-X3/xtrem/issues/12322))   ([700b5d1](https://github.com/Sage-ERP-X3/xtrem/commit/700b5d1071975d7529d5e1107da9e4ced16a4497))


## [30.0.16](https://github.com/compare/...@sage/xtrem~tools@30.0.16) (2023-04-12)

### Bug Fixes

### Features


## [30.0.15](https://github.com/compare/...@sage/xtrem~tools@30.0.15) (2023-04-11)

### Bug Fixes
* **xtrem-glossary:** fixing loading of demo data   ([#12310](https://github.com/Sage-ERP-X3/xtrem/issues/12310))   ([801e5e3](https://github.com/Sage-ERP-X3/xtrem/commit/801e5e358fdee6a8141990ff7b461789bf3b8883))

### Features


## [30.0.14](https://github.com/compare/...@sage/xtrem~tools@30.0.14) (2023-04-10)

### Bug Fixes

### Features


## [30.0.13](https://github.com/compare/...@sage/xtrem~tools@30.0.13) (2023-04-09)

### Bug Fixes

### Features


## [30.0.12](https://github.com/compare/...@sage/xtrem~tools@30.0.12) (2023-04-08)

### Bug Fixes

### Features


## [30.0.11](https://github.com/compare/...@sage/xtrem~tools@30.0.11) (2023-04-07)

### Bug Fixes

### Features
* **xtrem-glossary:** implementation of staging master creation when creating a staging import   ([#12289](https://github.com/Sage-ERP-X3/xtrem/issues/12289))   ([c940775](https://github.com/Sage-ERP-X3/xtrem/commit/c940775a7aafa3863779aecf240c064160a45409))


## [30.0.10](https://github.com/compare/...@sage/xtrem~tools@30.0.10) (2023-04-07)

### Bug Fixes

### Features


## [30.0.9](https://github.com/compare/...@sage/xtrem~tools@30.0.9) (2023-04-06)

### Bug Fixes

### Features
* **xtrem-glossary:** glossary staging   ([#12207](https://github.com/Sage-ERP-X3/xtrem/issues/12207))   ([badf21a](https://github.com/Sage-ERP-X3/xtrem/commit/badf21ab7ff815a8c7c1d562820ad563456ae94e))


## [30.0.8](https://github.com/compare/...@sage/xtrem~tools@30.0.8) (2023-04-05)

### Bug Fixes
* **xtrem-glossary:** improving and cleaning the admin-dashboard main glossary page   ([#12242](https://github.com/Sage-ERP-X3/xtrem/issues/12242))   ([417bf09](https://github.com/Sage-ERP-X3/xtrem/commit/417bf09f8acf93c475970f37938b115854a91b1e))

### Features
* support translations on deep binding   ([#12245](https://github.com/Sage-ERP-X3/xtrem/issues/12245))   ([3a87561](https://github.com/Sage-ERP-X3/xtrem/commit/3a87561f3245543c4afbc68bbb975f40e48c407d))


## [30.0.7](https://github.com/compare/...@sage/xtrem~tools@30.0.7) (2023-04-04)

### Bug Fixes
* XT-44762 fix class name in topic of async mutation defined in an extension node ([XT-44762](https://jira.sage.com/browse/XT-44762)) ([#12187](https://github.com/Sage-ERP-X3/xtrem/issues/12187))   ([8ba1465](https://github.com/Sage-ERP-X3/xtrem/commit/8ba146553d26a6e6f8feb873933e70c088881389))

### Features


## [30.0.6](https://github.com/compare/...@sage/xtrem~tools@30.0.6) (2023-04-03)

### Bug Fixes

### Features
* **xtrem-glossary:** add a space after comma   ([#12189](https://github.com/Sage-ERP-X3/xtrem/issues/12189))   ([cd74bc8](https://github.com/Sage-ERP-X3/xtrem/commit/cd74bc82ae2a3d15da63a2b679ce0896e0a3621e))


## [30.0.5](https://github.com/compare/...@sage/xtrem~tools@30.0.5) (2023-04-02)

### Bug Fixes

### Features


## [30.0.4](https://github.com/compare/...@sage/xtrem~tools@30.0.4) (2023-04-01)

### Bug Fixes

### Features


## [30.0.3](https://github.com/compare/...@sage/xtrem~tools@30.0.3) (2023-03-31)

### Bug Fixes

### Features
* **communication:** new node SysNotificationState XT-41930 ([XT-41930](https://jira.sage.com/browse/XT-41930)) ([#11406](https://github.com/Sage-ERP-X3/xtrem/issues/11406))   ([5173174](https://github.com/Sage-ERP-X3/xtrem/commit/51731748b06c4cbde137e5c21093b3f31b053358))


## [30.0.2](https://github.com/compare/...@sage/xtrem~tools@30.0.2) (2023-03-30)

### Bug Fixes

### Features


## [30.0.1](https://github.com/compare/...@sage/xtrem~tools@30.0.1) (2023-03-30)

### Bug Fixes

### Features


## [30.0.0](https://github.com/compare/...@sage/xtrem~tools@30.0.0) (2023-03-30)

### Bug Fixes

### Features


## [29.0.39](https://github.com/compare/...@sage/xtrem~tools@29.0.39) (2023-03-30)

### Bug Fixes

### Features


## [29.0.38](https://github.com/compare/...@sage/xtrem~tools@29.0.38) (2023-03-29)

### Bug Fixes

### Features


## [29.0.37](https://github.com/compare/...@sage/xtrem~tools@29.0.37) (2023-03-28)

### Bug Fixes

### Features


## [29.0.36](https://github.com/compare/...@sage/xtrem~tools@29.0.36) (2023-03-27)

### Bug Fixes
* **xtrem-glossary:** fixing usage of hard coded _id for en-US   ([#12014](https://github.com/Sage-ERP-X3/xtrem/issues/12014))   ([337488b](https://github.com/Sage-ERP-X3/xtrem/commit/337488b88bb62f242f3141ac9889020a081a6bf2))

### Features


## [29.0.35](https://github.com/compare/...@sage/xtrem~tools@29.0.35) (2023-03-27)

### Bug Fixes

### Features


## [29.0.34](https://github.com/compare/...@sage/xtrem~tools@29.0.34) (2023-03-24)

### Bug Fixes

### Features


## [29.0.33](https://github.com/compare/...@sage/xtrem~tools@29.0.33) (2023-03-23)

### Bug Fixes

### Features


## [29.0.32](https://github.com/compare/...@sage/xtrem~tools@29.0.32) (2023-03-23)

### Bug Fixes

### Features


## [29.0.31](https://github.com/compare/...@sage/xtrem~tools@29.0.31) (2023-03-22)

### Bug Fixes

### Features
* **xtrem-glossary:** XGL-212 Setting language, product and domain glossary nodes as isSetupNodes and moving the csv files from demo to a new setup layer   ([#11966](https://github.com/Sage-ERP-X3/xtrem/issues/11966))   ([b9209d6](https://github.com/Sage-ERP-X3/xtrem/commit/b9209d6550a415f83088fd3d70c61b02db423b09))


## [29.0.30](https://github.com/compare/...@sage/xtrem~tools@29.0.30) (2023-03-21)

### Bug Fixes

### Features
* XT-43722 Add simulated async mutation ([XT-43722](https://jira.sage.com/browse/XT-43722)) ([#11950](https://github.com/Sage-ERP-X3/xtrem/issues/11950))   ([a375fef](https://github.com/Sage-ERP-X3/xtrem/commit/a375fef8c5ea6a457dac15a5f681c482315a0d3b))
* **xtrem-glossary:** adding masterTermReference to the searchFromTerm graphql api result   ([#11948](https://github.com/Sage-ERP-X3/xtrem/issues/11948))   ([ee9e4f9](https://github.com/Sage-ERP-X3/xtrem/commit/ee9e4f95e54b5fdccacd3db750e4f6bd1962dad6))


## [29.0.29](https://github.com/compare/...@sage/xtrem~tools@29.0.29) (2023-03-20)

### Bug Fixes

### Features


## [29.0.28](https://github.com/compare/...@sage/xtrem~tools@29.0.28) (2023-03-19)

### Bug Fixes

### Features


## [29.0.27](https://github.com/compare/...@sage/xtrem~tools@29.0.27) (2023-03-18)

### Bug Fixes

### Features


## [29.0.26](https://github.com/compare/...@sage/xtrem~tools@29.0.26) (2023-03-17)

### Bug Fixes

### Features


## [29.0.25](https://github.com/compare/...@sage/xtrem~tools@29.0.25) (2023-03-16)

### Bug Fixes

### Features


## [29.0.24](https://github.com/compare/...@sage/xtrem~tools@29.0.24) (2023-03-15)

### Bug Fixes

### Features
* XT-2582 first pass on async mutation stop ([XT-2582](https://jira.sage.com/browse/XT-2582)) ([#11809](https://github.com/Sage-ERP-X3/xtrem/issues/11809))   ([ca3cab7](https://github.com/Sage-ERP-X3/xtrem/commit/ca3cab75301e65a2801d77c9e9338dc0ecede6cd))


## [29.0.23](https://github.com/compare/...@sage/xtrem~tools@29.0.23) (2023-03-15)

### Bug Fixes

### Features
* dedicated package for upload (XT-42599) ([XT-42599](https://jira.sage.com/browse/XT-42599)) ([#11721](https://github.com/Sage-ERP-X3/xtrem/issues/11721))   ([c5b596f](https://github.com/Sage-ERP-X3/xtrem/commit/c5b596f8a1e6e030764742ce20ff918d3eab0556))


## [29.0.22](https://github.com/compare/...@sage/xtrem~tools@29.0.22) (2023-03-13)

### Bug Fixes

### Features
* XT-37312 convert bulk mutation to async mutation ([XT-37312](https://jira.sage.com/browse/XT-37312)) ([#11772](https://github.com/Sage-ERP-X3/xtrem/issues/11772))   ([f4118a9](https://github.com/Sage-ERP-X3/xtrem/commit/f4118a9621c2be23ade0cd7dc38ba110c7295e37))


## [29.0.21](https://github.com/compare/...@sage/xtrem~tools@29.0.21) (2023-03-12)

### Bug Fixes

### Features


## [29.0.20](https://github.com/compare/...@sage/xtrem~tools@29.0.20) (2023-03-11)

### Bug Fixes

### Features


## [29.0.19](https://github.com/compare/...@sage/xtrem~tools@29.0.19) (2023-03-10)

### Bug Fixes

### Features


## [29.0.18](https://github.com/compare/...@sage/xtrem~tools@29.0.18) (2023-03-10)

### Bug Fixes

### Features


## [29.0.17](https://github.com/compare/...@sage/xtrem~tools@29.0.17) (2023-03-08)

### Bug Fixes

### Features
* **metaData:** Pages & replace jobDefinition / scheduler  XT-18415 ([XT-18415](https://jira.sage.com/browse/XT-18415)) ([#11568](https://github.com/Sage-ERP-X3/xtrem/issues/11568))   ([ead2ee4](https://github.com/Sage-ERP-X3/xtrem/commit/ead2ee46cfaa975167da94d5969e123b1380960d))


## [29.0.16](https://github.com/compare/...@sage/xtrem~tools@29.0.16) (2023-03-07)

### Bug Fixes

### Features


## [29.0.15](https://github.com/compare/...@sage/xtrem~tools@29.0.15) (2023-03-07)

### Bug Fixes

### Features


## [29.0.14](https://github.com/compare/...@sage/xtrem~tools@29.0.14) (2023-03-07)

### Bug Fixes

### Features


## [29.0.13](https://github.com/compare/...@sage/xtrem~tools@29.0.13) (2023-03-06)

### Bug Fixes

### Features


## [29.0.12](https://github.com/compare/...@sage/xtrem~tools@29.0.12) (2023-03-05)

### Bug Fixes

### Features


## [29.0.11](https://github.com/compare/...@sage/xtrem~tools@29.0.11) (2023-03-04)

### Bug Fixes

### Features


## [29.0.10](https://github.com/compare/...@sage/xtrem~tools@29.0.10) (2023-03-03)

### Bug Fixes

### Features


## [29.0.9](https://github.com/compare/...@sage/xtrem~tools@29.0.9) (2023-03-02)

### Bug Fixes

### Features


## [29.0.8](https://github.com/compare/...@sage/xtrem~tools@29.0.8) (2023-03-01)

### Bug Fixes

### Features


## [29.0.7](https://github.com/compare/...@sage/xtrem~tools@29.0.7) (2023-02-28)

### Bug Fixes

### Features


## [29.0.6](https://github.com/compare/...@sage/xtrem~tools@29.0.6) (2023-02-27)

### Bug Fixes

### Features


## [29.0.5](https://github.com/compare/...@sage/xtrem~tools@29.0.5) (2023-02-26)

### Bug Fixes

### Features


## [29.0.4](https://github.com/compare/...@sage/xtrem~tools@29.0.4) (2023-02-25)

### Bug Fixes

### Features


## [29.0.3](https://github.com/compare/...@sage/xtrem~tools@29.0.3) (2023-02-24)

### Bug Fixes

### Features


## [29.0.2](https://github.com/compare/...@sage/xtrem~tools@29.0.2) (2023-02-23)

### Bug Fixes

### Features
* XT-42146 fix release-patch ([XT-42146](https://jira.sage.com/browse/XT-42146)) ([#11474](https://github.com/Sage-ERP-X3/xtrem/issues/11474))   ([bdef4b0](https://github.com/Sage-ERP-X3/xtrem/commit/bdef4b06fa1849b65f44da7fc71ef13217db12e4))


## [29.0.1](https://github.com/compare/...@sage/xtrem~tools@29.0.1) (2023-02-23)

### Bug Fixes

### Features


## [29.0.0](https://github.com/compare/...@sage/xtrem~tools@29.0.0) (2023-02-23)

### Bug Fixes

### Features


## [28.0.46](https://github.com/compare/...@sage/xtrem~tools@28.0.46) (2023-02-22)

### Bug Fixes

### Features


## [28.0.45](https://github.com/compare/...@sage/xtrem~tools@28.0.45) (2023-02-22)

### Bug Fixes

### Features


## [28.0.44](https://github.com/compare/...@sage/xtrem~tools@28.0.44) (2023-02-21)

### Bug Fixes

### Features


## [28.0.43](https://github.com/compare/...@sage/xtrem~tools@28.0.43) (2023-02-21)

### Bug Fixes

### Features


## [28.0.42](https://github.com/compare/...@sage/xtrem~tools@28.0.42) (2023-02-20)

### Bug Fixes

### Features


## [28.0.41](https://github.com/compare/...@sage/xtrem~tools@28.0.41) (2023-02-19)

### Bug Fixes

### Features


## [28.0.40](https://github.com/compare/...@sage/xtrem~tools@28.0.40) (2023-02-18)

### Bug Fixes

### Features


## [28.0.39](https://github.com/compare/...@sage/xtrem~tools@28.0.39) (2023-02-17)

### Bug Fixes

### Features


## [28.0.38](https://github.com/compare/...@sage/xtrem~tools@28.0.38) (2023-02-16)

### Bug Fixes

### Features


## [28.0.37](https://github.com/compare/...@sage/xtrem~tools@28.0.37) (2023-02-15)

### Bug Fixes

### Features


## [28.0.36](https://github.com/compare/...@sage/xtrem~tools@28.0.36) (2023-02-14)

### Bug Fixes

### Features


## [28.0.35](https://github.com/compare/...@sage/xtrem~tools@28.0.35) (2023-02-13)

### Bug Fixes

### Features


## [28.0.34](https://github.com/compare/...@sage/xtrem~tools@28.0.34) (2023-02-12)

### Bug Fixes

### Features


## [28.0.33](https://github.com/compare/...@sage/xtrem~tools@28.0.33) (2023-02-11)

### Bug Fixes

### Features


## [28.0.32](https://github.com/compare/...@sage/xtrem~tools@28.0.32) (2023-02-10)

### Bug Fixes

### Features
* XT-40095 rename custom actions ([XT-40095](https://jira.sage.com/browse/XT-40095)) ([#11192](https://github.com/Sage-ERP-X3/xtrem/issues/11192))   ([b5d36d3](https://github.com/Sage-ERP-X3/xtrem/commit/b5d36d3878d47b91d845893898444b30eb54b68f))


## [28.0.31](https://github.com/compare/...@sage/xtrem~tools@28.0.31) (2023-02-09)

### Bug Fixes

### Features


## [28.0.30](https://github.com/compare/...@sage/xtrem~tools@28.0.30) (2023-02-08)

### Bug Fixes

### Features


## [28.0.29](https://github.com/compare/...@sage/xtrem~tools@28.0.29) (2023-02-07)

### Bug Fixes

### Features


## [28.0.28](https://github.com/compare/...@sage/xtrem~tools@28.0.28) (2023-02-06)

### Bug Fixes

### Features


## [28.0.27](https://github.com/compare/...@sage/xtrem~tools@28.0.27) (2023-02-05)

### Bug Fixes

### Features


## [28.0.26](https://github.com/compare/...@sage/xtrem~tools@28.0.26) (2023-02-04)

### Bug Fixes

### Features


## [28.0.25](https://github.com/compare/...@sage/xtrem~tools@28.0.25) (2023-02-03)

### Bug Fixes

### Features


## [28.0.24](https://github.com/compare/...@sage/xtrem~tools@28.0.24) (2023-02-03)

### Bug Fixes

### Features


## [28.0.23](https://github.com/compare/...@sage/xtrem~tools@28.0.23) (2023-02-02)

### Bug Fixes

### Features


## [28.0.22](https://github.com/compare/...@sage/xtrem~tools@28.0.22) (2023-02-01)

### Bug Fixes

### Features


## [28.0.21](https://github.com/compare/...@sage/xtrem~tools@28.0.21) (2023-02-01)

### Bug Fixes

### Features


## [28.0.20](https://github.com/compare/...@sage/xtrem~tools@28.0.20) (2023-01-31)

### Bug Fixes

### Features
* XT-39495 refactor system upgrades ([XT-39495](https://jira.sage.com/browse/XT-39495)) ([#10971](https://github.com/Sage-ERP-X3/xtrem/issues/10971))   ([16fc65e](https://github.com/Sage-ERP-X3/xtrem/commit/16fc65eacd96d03354ca442b39a6d5b4f1099645))


## [28.0.19](https://github.com/compare/...@sage/xtrem~tools@28.0.19) (2023-01-30)

### Bug Fixes

### Features


## [28.0.18](https://github.com/compare/...@sage/xtrem~tools@28.0.18) (2023-01-29)

### Bug Fixes

### Features


## [28.0.17](https://github.com/compare/...@sage/xtrem~tools@28.0.17) (2023-01-28)

### Bug Fixes

### Features


## [28.0.16](https://github.com/compare/...@sage/xtrem~tools@28.0.16) (2023-01-27)

### Bug Fixes

### Features


## [28.0.15](https://github.com/compare/...@sage/xtrem~tools@28.0.15) (2023-01-26)

### Bug Fixes

### Features


## [28.0.14](https://github.com/compare/...@sage/xtrem~tools@28.0.14) (2023-01-25)

### Bug Fixes

### Features


## [28.0.13](https://github.com/compare/...@sage/xtrem~tools@28.0.13) (2023-01-24)

### Bug Fixes
* XT-37411 fix publishing of glossary package ([XT-37411](https://jira.sage.com/browse/XT-37411)) ([#10890](https://github.com/Sage-ERP-X3/xtrem/issues/10890))   ([1d57b88](https://github.com/Sage-ERP-X3/xtrem/commit/1d57b88165483304031aaad64773ce8628f4248f))
* typo in UploadedFile/InfrastructureComplete topic   ([#10891](https://github.com/Sage-ERP-X3/xtrem/issues/10891))   ([2c32010](https://github.com/Sage-ERP-X3/xtrem/commit/2c320102c6ff5ae2b259115b048f445c070edd50))

### Features


## [28.0.12](https://github.com/compare/...@sage/xtrem~tools@28.0.12) (2023-01-23)

### Bug Fixes

### Features


## [28.0.11](https://github.com/compare/...@sage/xtrem~tools@28.0.11) (2023-01-22)

### Bug Fixes

### Features


## [28.0.10](https://github.com/compare/...@sage/xtrem~tools@28.0.10) (2023-01-21)

### Bug Fixes

### Features


## [28.0.9](https://github.com/compare/...@sage/xtrem~tools@28.0.9) (2023-01-20)

### Bug Fixes

### Features


## [28.0.8](https://github.com/compare/...@sage/xtrem~tools@28.0.8) (2023-01-19)

### Bug Fixes

### Features


## [28.0.7](https://github.com/compare/...@sage/xtrem~tools@28.0.7) (2023-01-18)

### Bug Fixes

### Features


## [28.0.6](https://github.com/compare/...@sage/xtrem~tools@28.0.6) (2023-01-17)

### Bug Fixes
* i18n newline at end XT-39254 ([XT-39254](https://jira.sage.com/browse/XT-39254)) ([#10732](https://github.com/Sage-ERP-X3/xtrem/issues/10732))   ([83d79fc](https://github.com/Sage-ERP-X3/xtrem/commit/83d79fce1f6f7c65bd5222ff3129fad8a765940e))

### Features


## [28.0.5](https://github.com/compare/...@sage/xtrem~tools@28.0.5) (2023-01-16)

### Bug Fixes

### Features


## [28.0.4](https://github.com/compare/...@sage/xtrem~tools@28.0.4) (2023-01-15)

### Bug Fixes

### Features


## [28.0.3](https://github.com/compare/...@sage/xtrem~tools@28.0.3) (2023-01-14)

### Bug Fixes

### Features


## [28.0.2](https://github.com/compare/...@sage/xtrem~tools@28.0.2) (2023-01-13)

### Bug Fixes

### Features


## [28.0.1](https://github.com/compare/...@sage/xtrem~tools@28.0.1) (2023-01-12)

### Bug Fixes

### Features
* row actions renamed to dropdown actions XT-37646 ([XT-37646](https://jira.sage.com/browse/XT-37646)) ([#10666](https://github.com/Sage-ERP-X3/xtrem/issues/10666))   ([d63ce88](https://github.com/Sage-ERP-X3/xtrem/commit/d63ce88e68ae8456da3b22beade9f17d32bd059e))


## [28.0.0](https://github.com/compare/...@sage/xtrem~tools@28.0.0) (2023-01-12)

### Bug Fixes

### Features


## [27.0.21](https://github.com/compare/...@sage/xtrem~tools@27.0.21) (2023-01-12)

### Bug Fixes

### Features


## [27.0.20](https://github.com/compare/...@sage/xtrem~tools@27.0.20) (2023-01-11)

### Bug Fixes

### Features


## [27.0.19](https://github.com/compare/...@sage/xtrem~tools@27.0.19) (2023-01-10)

### Bug Fixes

### Features


## [27.0.18](https://github.com/compare/...@sage/xtrem~tools@27.0.18) (2023-01-10)

### Bug Fixes

### Features


## [27.0.17](https://github.com/compare/...@sage/xtrem~tools@27.0.17) (2023-01-09)

### Bug Fixes

### Features


## [27.0.16](https://github.com/compare/...@sage/xtrem~tools@27.0.16) (2023-01-08)

### Bug Fixes

### Features


## [27.0.15](https://github.com/compare/...@sage/xtrem~tools@27.0.15) (2023-01-07)

### Bug Fixes

### Features


## [27.0.14](https://github.com/compare/...@sage/xtrem~tools@27.0.14) (2023-01-06)

### Bug Fixes

### Features


## [27.0.13](https://github.com/compare/...@sage/xtrem~tools@27.0.13) (2023-01-05)

### Bug Fixes

### Features


## [27.0.12](https://github.com/compare/...@sage/xtrem~tools@27.0.12) (2023-01-04)

### Bug Fixes

### Features


## [27.0.11](https://github.com/compare/...@sage/xtrem~tools@27.0.11) (2023-01-03)

### Bug Fixes
* security patches XT-38525 ([XT-38525](https://jira.sage.com/browse/XT-38525)) ([#10489](https://github.com/Sage-ERP-X3/xtrem/issues/10489))   ([d2b29fd](https://github.com/Sage-ERP-X3/xtrem/commit/d2b29fd2a6cb4dd1c87d75135a06b1997740e4a7))

### Features


## [27.0.10](https://github.com/compare/...@sage/xtrem~tools@27.0.10) (2023-01-02)

### Bug Fixes

### Features


## [27.0.9](https://github.com/compare/...@sage/xtrem~tools@27.0.9) (2023-01-01)

### Bug Fixes

### Features


## [27.0.8](https://github.com/compare/...@sage/xtrem~tools@27.0.8) (2022-12-31)

### Bug Fixes

### Features


## [27.0.7](https://github.com/compare/...@sage/xtrem~tools@27.0.7) (2022-12-30)

### Bug Fixes

### Features


## [27.0.6](https://github.com/compare/...@sage/xtrem~tools@27.0.6) (2022-12-29)

### Bug Fixes

### Features


## [27.0.5](https://github.com/compare/...@sage/xtrem~tools@27.0.5) (2022-12-28)

### Bug Fixes

### Features


## [27.0.4](https://github.com/compare/...@sage/xtrem~tools@27.0.4) (2022-12-27)

### Bug Fixes

### Features


## [27.0.3](https://github.com/compare/...@sage/xtrem~tools@27.0.3) (2022-12-26)

### Bug Fixes

### Features


## [27.0.2](https://github.com/compare/...@sage/xtrem~tools@27.0.2) (2022-12-26)

### Bug Fixes

### Features


## [27.0.1](https://github.com/compare/...@sage/xtrem~tools@27.0.1) (2022-12-23)

### Bug Fixes

### Features


## [27.0.0](https://github.com/compare/...@sage/xtrem~tools@27.0.0) (2022-12-23)

### Bug Fixes

### Features


## [26.0.28](https://github.com/compare/...@sage/xtrem~tools@26.0.28) (2022-12-22)

### Bug Fixes

### Features


## [26.0.27](https://github.com/compare/...@sage/xtrem~tools@26.0.27) (2022-12-21)

### Bug Fixes

### Features


## [26.0.26](https://github.com/compare/...@sage/xtrem~tools@26.0.26) (2022-12-20)

### Bug Fixes

### Features


## [26.0.25](https://github.com/compare/...@sage/xtrem~tools@26.0.25) (2022-12-20)

### Bug Fixes

### Features


## [26.0.24](https://github.com/compare/...@sage/xtrem~tools@26.0.24) (2022-12-18)

### Bug Fixes

### Features


## [26.0.23](https://github.com/compare/...@sage/xtrem~tools@26.0.23) (2022-12-17)

### Bug Fixes

### Features


## [26.0.22](https://github.com/compare/...@sage/xtrem~tools@26.0.22) (2022-12-16)

### Bug Fixes

### Features


## [26.0.21](https://github.com/compare/...@sage/xtrem~tools@26.0.21) (2022-12-15)

### Bug Fixes

### Features


## [26.0.20](https://github.com/compare/...@sage/xtrem~tools@26.0.20) (2022-12-14)

### Bug Fixes

### Features
* **core:** XT-36830 - Tsconfig-paths version upgrade. ([XT-36830](https://jira.sage.com/browse/XT-36830)) ([#10287](https://github.com/Sage-ERP-X3/xtrem/issues/10287))   ([47ca0b4](https://github.com/Sage-ERP-X3/xtrem/commit/47ca0b4061872b9212b736035f3f9944b28e382d))
* **xtrem-cli:** XT-37241 - Tests refactoring & step definition deprecation ([XT-37241](https://jira.sage.com/browse/XT-37241)) ([#10188](https://github.com/Sage-ERP-X3/xtrem/issues/10188))   ([dba0127](https://github.com/Sage-ERP-X3/xtrem/commit/dba012717f7f61d24502a2c798afe069d115ced7))


## [26.0.19](https://github.com/compare/...@sage/xtrem~tools@26.0.19) (2022-12-13)

### Bug Fixes

### Features


## [26.0.18](https://github.com/compare/...@sage/xtrem~tools@26.0.18) (2022-12-12)

### Bug Fixes
* **scripts:** XT-34673 - improve changelog update speed ([XT-34673](https://jira.sage.com/browse/XT-34673)) ([#10038](https://github.com/Sage-ERP-X3/xtrem/issues/10038))   ([863033a](https://github.com/Sage-ERP-X3/xtrem/commit/863033a16edab142c8d7d9e6f14c5554979964a0))

### Features
* new action structure XT-28176 ([XT-28176](https://jira.sage.com/browse/XT-28176)) ([#10215](https://github.com/Sage-ERP-X3/xtrem/issues/10215))   ([4de5cbf](https://github.com/Sage-ERP-X3/xtrem/commit/4de5cbff5c2c9b9889515a86bc9ac080fcc59909))


## [26.0.17](https://github.com/compare/...@sage/xtrem~tools@26.0.17) (2022-12-11)

### Bug Fixes

### Features

## [26.0.16](https://github.com/compare/...@sage/xtrem~tools@26.0.16) (2022-12-10)

### Bug Fixes

### Features

## [26.0.15](https://github.com/compare/...@sage/xtrem~tools@26.0.15) (2022-12-09)

### Bug Fixes
* XT-37290 refactor xtrem-communication and xtrem-routing ([XT-37290](https://jira.sage.com/browse/XT-37290)) ([#10239](https://github.com/Sage-ERP-X3/xtrem/issues/10239))   ([9100674](https://github.com/Sage-ERP-X3/xtrem/commit/9100674d0f939b2b2c369d47e53fe5871d7fc1f9))

### Features

## [26.0.14](https://github.com/compare/...@sage/xtrem~tools@26.0.14) (2022-12-08)

### Bug Fixes

### Features

## [26.0.13](https://github.com/compare/...@sage/xtrem~tools@26.0.13) (2022-12-08)

### Bug Fixes
* integrity constraint violation message XT-35778 ([XT-35778](https://jira.sage.com/browse/XT-35778)) ([#10181](https://github.com/Sage-ERP-X3/xtrem/issues/10181))   ([a925cb4](https://github.com/Sage-ERP-X3/xtrem/commit/a925cb46064aa14f67e957a45265a8b5febe6030))

### Features

## [26.0.12](https://github.com/compare/...@sage/xtrem~tools@26.0.12) (2022-12-06)

### Bug Fixes

### Features

## [26.0.11](https://github.com/compare/...@sage/xtrem~tools@26.0.11) (2022-12-05)

### Bug Fixes

### Features

## [26.0.10](https://github.com/compare/...@sage/xtrem~tools@26.0.10) (2022-12-04)

### Bug Fixes

### Features

## [26.0.9](https://github.com/compare/...@sage/xtrem~tools@26.0.9) (2022-12-03)

### Bug Fixes

### Features

## [26.0.8](https://github.com/compare/...@sage/xtrem~tools@26.0.8) (2022-12-02)

### Bug Fixes

### Features

## [26.0.7](https://github.com/compare/...@sage/xtrem~tools@26.0.7) (2022-12-02)

### Bug Fixes

### Features

## [26.0.6](https://github.com/compare/...@sage/xtrem~tools@26.0.6) (2022-12-01)

### Bug Fixes

### Features

## [26.0.5](https://github.com/compare/...@sage/xtrem~tools@26.0.5) (2022-11-30)

### Bug Fixes

### Features

## [26.0.4](https://github.com/compare/...@sage/xtrem~tools@26.0.4) (2022-11-29)

### Bug Fixes

### Features

## [26.0.3](https://github.com/compare/...@sage/xtrem~tools@26.0.3) (2022-11-29)

### Bug Fixes

### Features

## [26.0.2](https://github.com/compare/...@sage/xtrem~tools@26.0.2) (2022-11-28)

### Bug Fixes

### Features

## [26.0.1](https://github.com/compare/...@sage/xtrem~tools@26.0.1) (2022-11-28)

### Bug Fixes

### Features

## [25.0.32](https://github.com/compare/...@sage/xtrem~tools@25.0.32) (2022-11-28)

### Bug Fixes

### Features

## [25.0.31](https://github.com/compare/...@sage/xtrem~tools@25.0.31) (2022-11-27)

### Bug Fixes

### Features

## [25.0.30](https://github.com/compare/...@sage/xtrem~tools@25.0.30) (2022-11-26)

### Bug Fixes

### Features

## [25.0.29](https://github.com/compare/...@sage/xtrem~tools@25.0.29) (2022-11-25)

### Bug Fixes

### Features

## [25.0.28](https://github.com/compare/...@sage/xtrem~tools@25.0.28) (2022-11-25)

### Bug Fixes

### Features

## [25.0.27](https://github.com/compare/...@sage/xtrem~tools@25.0.27) (2022-11-24)

### Bug Fixes

### Features

## [25.0.26](https://github.com/compare/...@sage/xtrem~tools@25.0.26) (2022-11-22)

### Bug Fixes

### Features

## [25.0.25](https://github.com/compare/...@sage/xtrem~tools@25.0.25) (2022-11-21)

### Bug Fixes
* glossary/xgl 217 Admin reading id fix   ([#9946](https://github.com/Sage-ERP-X3/xtrem/issues/9946))   ([0800715](https://github.com/Sage-ERP-X3/xtrem/commit/080071595fef9880010e3be5d0f804684af69876))

### Features
* XT-35978 refactor the way SQL inserts are built ([XT-35978](https://jira.sage.com/browse/XT-35978)) ([#9901](https://github.com/Sage-ERP-X3/xtrem/issues/9901))   ([1def1f5](https://github.com/Sage-ERP-X3/xtrem/commit/1def1f5ec8584125a8bd175822f89f4e61294f27))

## [25.0.24](https://github.com/compare/...@sage/xtrem~tools@25.0.24) (2022-11-20)

### Bug Fixes

### Features

## [25.0.23](https://github.com/compare/...@sage/xtrem~tools@25.0.23) (2022-11-19)

### Bug Fixes

### Features

## [25.0.22](https://github.com/compare/...@sage/xtrem~tools@25.0.22) (2022-11-18)

### Bug Fixes

### Features

## [25.0.21](https://github.com/compare/...@sage/xtrem~tools@25.0.21) (2022-11-17)

### Bug Fixes

### Features

## [25.0.20](https://github.com/compare/...@sage/xtrem~tools@25.0.20) (2022-11-16)

### Bug Fixes

### Features

## [25.0.19](https://github.com/compare/...@sage/xtrem~tools@25.0.19) (2022-11-15)

### Bug Fixes

### Features

## [25.0.18](https://github.com/compare/...@sage/xtrem~tools@25.0.18) (2022-11-14)

### Bug Fixes

### Features

## [25.0.17](https://github.com/compare/...@sage/xtrem~tools@25.0.17) (2022-11-13)

### Bug Fixes

### Features

## [25.0.16](https://github.com/compare/...@sage/xtrem~tools@25.0.16) (2022-11-12)

### Bug Fixes

### Features

## [25.0.15](https://github.com/compare/...@sage/xtrem~tools@25.0.15) (2022-11-11)

### Bug Fixes

### Features

## [25.0.14](https://github.com/compare/...@sage/xtrem~tools@25.0.14) (2022-11-10)

### Bug Fixes
* glossary/xgl 217 search reading id fix   ([#9730](https://github.com/Sage-ERP-X3/xtrem/issues/9730))   ([ffa4cd1](https://github.com/Sage-ERP-X3/xtrem/commit/ffa4cd15a9d701a3e0166aea2bb5e58e0ead2ca5))
* adjusts select record call after refactor XT-35532 ([XT-35532](https://jira.sage.com/browse/XT-35532)) ([#9792](https://github.com/Sage-ERP-X3/xtrem/issues/9792))   ([274cb9b](https://github.com/Sage-ERP-X3/xtrem/commit/274cb9bcd34828e1bb78f7ece81755768eb0876d))

### Features
* node names and properties extracted to translation files   ([#4933](https://github.com/Sage-ERP-X3/xtrem/issues/4933))   ([8e857d8](https://github.com/Sage-ERP-X3/xtrem/commit/8e857d841afb7a6f4a79000d21eb2e762c6c1c22))

## [25.0.13](https://github.com/compare/...@sage/xtrem~tools@25.0.13) (2022-11-08)

### Bug Fixes

### Features

## [25.0.12](https://github.com/compare/...@sage/xtrem~tools@25.0.12) (2022-11-07)

### Bug Fixes

### Features

## [25.0.11](https://github.com/compare/...@sage/xtrem~tools@25.0.11) (2022-11-06)

### Bug Fixes

### Features

## [25.0.10](https://github.com/compare/...@sage/xtrem~tools@25.0.10) (2022-11-05)

### Bug Fixes

### Features

## [25.0.9](https://github.com/compare/...@sage/xtrem~tools@25.0.9) (2022-11-04)

### Bug Fixes

### Features

## [25.0.8](https://github.com/compare/...@sage/xtrem~tools@25.0.8) (2022-11-03)

### Bug Fixes

### Features

## [25.0.7](https://github.com/compare/...@sage/xtrem~tools@25.0.7) (2022-11-02)

### Bug Fixes

### Features

## [25.0.6](https://github.com/compare/...@sage/xtrem~tools@25.0.6) (2022-11-01)

### Bug Fixes
* **core:** XT-33389 - added diagnoses to error object ([XT-33389](https://jira.sage.com/browse/XT-33389)) ([#9181](https://github.com/Sage-ERP-X3/xtrem/issues/9181))   ([1a4e00e](https://github.com/Sage-ERP-X3/xtrem/commit/1a4e00e4b18157e07bf54a68521b0b9ab9179818))

### Features

## [25.0.5](https://github.com/compare/...@sage/xtrem~tools@25.0.5) (2022-10-31)

### Bug Fixes

### Features

## [25.0.4](https://github.com/compare/...@sage/xtrem~tools@25.0.4) (2022-10-31)

### Bug Fixes

### Features

## [25.0.3](https://github.com/compare/...@sage/xtrem~tools@25.0.3) (2022-10-30)

### Bug Fixes

### Features
* lint deprecated step definitions in feature files   ([#9556](https://github.com/Sage-ERP-X3/xtrem/issues/9556))   ([5351708](https://github.com/Sage-ERP-X3/xtrem/commit/5351708b9e5744a2294dc8b32eea49dd72e589a3))

## [25.0.2](https://github.com/compare/...@sage/xtrem~tools@25.0.2) (2022-10-29)

### Bug Fixes

### Features

## [25.0.1](https://github.com/compare/...@sage/xtrem~tools@25.0.1) (2022-10-28)

### Bug Fixes

### Features

## [24.0.20](https://github.com/compare/...@sage/xtrem~tools@24.0.20) (2022-10-27)

### Bug Fixes
* realign mocha package.json (XT-34837) ([XT-34837](https://jira.sage.com/browse/XT-34837)) ([#9579](https://github.com/Sage-ERP-X3/xtrem/issues/9579))   ([cd5d2b4](https://github.com/Sage-ERP-X3/xtrem/commit/cd5d2b4c8d402e96cdc84dda790facc617780058))

### Features

## [24.0.19](https://github.com/compare/...@sage/xtrem~tools@24.0.19) (2022-10-26)

### Bug Fixes

### Features

## [24.0.18](https://github.com/compare/...@sage/xtrem~tools@24.0.18) (2022-10-25)

### Bug Fixes

### Features

## [24.0.17](https://github.com/compare/...@sage/xtrem~tools@24.0.17) (2022-10-24)

### Bug Fixes

### Features

## [24.0.16](https://github.com/compare/...@sage/xtrem~tools@24.0.16) (2022-10-23)

### Bug Fixes

### Features

## [24.0.15](https://github.com/compare/...@sage/xtrem~tools@24.0.15) (2022-10-22)

### Bug Fixes

### Features

## [24.0.14](https://github.com/compare/...@sage/xtrem~tools@24.0.14) (2022-10-21)

### Bug Fixes

### Features

## [24.0.13](https://github.com/compare/...@sage/xtrem~tools@24.0.13) (2022-10-20)

### Bug Fixes
* XT-34721 reload missing data on already upgraded clusters ([XT-34721](https://jira.sage.com/browse/XT-34721)) ([#9475](https://github.com/Sage-ERP-X3/xtrem/issues/9475))   ([097514d](https://github.com/Sage-ERP-X3/xtrem/commit/097514dde87c65347d39bf932064f21aa71cec19))

### Features

## [24.0.12](https://github.com/compare/...@sage/xtrem~tools@24.0.12) (2022-10-20)

### Bug Fixes

### Features

## [24.0.11](https://github.com/compare/...@sage/xtrem~tools@24.0.11) (2022-10-19)

### Bug Fixes

### Features

## [24.0.10](https://github.com/compare/...@sage/xtrem~tools@24.0.10) (2022-10-19)

### Bug Fixes

### Features

## [24.0.9](https://github.com/compare/...@sage/xtrem~tools@24.0.9) (2022-10-18)

### Bug Fixes
* XT-34634 fix shopfloor packages and realign packages from 24.0.7 to 24.0.8 ([XT-34634](https://jira.sage.com/browse/XT-34634)) ([#9406](https://github.com/Sage-ERP-X3/xtrem/issues/9406))   ([f4713c3](https://github.com/Sage-ERP-X3/xtrem/commit/f4713c3c56f794f8e663fe49881c93c96f941478))

### Features

## [24.0.8](https://github.com/compare/...@sage/xtrem~tools@24.0.8) (2022-10-18)

### Bug Fixes

* XT-34634 fix shopfloor packages and realign packages from 24.0.7 to 24.0.8 ([#9406](https://github.com/issues/9406))  ([f4713c3](https://github.com/commit/f4713c3c56f794f8e663fe49881c93c96f941478))

### Features


# [24.0.6](https://github.com/compare/...@sage/xtrem~tools@24.0.6) (2022-10-16)

### Bug Fixes


### Features


# [24.0.5](https://github.com/compare/...@sage/xtrem~tools@24.0.5) (2022-10-15)

### Bug Fixes


### Features


# [24.0.4](https://github.com/compare/...@sage/xtrem~tools@24.0.4) (2022-10-15)

### Bug Fixes

* XT-34323 order queues in elasticmq conf ([#9305](https://github.com/issues/9305))  ([fa6354e](https://github.com/commit/fa6354e9004afe534de7fed31b5cf9861da0be64))

### Features

* XT-30067 remove main filter on elasticmq generation ([#9366](https://github.com/issues/9366))  ([05dee0f](https://github.com/commit/05dee0fdfa835b68f68e5b8b72c66ba57eb871c1))

# [24.0.3](https://github.com/compare/...@sage/xtrem~tools@24.0.3) (2022-10-11)

### Bug Fixes


### Features


# [24.0.2](https://github.com/compare/...@sage/xtrem~tools@24.0.2) (2022-10-11)

### Bug Fixes


### Features


# [24.0.1](https://github.com/compare/...@sage/xtrem~tools@24.0.1) (2022-10-11)

### Bug Fixes


### Features

* XT-33019 generate elasticmq config per each main package ([#9239](https://github.com/issues/9239))  ([0ca7636](https://github.com/commit/0ca763683d0e799376c5919d4548702a876a7c8c))
* XT-33019 include generic notification queues in the generation of the elasticmq.conf file ([#9253](https://github.com/issues/9253))  ([ecf80e1](https://github.com/commit/ecf80e1ab130f4e2b38e7748a5fcd2fd046e8149))

# [24.0.0](https://github.com/compare/...@sage/xtrem~tools@24.0.0) (2022-09-29)

### Bug Fixes


### Features


# [23.0.48](https://github.com/compare/...@sage/xtrem~tools@23.0.48) (2022-09-29)

### Bug Fixes


### Features


# [23.0.47](https://github.com/compare/...@sage/xtrem~tools@23.0.47) (2022-09-29)

### Bug Fixes


### Features


# [23.0.46](https://github.com/compare/...@sage/xtrem~tools@23.0.46) (2022-09-28)

### Bug Fixes


### Features


# [23.0.45](https://github.com/compare/...@sage/xtrem~tools@23.0.45) (2022-09-28)

### Bug Fixes


### Features


# [23.0.44](https://github.com/compare/...@sage/xtrem~tools@23.0.44) (2022-09-26)

### Bug Fixes


### Features


# [23.0.43](https://github.com/compare/...@sage/xtrem~tools@23.0.43) (2022-09-25)

### Bug Fixes


### Features


# [23.0.42](https://github.com/compare/...@sage/xtrem~tools@23.0.42) (2022-09-24)

### Bug Fixes


### Features


# [23.0.41](https://github.com/compare/...@sage/xtrem~tools@23.0.41) (2022-09-23)

### Bug Fixes


### Features


# [23.0.40](https://github.com/compare/...@sage/xtrem~tools@23.0.40) (2022-09-22)

### Bug Fixes


### Features


# [23.0.39](https://github.com/compare/...@sage/xtrem~tools@23.0.39) (2022-09-21)

### Bug Fixes


### Features


# [23.0.38](https://github.com/compare/...@sage/xtrem~tools@23.0.38) (2022-09-21)

### Bug Fixes


### Features


# [23.0.37](https://github.com/compare/...@sage/xtrem~tools@23.0.37) (2022-09-21)

### Bug Fixes


### Features


# [23.0.36](https://github.com/compare/...@sage/xtrem~tools@23.0.36) (2022-09-19)

### Bug Fixes


### Features


# [23.0.35](https://github.com/compare/...@sage/xtrem~tools@23.0.35) (2022-09-19)

### Bug Fixes


### Features


# [23.0.34](https://github.com/compare/...@sage/xtrem~tools@23.0.34) (2022-09-19)

### Bug Fixes


### Features


# [23.0.33](https://github.com/compare/...@sage/xtrem~tools@23.0.33) (2022-09-19)

### Bug Fixes


### Features


# [23.0.32](https://github.com/compare/...@sage/xtrem~tools@23.0.32) (2022-09-18)

### Bug Fixes


### Features


# [23.0.31](https://github.com/compare/...@sage/xtrem~tools@23.0.31) (2022-09-18)

### Bug Fixes


### Features


# [23.0.30](https://github.com/compare/...@sage/xtrem~tools@23.0.30) (2022-09-18)

### Bug Fixes


### Features


# [23.0.29](https://github.com/compare/...@sage/xtrem~tools@23.0.29) (2022-09-18)

### Bug Fixes


### Features


# [23.0.28](https://github.com/compare/...@sage/xtrem~tools@23.0.28) (2022-09-17)

### Bug Fixes


### Features


# [23.0.27](https://github.com/compare/...@sage/xtrem~tools@23.0.27) (2022-09-17)

### Bug Fixes


### Features


# [23.0.26](https://github.com/compare/...@sage/xtrem~tools@23.0.26) (2022-09-16)

### Bug Fixes


### Features


# [23.0.25](https://github.com/compare/...@sage/xtrem~tools@23.0.25) (2022-09-16)

### Bug Fixes


### Features


# [23.0.24](https://github.com/compare/...@sage/xtrem~tools@23.0.24) (2022-09-16)

### Bug Fixes


### Features


# [23.0.23](https://github.com/compare/...@sage/xtrem~tools@23.0.23) (2022-09-16)

### Bug Fixes


### Features

* XT-26111 async import mutations and listeners ([#8795](https://github.com/issues/8795))  ([50846bd](https://github.com/commit/50846bdcdbf289db502eee38f45bc42768d5997b))

# [23.0.22](https://github.com/compare/...@sage/xtrem~tools@23.0.22) (2022-09-14)

### Bug Fixes


### Features


# [23.0.21](https://github.com/compare/...@sage/xtrem~tools@23.0.21) (2022-09-14)

### Bug Fixes


### Features


# [23.0.20](https://github.com/compare/...@sage/xtrem~tools@23.0.20) (2022-09-12)

### Bug Fixes


### Features


# [23.0.19](https://github.com/compare/...@sage/xtrem~tools@23.0.19) (2022-09-12)

### Bug Fixes


### Features


# [23.0.18](https://github.com/compare/...@sage/xtrem~tools@23.0.18) (2022-09-11)

### Bug Fixes


### Features


# [23.0.17](https://github.com/compare/...@sage/xtrem~tools@23.0.17) (2022-09-10)

### Bug Fixes


### Features


# [23.0.16](https://github.com/compare/...@sage/xtrem~tools@23.0.16) (2022-09-09)

### Bug Fixes


### Features


# [23.0.15](https://github.com/compare/...@sage/xtrem~tools@23.0.15) (2022-09-09)

### Bug Fixes


### Features


# [23.0.14](https://github.com/compare/...@sage/xtrem~tools@23.0.14) (2022-09-07)

### Bug Fixes


### Features


# [23.0.13](https://github.com/compare/...@sage/xtrem~tools@23.0.13) (2022-09-07)

### Bug Fixes


### Features


# [23.0.12](https://github.com/compare/...@sage/xtrem~tools@23.0.12) (2022-09-06)

### Bug Fixes


### Features


# [23.0.11](https://github.com/compare/...@sage/xtrem~tools@23.0.11) (2022-09-05)

### Bug Fixes


### Features


# [23.0.10](https://github.com/compare/...@sage/xtrem~tools@23.0.10) (2022-09-05)

### Bug Fixes


### Features


# [23.0.9](https://github.com/compare/...@sage/xtrem~tools@23.0.9) (2022-09-04)

### Bug Fixes


### Features


# [23.0.8](https://github.com/compare/...@sage/xtrem~tools@23.0.8) (2022-09-03)

### Bug Fixes


### Features


# [23.0.7](https://github.com/compare/...@sage/xtrem~tools@23.0.7) (2022-09-02)

### Bug Fixes


### Features

* XT-26109 split routing service from communication and implement infrastructure queue and listener ([#8265](https://github.com/issues/8265))  ([9981e3c](https://github.com/commit/9981e3c338896f9ca2f9bfaa43f567a6844eca82))

# [23.0.6](https://github.com/compare/...@sage/xtrem~tools@23.0.6) (2022-09-01)

### Bug Fixes


### Features


# [23.0.5](https://github.com/compare/...@sage/xtrem~tools@23.0.5) (2022-08-31)

### Bug Fixes


### Features


# [23.0.4](https://github.com/compare/...@sage/xtrem~tools@23.0.4) (2022-08-30)

### Bug Fixes


### Features


# [23.0.3](https://github.com/compare/...@sage/xtrem~tools@23.0.3) (2022-08-30)

### Bug Fixes


### Features


# [23.0.2](https://github.com/compare/...@sage/xtrem~tools@23.0.2) (2022-08-29)

### Bug Fixes


### Features


# [23.0.1](https://github.com/compare/...@sage/xtrem~tools@23.0.1) (2022-08-29)

### Bug Fixes

* XT-31675 fix Glossary SQL JSON file naming ([#8585](https://github.com/issues/8585))  ([dc32b8e](https://github.com/commit/dc32b8ed10b4b3ba1a9021ddafb314756cfc9d1a))

### Features


# [23.0.0](https://github.com/compare/...@sage/xtrem~tools@23.0.0) (2022-08-26)

### Bug Fixes

* XT-31675 fix package versions and sql file naming on upgrades ([#8573](https://github.com/issues/8573))  ([486cdca](https://github.com/commit/486cdcad6c95797d0238dca41af00c109d890a4f))
* **basalt:** XT-30475 Fix release in progress ([#8564](https://github.com/issues/8564))  ([cb3bbaa](https://github.com/commit/cb3bbaa412b2aaca30deee9e52d09299558eda90))

### Features


# [22.0.25](https://github.com/compare/...@sage/xtrem~tools@22.0.25) (2022-08-25)

### Bug Fixes


### Features


# [22.0.24](https://github.com/compare/...@sage/xtrem~tools@22.0.24) (2022-08-24)

### Bug Fixes


### Features


# [22.0.23](https://github.com/compare/...@sage/xtrem~tools@22.0.23) (2022-08-23)

### Bug Fixes


### Features


# [22.0.22](https://github.com/compare/...@sage/xtrem~tools@22.0.22) (2022-08-22)

### Bug Fixes


### Features


# [22.0.21](https://github.com/compare/...@sage/xtrem~tools@22.0.21) (2022-08-21)

### Bug Fixes


### Features


# [22.0.20](https://github.com/compare/...@sage/xtrem~tools@22.0.20) (2022-08-19)

### Bug Fixes


### Features


# [22.0.19](https://github.com/compare/...@sage/xtrem~tools@22.0.19) (2022-08-18)

### Bug Fixes


### Features


# [22.0.18](https://github.com/compare/...@sage/xtrem~tools@22.0.18) (2022-08-17)

### Bug Fixes


### Features

* **glossary:** XGL-201 glossary unique id property / upgrade script ([#8304](https://github.com/issues/8304))  ([822a7e1](https://github.com/commit/822a7e10935532d38b11961a801793953261b1a2))

# [22.0.17](https://github.com/compare/...@sage/xtrem~tools@22.0.17) (2022-08-16)

### Bug Fixes


### Features


# [22.0.16](https://github.com/compare/...@sage/xtrem~tools@22.0.16) (2022-08-15)

### Bug Fixes

* XT-30974 gix demo data on glossary for vitall collection nodes (missing _sort_value) ([#8401](https://github.com/issues/8401))  ([7298e73](https://github.com/commit/7298e7381a2c9a1d277ba82304a2bd02a20c56f9))

### Features


# [22.0.15](https://github.com/compare/...@sage/xtrem~tools@22.0.15) (2022-08-14)

### Bug Fixes


### Features


# [22.0.14](https://github.com/compare/...@sage/xtrem~tools@22.0.14) (2022-08-13)

### Bug Fixes


### Features


# [22.0.13](https://github.com/compare/...@sage/xtrem~tools@22.0.13) (2022-08-11)

### Bug Fixes


### Features


# [22.0.12](https://github.com/compare/...@sage/xtrem~tools@22.0.12) (2022-08-11)

### Bug Fixes

* XT-30630 relaod data in JSON instead of system-upgrade ([#8360](https://github.com/issues/8360))  ([6c6d238](https://github.com/commit/6c6d2387725cd8bf8c737c01dbdab2148953f9f7))

### Features

* **glossary:** XGL-209 Update content for GL 2022 R4 release ([#8339](https://github.com/issues/8339))  ([ca4dd89](https://github.com/commit/ca4dd89747d95a3e143b8aebeadbf5de0beba490))

# [22.0.11](https://github.com/compare/...@sage/xtrem~tools@22.0.11) (2022-08-09)

### Bug Fixes


### Features


# [22.0.10](https://github.com/compare/...@sage/xtrem~tools@22.0.10) (2022-08-08)

### Bug Fixes


### Features


# [22.0.9](https://github.com/compare/...@sage/xtrem~tools@22.0.9) (2022-08-07)

### Bug Fixes


### Features


# [22.0.8](https://github.com/compare/...@sage/xtrem~tools@22.0.8) (2022-08-06)

### Bug Fixes


### Features


# [22.0.7](https://github.com/compare/...@sage/xtrem~tools@22.0.7) (2022-08-05)

### Bug Fixes


### Features


# [22.0.6](https://github.com/compare/...@sage/xtrem~tools@22.0.6) (2022-08-04)

### Bug Fixes

* XT-30630 fix release patch issue upgrading glossary ([#8282](https://github.com/issues/8282))  ([3688f20](https://github.com/commit/3688f20ab763d318369bbbde98cfd411f6fbc279))
* XT-30630 remove isUnique from glossary node ([#8289](https://github.com/issues/8289))  ([3a34d59](https://github.com/commit/3a34d59c8b4cf0d3dcbf15c0ee336ab9d70cd56a))

### Features

* **glossary:** XGL-200 glossary unique id property ([#8231](https://github.com/issues/8231))  ([c5ed15e](https://github.com/commit/c5ed15edf8665a8df399538e8453516dc7a0987c))

# [22.0.5](https://github.com/compare/...@sage/xtrem~tools@22.0.5) (2022-08-01)

### Bug Fixes


### Features


# [22.0.4](https://github.com/compare/...@sage/xtrem~tools@22.0.4) (2022-07-31)

### Bug Fixes


### Features


# [22.0.3](https://github.com/compare/...@sage/xtrem~tools@22.0.3) (2022-07-30)

### Bug Fixes


### Features


# [22.0.2](https://github.com/compare/...@sage/xtrem~tools@22.0.2) (2022-07-29)

### Bug Fixes


### Features

* XT-25837 fix sha1 in SQL file  ([6d9482d](https://github.com/commit/6d9482d01fb5933c7d8224f0c43dbed0fc77f94b))
* XT-25837 upgrade for any application ([#8061](https://github.com/issues/8061))  ([1cf102b](https://github.com/commit/1cf102b8714006eb931abb73c619b3bc76be45f3))

# [22.0.1](https://github.com/compare/...@sage/xtrem~tools@22.0.1) (2022-07-28)

### Bug Fixes


### Features


# [22.0.0](https://github.com/compare/...@sage/xtrem~tools@22.0.0) (2022-07-28)

### Bug Fixes


### Features


# [21.0.36](https://github.com/compare/...@sage/xtrem~tools@21.0.36) (2022-07-28)

### Bug Fixes


### Features


# [21.0.35](https://github.com/compare/...@sage/xtrem~tools@21.0.35) (2022-07-27)

### Bug Fixes


### Features


# [21.0.34](https://github.com/compare/...@sage/xtrem~tools@21.0.34) (2022-07-26)

### Bug Fixes


### Features


# [21.0.33](https://github.com/compare/...@sage/xtrem~tools@21.0.33) (2022-07-25)

### Bug Fixes


### Features

* aurora v2 - PostgreSQL 13.6 (XT-28146) ([#8094](https://github.com/issues/8094))  ([fdb9c00](https://github.com/commit/fdb9c00c493f1ab9b399436e877a9704ac36e0b5))

# [21.0.32](https://github.com/compare/...@sage/xtrem~tools@21.0.32) (2022-07-24)

### Bug Fixes


### Features


# [21.0.31](https://github.com/compare/...@sage/xtrem~tools@21.0.31) (2022-07-23)

### Bug Fixes


### Features


# [21.0.30](https://github.com/compare/...@sage/xtrem~tools@21.0.30) (2022-07-22)

### Bug Fixes


### Features


# [21.0.29](https://github.com/compare/...@sage/xtrem~tools@21.0.29) (2022-07-21)

### Bug Fixes


### Features


# [21.0.28](https://github.com/compare/...@sage/xtrem~tools@21.0.28) (2022-07-20)

### Bug Fixes


### Features


# [21.0.27](https://github.com/compare/...@sage/xtrem~tools@21.0.27) (2022-07-19)

### Bug Fixes


### Features


# [21.0.26](https://github.com/compare/...@sage/xtrem~tools@21.0.26) (2022-07-19)

### Bug Fixes


### Features


# [21.0.25](https://github.com/compare/...@sage/xtrem~tools@21.0.25) (2022-07-18)

### Bug Fixes


### Features


# [21.0.24](https://github.com/compare/...@sage/xtrem~tools@21.0.24) (2022-07-17)

### Bug Fixes


### Features


# [21.0.23](https://github.com/compare/...@sage/xtrem~tools@21.0.23) (2022-07-16)

### Bug Fixes


### Features


# [21.0.22](https://github.com/compare/...@sage/xtrem~tools@21.0.22) (2022-07-15)

### Bug Fixes


### Features


# [21.0.21](https://github.com/compare/...@sage/xtrem~tools@21.0.21) (2022-07-14)

### Bug Fixes


### Features


# [21.0.20](https://github.com/compare/...@sage/xtrem~tools@21.0.20) (2022-07-14)

### Bug Fixes


### Features


# [21.0.19](https://github.com/compare/...@sage/xtrem~tools@21.0.19) (2022-07-13)

### Bug Fixes

* **glossary:** XGL-128 hard refresh page after deletion ([#7964](https://github.com/issues/7964))  ([90ff696](https://github.com/commit/90ff696f3bab73af10ae1a397456d2548b6d40e2))

### Features


# [21.0.18](https://github.com/compare/...@sage/xtrem~tools@21.0.18) (2022-07-13)

### Bug Fixes


### Features


# [21.0.17](https://github.com/compare/...@sage/xtrem~tools@21.0.17) (2022-07-10)

### Bug Fixes


### Features


# [21.0.16](https://github.com/compare/...@sage/xtrem~tools@21.0.16) (2022-07-09)

### Bug Fixes


### Features


# [21.0.15](https://github.com/compare/...@sage/xtrem~tools@21.0.15) (2022-07-08)

### Bug Fixes


### Features


# [21.0.14](https://github.com/compare/...@sage/xtrem~tools@21.0.14) (2022-07-07)

### Bug Fixes


### Features


# [21.0.13](https://github.com/compare/...@sage/xtrem~tools@21.0.13) (2022-07-06)

### Bug Fixes


### Features


# [21.0.12](https://github.com/compare/...@sage/xtrem~tools@21.0.12) (2022-07-05)

### Bug Fixes


### Features


# [21.0.11](https://github.com/compare/...@sage/xtrem~tools@21.0.11) (2022-07-04)

### Bug Fixes


### Features


# [21.0.10](https://github.com/compare/...@sage/xtrem~tools@21.0.10) (2022-07-04)

### Bug Fixes


### Features


# [21.0.9](https://github.com/compare/...@sage/xtrem~tools@21.0.9) (2022-07-02)

### Bug Fixes


### Features


# [21.0.8](https://github.com/compare/...@sage/xtrem~tools@21.0.8) (2022-07-01)

### Bug Fixes


### Features


# [21.0.7](https://github.com/compare/...@sage/xtrem~tools@21.0.7) (2022-06-30)

### Bug Fixes


### Features


# [21.0.6](https://github.com/compare/...@sage/xtrem~tools@21.0.6) (2022-06-29)

### Bug Fixes


### Features


# [21.0.5](https://github.com/compare/...@sage/xtrem~tools@21.0.5) (2022-06-28)

### Bug Fixes


### Features


# [21.0.4](https://github.com/compare/...@sage/xtrem~tools@21.0.4) (2022-06-27)

### Bug Fixes


### Features


# [21.0.3](https://github.com/compare/...@sage/xtrem~tools@21.0.3) (2022-06-26)

### Bug Fixes


### Features


# [21.0.2](https://github.com/compare/...@sage/xtrem~tools@21.0.2) (2022-06-25)

### Bug Fixes


### Features


# [21.0.1](https://github.com/compare/...@sage/xtrem~tools@21.0.1) (2022-06-24)

### Bug Fixes


### Features


# [21.0.0](https://github.com/compare/...@sage/xtrem~tools@21.0.0) (2022-06-23)

### Bug Fixes


### Features


# [20.0.32](https://github.com/compare/...@sage/xtrem~tools@20.0.32) (2022-06-23)

### Bug Fixes


### Features


# [20.0.31](https://github.com/compare/...@sage/xtrem~tools@20.0.31) (2022-06-22)

### Bug Fixes


### Features


# [20.0.30](https://github.com/compare/...@sage/xtrem~tools@20.0.30) (2022-06-21)

### Bug Fixes

* **client-gen:** XT-999999 fix order of declarations in api.d.ts files ([#7609](https://github.com/issues/7609))  ([b3aefa6](https://github.com/commit/b3aefa644b714501fa3b04108135ab1e50a317e9))

### Features


# [20.0.29](https://github.com/compare/...@sage/xtrem~tools@20.0.29) (2022-06-21)

### Bug Fixes


### Features


# [20.0.28](https://github.com/compare/...@sage/xtrem~tools@20.0.28) (2022-06-20)

### Bug Fixes


### Features


# [20.0.27](https://github.com/compare/...@sage/xtrem~tools@20.0.27) (2022-06-19)

### Bug Fixes


### Features


# [20.0.26](https://github.com/compare/...@sage/xtrem~tools@20.0.26) (2022-06-18)

### Bug Fixes


### Features


# [20.0.25](https://github.com/compare/...@sage/xtrem~tools@20.0.25) (2022-06-17)

### Bug Fixes

* **client-gen:** XT-27784 fixed typing issue in UI mashup packages ([#7518](https://github.com/issues/7518))  ([4efc9d9](https://github.com/commit/4efc9d992c6c119605567fc8b8697737adc16ba7))

### Features


# [20.0.24](https://github.com/compare/...@sage/xtrem~tools@20.0.24) (2022-06-16)

### Bug Fixes


### Features


# [20.0.23](https://github.com/compare/...@sage/xtrem~tools@20.0.23) (2022-06-16)

### Bug Fixes


### Features


# [20.0.22](https://github.com/compare/...@sage/xtrem~tools@20.0.22) (2022-06-14)

### Bug Fixes


### Features


# [20.0.21](https://github.com/compare/...@sage/xtrem~tools@20.0.21) (2022-06-14)

### Bug Fixes


### Features


# [20.0.20](https://github.com/compare/...@sage/xtrem~tools@20.0.20) (2022-06-13)

### Bug Fixes


### Features


# [20.0.19](https://github.com/compare/...@sage/xtrem~tools@20.0.19) (2022-06-12)

### Bug Fixes


### Features


# [20.0.18](https://github.com/compare/...@sage/xtrem~tools@20.0.18) (2022-06-11)

### Bug Fixes


### Features


# [20.0.17](https://github.com/compare/...@sage/xtrem~tools@20.0.17) (2022-06-10)

### Bug Fixes


### Features

* **basalt:** XT-25541 getDuplicate ([#7367](https://github.com/issues/7367))  ([6d5067f](https://github.com/commit/6d5067f1d08abdf6a60419acefa2ad34a92d68df))

# [20.0.16](https://github.com/compare/...@sage/xtrem~tools@20.0.16) (2022-06-10)

### Bug Fixes


### Features


# [20.0.15](https://github.com/compare/...@sage/xtrem~tools@20.0.15) (2022-06-08)

### Bug Fixes

* **glossary:** XGL-128 Admin dashboard - Line deletion in the Translation ([#7401](https://github.com/issues/7401))  ([845d11e](https://github.com/commit/845d11e179e4bc088536e9dc8a27389857e8d3d0))

### Features


# [20.0.14](https://github.com/compare/...@sage/xtrem~tools@20.0.14) (2022-06-07)

### Bug Fixes


### Features


# [20.0.13](https://github.com/compare/...@sage/xtrem~tools@20.0.13) (2022-06-06)

### Bug Fixes


### Features


# [20.0.12](https://github.com/compare/...@sage/xtrem~tools@20.0.12) (2022-06-05)

### Bug Fixes


### Features


# [20.0.11](https://github.com/compare/...@sage/xtrem~tools@20.0.11) (2022-06-04)

### Bug Fixes


### Features


# [20.0.10](https://github.com/compare/...@sage/xtrem~tools@20.0.10) (2022-06-03)

### Bug Fixes


### Features


# [20.0.9](https://github.com/compare/...@sage/xtrem~tools@20.0.9) (2022-06-03)

### Bug Fixes


### Features


# [20.0.8](https://github.com/compare/...@sage/xtrem~tools@20.0.8) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

### Features

* refactored card definitions ([#7338](https://github.com/issues/7338))  ([04c11f2](https://github.com/commit/04c11f2f4b45b96b80e78eabcf2dc537397a2777))

# [20.0.5](https://github.com/compare/...@sage/xtrem~tools@20.0.5) (2022-06-02)

### Bug Fixes


### Features


# [20.0.4](https://github.com/compare/...@sage/xtrem~tools@20.0.4) (2022-06-01)

### Bug Fixes

* removed heavy this.$.values._id calls ([#7263](https://github.com/issues/7263))  ([10c49fe](https://github.com/commit/10c49fe62debbfb4e039a526dfd81e09bb11d5f7))

### Features


# [20.0.3](https://github.com/compare/...@sage/xtrem~tools@20.0.3) (2022-05-29)

### Bug Fixes


### Features


# [20.0.2](https://github.com/compare/...@sage/xtrem~tools@20.0.2) (2022-05-28)

### Bug Fixes


### Features


# [20.0.1](https://github.com/compare/...@sage/xtrem~tools@20.0.1) (2022-05-27)

### Bug Fixes


### Features


# [20.0.0](https://github.com/compare/...@sage/xtrem~tools@20.0.0) (2022-05-26)

### Bug Fixes


### Features


# [19.0.33](https://github.com/compare/...@sage/xtrem~tools@19.0.33) (2022-05-26)

### Bug Fixes


### Features


# [19.0.32](https://github.com/compare/...@sage/xtrem~tools@19.0.32) (2022-05-26)

### Bug Fixes


### Features

* **glossary:** XGL-140 Remove Give feedback and Advanced filter button ([#7174](https://github.com/issues/7174))  ([ffff8d6](https://github.com/commit/ffff8d63d28889f9ed5179bfcddd3fb115a59ec4))

# [19.0.31](https://github.com/compare/...@sage/xtrem~tools@19.0.31) (2022-05-24)

### Bug Fixes


### Features


# [19.0.30](https://github.com/compare/...@sage/xtrem~tools@19.0.30) (2022-05-24)

### Bug Fixes


### Features


# [19.0.29](https://github.com/compare/...@sage/xtrem~tools@19.0.29) (2022-05-23)

### Bug Fixes


### Features


# [19.0.28](https://github.com/compare/...@sage/xtrem~tools@19.0.28) (2022-05-23)

### Bug Fixes


### Features


# [19.0.27](https://github.com/compare/...@sage/xtrem~tools@19.0.27) (2022-05-22)

### Bug Fixes


### Features


# [19.0.26](https://github.com/compare/...@sage/xtrem~tools@19.0.26) (2022-05-21)

### Bug Fixes


### Features


# [19.0.25](https://github.com/compare/...@sage/xtrem~tools@19.0.25) (2022-05-20)

### Bug Fixes

* XT-24320 fix CSV data, tax-value upgrade data action and up sort value to 100 times id ([#7106](https://github.com/issues/7106))  ([d3725ec](https://github.com/commit/d3725ecd70e96e8d880c57a692f016d9dc7c840d))

### Features


# [19.0.24](https://github.com/compare/...@sage/xtrem~tools@19.0.24) (2022-05-20)

### Bug Fixes


### Features


# [19.0.23](https://github.com/compare/...@sage/xtrem~tools@19.0.23) (2022-05-18)

### Bug Fixes


### Features


# [19.0.22](https://github.com/compare/...@sage/xtrem~tools@19.0.22) (2022-05-17)

### Bug Fixes


### Features

* **glossary:** XGL-116 best match ([#7036](https://github.com/issues/7036))  ([aec4fdc](https://github.com/commit/aec4fdc476423cc694c45da7dfd5157039d6c4e6))

# [19.0.21](https://github.com/compare/...@sage/xtrem~tools@19.0.21) (2022-05-16)

### Bug Fixes


### Features


# [19.0.20](https://github.com/compare/...@sage/xtrem~tools@19.0.20) (2022-05-15)

### Bug Fixes


### Features


# [19.0.19](https://github.com/compare/...@sage/xtrem~tools@19.0.19) (2022-05-14)

### Bug Fixes


### Features


# [19.0.18](https://github.com/compare/...@sage/xtrem~tools@19.0.18) (2022-05-13)

### Bug Fixes


### Features


# [19.0.17](https://github.com/compare/...@sage/xtrem~tools@19.0.17) (2022-05-13)

### Bug Fixes


### Features


# [19.0.16](https://github.com/compare/...@sage/xtrem~tools@19.0.16) (2022-05-12)

### Bug Fixes


### Features


# [19.0.15](https://github.com/compare/...@sage/xtrem~tools@19.0.15) (2022-05-11)

### Bug Fixes


### Features


# [19.0.14](https://github.com/compare/...@sage/xtrem~tools@19.0.14) (2022-05-10)

### Bug Fixes


### Features


# [19.0.13](https://github.com/compare/...@sage/xtrem~tools@19.0.13) (2022-05-10)

### Bug Fixes


### Features


# [19.0.12](https://github.com/compare/...@sage/xtrem~tools@19.0.12) (2022-05-09)

### Bug Fixes


### Features


# [19.0.11](https://github.com/compare/...@sage/xtrem~tools@19.0.11) (2022-05-08)

### Bug Fixes


### Features


# [19.0.10](https://github.com/compare/...@sage/xtrem~tools@19.0.10) (2022-05-07)

### Bug Fixes


### Features


# [19.0.9](https://github.com/compare/...@sage/xtrem~tools@19.0.9) (2022-05-06)

### Bug Fixes


### Features


# [19.0.8](https://github.com/compare/...@sage/xtrem~tools@19.0.8) (2022-05-06)

### Bug Fixes


### Features


# [19.0.7](https://github.com/compare/...@sage/xtrem~tools@19.0.7) (2022-05-04)

### Bug Fixes


### Features


# [19.0.6](https://github.com/compare/...@sage/xtrem~tools@19.0.6) (2022-05-03)

### Bug Fixes


### Features

* **glossary:** XT-24772 Added import export menus to the glossary application ([#6763](https://github.com/issues/6763))  ([f95ff03](https://github.com/commit/f95ff0336f95a54abf4cf20cffda2e6455acc404))

# [19.0.5](https://github.com/compare/...@sage/xtrem~tools@19.0.5) (2022-05-02)

### Bug Fixes


### Features


# [19.0.4](https://github.com/compare/...@sage/xtrem~tools@19.0.4) (2022-05-01)

### Bug Fixes


### Features


# [19.0.3](https://github.com/compare/...@sage/xtrem~tools@19.0.3) (2022-04-30)

### Bug Fixes


### Features


# [19.0.2](https://github.com/compare/...@sage/xtrem~tools@19.0.2) (2022-04-29)

### Bug Fixes

* remove npm npx rimraf (XT-24746) ([#6742](https://github.com/issues/6742))  ([6aa09dd](https://github.com/commit/6aa09dd305b2724eb472161396c820ca4c3f3d0a))

### Features


# [19.0.1](https://github.com/compare/...@sage/xtrem~tools@19.0.1) (2022-04-28)

### Bug Fixes


### Features


# [19.0.0](https://github.com/compare/...@sage/xtrem~tools@19.0.0) (2022-04-28)

### Bug Fixes


### Features


# [18.0.37](https://github.com/compare/...@sage/xtrem~tools@18.0.37) (2022-04-28)

### Bug Fixes


### Features


# [18.0.36](https://github.com/compare/...@sage/xtrem~tools@18.0.36) (2022-04-27)

### Bug Fixes


### Features

* **glossary:** XGL-192 Update content for GL 2023 R3 release ([#6668](https://github.com/issues/6668))  ([7c9f003](https://github.com/commit/7c9f003ddd7b48bdbfac6c01042b974f381a05e0))

# [18.0.35](https://github.com/compare/...@sage/xtrem~tools@18.0.35) (2022-04-27)

### Bug Fixes


### Features


# [18.0.34](https://github.com/compare/...@sage/xtrem~tools@18.0.34) (2022-04-26)

### Bug Fixes


### Features


# [18.0.33](https://github.com/compare/...@sage/xtrem~tools@18.0.33) (2022-04-26)

### Bug Fixes


### Features


# [18.0.32](https://github.com/compare/...@sage/xtrem~tools@18.0.32) (2022-04-25)

### Bug Fixes


### Features


# [18.0.31](https://github.com/compare/...@sage/xtrem~tools@18.0.31) (2022-04-25)

### Bug Fixes


### Features


# [18.0.30](https://github.com/compare/...@sage/xtrem~tools@18.0.30) (2022-04-21)

### Bug Fixes


### Features


# [18.0.29](https://github.com/compare/...@sage/xtrem~tools@18.0.29) (2022-04-21)

### Bug Fixes


### Features


# [18.0.28](https://github.com/compare/...@sage/xtrem~tools@18.0.28) (2022-04-20)

### Bug Fixes


### Features


# [18.0.27](https://github.com/compare/...@sage/xtrem~tools@18.0.27) (2022-04-20)

### Bug Fixes


### Features


# [18.0.26](https://github.com/compare/...@sage/xtrem~tools@18.0.26) (2022-04-18)

### Bug Fixes


### Features


# [18.0.25](https://github.com/compare/...@sage/xtrem~tools@18.0.25) (2022-04-18)

### Bug Fixes


### Features


# [18.0.24](https://github.com/compare/...@sage/xtrem~tools@18.0.24) (2022-04-16)

### Bug Fixes


### Features


# [18.0.23](https://github.com/compare/...@sage/xtrem~tools@18.0.23) (2022-04-15)

### Bug Fixes


### Features


# [18.0.22](https://github.com/compare/...@sage/xtrem~tools@18.0.22) (2022-04-14)

### Bug Fixes


### Features


# [18.0.21](https://github.com/compare/...@sage/xtrem~tools@18.0.21) (2022-04-13)

### Bug Fixes


### Features


# [18.0.20](https://github.com/compare/...@sage/xtrem~tools@18.0.20) (2022-04-12)

### Bug Fixes


### Features


# [18.0.19](https://github.com/compare/...@sage/xtrem~tools@18.0.19) (2022-04-11)

### Bug Fixes


### Features


# [18.0.18](https://github.com/compare/...@sage/xtrem~tools@18.0.18) (2022-04-10)

### Bug Fixes


### Features


# [18.0.17](https://github.com/compare/...@sage/xtrem~tools@18.0.17) (2022-04-09)

### Bug Fixes


### Features


# [18.0.16](https://github.com/compare/...@sage/xtrem~tools@18.0.16) (2022-04-08)

### Bug Fixes


### Features


# [18.0.15](https://github.com/compare/...@sage/xtrem~tools@18.0.15) (2022-04-07)

### Bug Fixes


### Features


# [18.0.14](https://github.com/compare/...@sage/xtrem~tools@18.0.14) (2022-04-06)

### Bug Fixes


### Features


# [18.0.13](https://github.com/compare/...@sage/xtrem~tools@18.0.13) (2022-04-05)

### Bug Fixes


### Features


# [18.0.12](https://github.com/compare/...@sage/xtrem~tools@18.0.12) (2022-04-04)

### Bug Fixes


### Features


# [18.0.11](https://github.com/compare/...@sage/xtrem~tools@18.0.11) (2022-04-03)

### Bug Fixes


### Features


# [18.0.10](https://github.com/compare/...@sage/xtrem~tools@18.0.10) (2022-04-02)

### Bug Fixes


### Features


# [18.0.9](https://github.com/compare/...@sage/xtrem~tools@18.0.9) (2022-04-01)

### Bug Fixes


### Features


# [18.0.8](https://github.com/compare/...@sage/xtrem~tools@18.0.8) (2022-03-31)

### Bug Fixes


### Features

* **glossary:** XGL-172 add first parameter ([#6224](https://github.com/issues/6224))  ([a0605c1](https://github.com/commit/a0605c17d125e915fd18d5b5ce3c9b6717526cc8))

# [18.0.7](https://github.com/compare/...@sage/xtrem~tools@18.0.7) (2022-03-31)

### Bug Fixes


### Features


# [18.0.6](https://github.com/compare/...@sage/xtrem~tools@18.0.6) (2022-03-30)

### Bug Fixes


### Features

* XT-20534 refactor FKDefinitions ([#6113](https://github.com/issues/6113))  ([fb69421](https://github.com/commit/fb69421cedf2ffe96875cc51979d19c40920ae88))
* **glossary:** XGL-172 Remove domains and products add page info ([#6218](https://github.com/issues/6218))  ([bb9a39f](https://github.com/commit/bb9a39f2f934c2e6c6f018c2e288d8b0abf4dba0))

# [18.0.5](https://github.com/compare/...@sage/xtrem~tools@18.0.5) (2022-03-29)

### Bug Fixes


### Features

* **glossary:** XGL-172 Create a custom query ([#6189](https://github.com/issues/6189))  ([8e13460](https://github.com/commit/8e134608d093a41863e483f225c598c396308e27))

# [18.0.4](https://github.com/compare/...@sage/xtrem~tools@18.0.4) (2022-03-28)

### Bug Fixes

* XT-21824 resolved issue on collection order by for paging ([#6120](https://github.com/issues/6120))  ([aeaf3fb](https://github.com/commit/aeaf3fb9daee26136d62382e7504ff5cccca2cd8))

### Features


# [18.0.3](https://github.com/compare/...@sage/xtrem~tools@18.0.3) (2022-03-27)

### Bug Fixes


### Features


# [18.0.2](https://github.com/compare/...@sage/xtrem~tools@18.0.2) (2022-03-26)

### Bug Fixes


### Features


# [18.0.1](https://github.com/compare/...@sage/xtrem~tools@18.0.1) (2022-03-25)

### Bug Fixes


### Features

* rename helper panel to detail panel ([#6073](https://github.com/issues/6073))  ([b81d3b1](https://github.com/commit/b81d3b1e4de5168c71dc58f6046cb6d152b4dd32))

# [18.0.0](https://github.com/compare/...@sage/xtrem~tools@18.0.0) (2022-03-24)

### Bug Fixes


### Features


# [17.0.29](https://github.com/compare/...@sage/xtrem~tools@17.0.29) (2022-03-24)

### Bug Fixes


### Features


# [17.0.28](https://github.com/compare/...@sage/xtrem~tools@17.0.28) (2022-03-24)

### Bug Fixes


### Features


# [17.0.27](https://github.com/compare/...@sage/xtrem~tools@17.0.27) (2022-03-23)

### Bug Fixes


### Features


# [17.0.26](https://github.com/compare/...@sage/xtrem~tools@17.0.26) (2022-03-22)

### Bug Fixes


### Features

* **glossary:** XGL-139 Update content for GL 2022 R2 release ([#5998](https://github.com/issues/5998))  ([4a1dea5](https://github.com/commit/4a1dea5d3206ec41edc24f10a6281dcd51227b7a))

# [17.0.25](https://github.com/compare/...@sage/xtrem~tools@17.0.25) (2022-03-21)

### Bug Fixes


### Features


# [17.0.24](https://github.com/compare/...@sage/xtrem~tools@17.0.24) (2022-03-20)

### Bug Fixes


### Features


# [17.0.23](https://github.com/compare/...@sage/xtrem~tools@17.0.23) (2022-03-20)

### Bug Fixes


### Features


# [17.0.22](https://github.com/compare/...@sage/xtrem~tools@17.0.22) (2022-03-19)

### Bug Fixes


### Features


# [17.0.21](https://github.com/compare/...@sage/xtrem~tools@17.0.21) (2022-03-19)

### Bug Fixes


### Features


# [17.0.20](https://github.com/compare/...@sage/xtrem~tools@17.0.20) (2022-03-18)

### Bug Fixes


### Features


# [17.0.19](https://github.com/compare/...@sage/xtrem~tools@17.0.19) (2022-03-18)

### Bug Fixes


### Features


# [17.0.18](https://github.com/compare/...@sage/xtrem~tools@17.0.18) (2022-03-18)

### Bug Fixes


### Features


# [17.0.17](https://github.com/compare/...@sage/xtrem~tools@17.0.17) (2022-03-17)

### Bug Fixes


### Features


# [17.0.16](https://github.com/compare/...@sage/xtrem~tools@17.0.16) (2022-03-17)

### Bug Fixes


### Features


# [17.0.15](https://github.com/compare/...@sage/xtrem~tools@17.0.15) (2022-03-13)

### Bug Fixes


### Features


# [17.0.14](https://github.com/compare/...@sage/xtrem~tools@17.0.14) (2022-03-10)

### Bug Fixes


### Features


# [17.0.13](https://github.com/compare/...@sage/xtrem~tools@17.0.13) (2022-03-09)

### Bug Fixes


### Features

* **upgrade:** XT-19111 Added option execute ([#5720](https://github.com/issues/5720))  ([14e6404](https://github.com/commit/14e6404fa151fbe4f1e5ea81e7db6735b48d713f))

# [17.0.12](https://github.com/compare/...@sage/xtrem~tools@17.0.12) (2022-03-09)

### Bug Fixes


### Features


# [17.0.11](https://github.com/compare/...@sage/xtrem~tools@17.0.11) (2022-03-08)

### Bug Fixes


### Features


# [17.0.10](https://github.com/compare/...@sage/xtrem~tools@17.0.10) (2022-03-07)

### Bug Fixes


### Features


# [17.0.9](https://github.com/compare/...@sage/xtrem~tools@17.0.9) (2022-03-06)

### Bug Fixes


### Features


# [17.0.8](https://github.com/compare/...@sage/xtrem~tools@17.0.8) (2022-03-05)

### Bug Fixes


### Features


# [17.0.7](https://github.com/compare/...@sage/xtrem~tools@17.0.7) (2022-03-04)

### Bug Fixes


### Features


# [17.0.6](https://github.com/compare/...@sage/xtrem~tools@17.0.6) (2022-03-03)

### Bug Fixes


### Features


# [17.0.5](https://github.com/compare/...@sage/xtrem~tools@17.0.5) (2022-03-03)

### Bug Fixes


### Features


# [17.0.4](https://github.com/compare/...@sage/xtrem~tools@17.0.4) (2022-03-01)

### Bug Fixes


### Features


# [17.0.3](https://github.com/compare/...@sage/xtrem~tools@17.0.3) (2022-02-28)

### Bug Fixes


### Features


# [17.0.2](https://github.com/compare/...@sage/xtrem~tools@17.0.2) (2022-02-27)

### Bug Fixes


### Features


# [17.0.1](https://github.com/compare/...@sage/xtrem~tools@17.0.1) (2022-02-26)

### Bug Fixes


### Features


# [17.0.0](https://github.com/compare/...@sage/xtrem~tools@17.0.0) (2022-02-25)

### Bug Fixes


### Features


# [16.0.29](https://github.com/compare/...@sage/xtrem~tools@16.0.29) (2022-02-24)

### Bug Fixes


### Features


# [16.0.28](https://github.com/compare/...@sage/xtrem~tools@16.0.28) (2022-02-24)

### Bug Fixes


### Features


# [16.0.27](https://github.com/compare/...@sage/xtrem~tools@16.0.27) (2022-02-24)

### Bug Fixes


### Features


# [16.0.26](https://github.com/compare/...@sage/xtrem~tools@16.0.26) (2022-02-23)

### Bug Fixes


### Features


# [16.0.25](https://github.com/compare/...@sage/xtrem~tools@16.0.25) (2022-02-22)

### Bug Fixes


### Features

* **glossary:** XGL-138 Term mismatch ([#5455](https://github.com/issues/5455))  ([2209dc3](https://github.com/commit/2209dc3e425c9860a529427d6fdd7df82b6fef5a))

# [16.0.24](https://github.com/compare/...@sage/xtrem~tools@16.0.24) (2022-02-21)

### Bug Fixes


### Features


# [16.0.23](https://github.com/compare/...@sage/xtrem~tools@16.0.23) (2022-02-20)

### Bug Fixes


### Features


# [16.0.22](https://github.com/compare/...@sage/xtrem~tools@16.0.22) (2022-02-19)

### Bug Fixes


### Features


# [16.0.21](https://github.com/compare/...@sage/xtrem~tools@16.0.21) (2022-02-18)

### Bug Fixes


### Features


# [16.0.20](https://github.com/compare/...@sage/xtrem~tools@16.0.20) (2022-02-17)

### Bug Fixes


### Features


# [16.0.19](https://github.com/compare/...@sage/xtrem~tools@16.0.19) (2022-02-16)

### Bug Fixes


### Features

* add bar chart widget XT-18752 ([#5335](https://github.com/issues/5335))  ([daab389](https://github.com/commit/daab389c9b5267d3761c4cde726f33cf42c95097))

# [16.0.18](https://github.com/compare/...@sage/xtrem~tools@16.0.18) (2022-02-15)

### Bug Fixes

* XT-19324 fixed Action suffix warnings and converted the warning to an error ([#5297](https://github.com/issues/5297))  ([97efbf0](https://github.com/commit/97efbf0be3752a20dfea61d235da82aa2529a1c8))

### Features


# [16.0.17](https://github.com/compare/...@sage/xtrem~tools@16.0.17) (2022-02-13)

### Bug Fixes


### Features


# [16.0.16](https://github.com/compare/...@sage/xtrem~tools@16.0.16) (2022-02-12)

### Bug Fixes


### Features


# [16.0.15](https://github.com/compare/...@sage/xtrem~tools@16.0.15) (2022-02-11)

### Bug Fixes


### Features


# [16.0.14](https://github.com/compare/...@sage/xtrem~tools@16.0.14) (2022-02-10)

### Bug Fixes


### Features


# [16.0.13](https://github.com/compare/...@sage/xtrem~tools@16.0.13) (2022-02-10)

### Bug Fixes


### Features


# [16.0.12](https://github.com/compare/...@sage/xtrem~tools@16.0.12) (2022-02-08)

### Bug Fixes


### Features


# [16.0.11](https://github.com/compare/...@sage/xtrem~tools@16.0.11) (2022-02-07)

### Bug Fixes


### Features


# [16.0.10](https://github.com/compare/...@sage/xtrem~tools@16.0.10) (2022-02-07)

### Bug Fixes


### Features


# [16.0.9](https://github.com/compare/...@sage/xtrem~tools@16.0.9) (2022-02-06)

### Bug Fixes


### Features


# [16.0.8](https://github.com/compare/...@sage/xtrem~tools@16.0.8) (2022-02-05)

### Bug Fixes


### Features


# [16.0.7](https://github.com/compare/...@sage/xtrem~tools@16.0.7) (2022-02-04)

### Bug Fixes


### Features


# [16.0.6](https://github.com/compare/...@sage/xtrem~tools@16.0.6) (2022-02-04)

### Bug Fixes


### Features


# [16.0.5](https://github.com/compare/...@sage/xtrem~tools@16.0.5) (2022-02-02)

### Bug Fixes

* **glossary:** XGL-129 errors on admin page ([#5036](https://github.com/issues/5036))  ([c425311](https://github.com/commit/c4253119db748faebfe594c4636626f8a81843f8))

### Features


# [16.0.4](https://github.com/compare/...@sage/xtrem~tools@16.0.4) (2022-02-01)

### Bug Fixes


### Features


# [16.0.3](https://github.com/compare/...@sage/xtrem~tools@16.0.3) (2022-01-31)

### Bug Fixes

* **glossary:** XGL-129 term cannot be created ([#4963](https://github.com/issues/4963))  ([076987e](https://github.com/commit/076987e75c73fb0ebac5cdf8e2e4d79043b74ee1))

### Features


# [16.0.2](https://github.com/compare/...@sage/xtrem~tools@16.0.2) (2022-01-30)

### Bug Fixes


### Features


# [16.0.1](https://github.com/compare/...@sage/xtrem~tools@16.0.1) (2022-01-29)

### Bug Fixes

* **glossary:** XGL-102 change status from deprecated to not recommended ([#4880](https://github.com/issues/4880))  ([dce93b2](https://github.com/commit/dce93b238dab9391a56cc8e0875848762f9fcf16))

### Features


# [16.0.0](https://github.com/compare/...@sage/xtrem~tools@16.0.0) (2022-01-29)

### Bug Fixes


### Features


# [15.0.36](https://github.com/compare/...@sage/xtrem~tools@15.0.36) (2022-01-28)

### Bug Fixes


### Features


# [15.0.35](https://github.com/compare/...@sage/xtrem~tools@15.0.35) (2022-01-28)

### Bug Fixes


### Features


# [15.0.34](https://github.com/compare/...@sage/xtrem~tools@15.0.34) (2022-01-28)

### Bug Fixes


### Features


# [15.0.33](https://github.com/compare/...@sage/xtrem~tools@15.0.33) (2022-01-26)

### Bug Fixes


### Features


# [15.0.32](https://github.com/compare/...@sage/xtrem~tools@15.0.32) (2022-01-26)

### Bug Fixes


### Features


# [15.0.31](https://github.com/compare/...@sage/xtrem~tools@15.0.31) (2022-01-25)

### Bug Fixes


### Features


# [15.0.30](https://github.com/compare/...@sage/xtrem~tools@15.0.30) (2022-01-25)

### Bug Fixes


### Features


# [15.0.29](https://github.com/compare/...@sage/xtrem~tools@15.0.29) (2022-01-24)

### Bug Fixes


### Features


# [15.0.28](https://github.com/compare/...@sage/xtrem~tools@15.0.28) (2022-01-24)

### Bug Fixes


### Features


# [15.0.27](https://github.com/compare/...@sage/xtrem~tools@15.0.27) (2022-01-23)

### Bug Fixes


### Features

* british translations removed ([#4803](https://github.com/issues/4803))  ([28d162b](https://github.com/commit/28d162bbd8f921046914ca1c5f4b2ce9862481c5))

# [15.0.26](https://github.com/compare/...@sage/xtrem~tools@15.0.26) (2022-01-23)

### Bug Fixes


### Features

* XT-16051 tools and reformatting before propagation of new setup id values ([#4824](https://github.com/issues/4824))  ([6e29f88](https://github.com/commit/6e29f88187962d2838a459235e48edaf98eeb657))

# [15.0.25](https://github.com/compare/...@sage/xtrem~tools@15.0.25) (2022-01-21)

### Bug Fixes


### Features


# [15.0.24](https://github.com/compare/...@sage/xtrem~tools@15.0.24) (2022-01-18)

### Bug Fixes


### Features


# [15.0.23](https://github.com/compare/...@sage/xtrem~tools@15.0.23) (2022-01-18)

### Bug Fixes


### Features


# [15.0.22](https://github.com/compare/...@sage/xtrem~tools@15.0.22) (2022-01-17)

### Bug Fixes


### Features


# [15.0.21](https://github.com/compare/...@sage/xtrem~tools@15.0.21) (2022-01-16)

### Bug Fixes


### Features


# [15.0.20](https://github.com/compare/...@sage/xtrem~tools@15.0.20) (2022-01-15)

### Bug Fixes


### Features


# [15.0.19](https://github.com/compare/...@sage/xtrem~tools@15.0.19) (2022-01-14)

### Bug Fixes


### Features


# [15.0.18](https://github.com/compare/...@sage/xtrem~tools@15.0.18) (2022-01-14)

### Bug Fixes


### Features


# [15.0.17](https://github.com/compare/...@sage/xtrem~tools@15.0.17) (2022-01-13)

### Bug Fixes


### Features

* XT-16495 do not hide details on index collision ([#4465](https://github.com/issues/4465))  ([94e6970](https://github.com/commit/94e69701d00b4a20d265b1ba2a47e305320dff93))

# [15.0.16](https://github.com/compare/...@sage/xtrem~tools@15.0.16) (2022-01-12)

### Bug Fixes


### Features


# [15.0.15](https://github.com/compare/...@sage/xtrem~tools@15.0.15) (2022-01-11)

### Bug Fixes


### Features


# [15.0.14](https://github.com/compare/...@sage/xtrem~tools@15.0.14) (2022-01-11)

### Bug Fixes


### Features


# [15.0.13](https://github.com/compare/...@sage/xtrem~tools@15.0.13) (2022-01-11)

### Bug Fixes


### Features


# [15.0.12](https://github.com/compare/...@sage/xtrem~tools@15.0.12) (2022-01-10)

### Bug Fixes


### Features


# [15.0.11](https://github.com/compare/...@sage/xtrem~tools@15.0.11) (2022-01-09)

### Bug Fixes


### Features


# [15.0.10](https://github.com/compare/...@sage/xtrem~tools@15.0.10) (2022-01-08)

### Bug Fixes


### Features


# [15.0.9](https://github.com/compare/...@sage/xtrem~tools@15.0.9) (2022-01-07)

### Bug Fixes


### Features


# [15.0.8](https://github.com/compare/...@sage/xtrem~tools@15.0.8) (2022-01-07)

### Bug Fixes


### Features


# [15.0.7](https://github.com/compare/...@sage/xtrem~tools@15.0.7) (2022-01-06)

### Bug Fixes


### Features


# [15.0.6](https://github.com/compare/...@sage/xtrem~tools@15.0.6) (2022-01-05)

### Bug Fixes


### Features


# [15.0.5](https://github.com/compare/...@sage/xtrem~tools@15.0.5) (2022-01-04)

### Bug Fixes

* fixed missing project references ([#4419](https://github.com/issues/4419))  ([94dae98](https://github.com/commit/94dae983ba6ec71ac0451f793f3d75daedac0b92))
* get the correct translations in standalone app XT-16077 ([#4411](https://github.com/issues/4411))  ([9b8843a](https://github.com/commit/9b8843a4f07e0a1d78f5d30e3a8d8213c59cfc38))

### Features


# [15.0.4](https://github.com/compare/...@sage/xtrem~tools@15.0.4) (2022-01-03)

### Bug Fixes


### Features


# [15.0.3](https://github.com/compare/...@sage/xtrem~tools@15.0.3) (2022-01-02)

### Bug Fixes


### Features


# [15.0.2](https://github.com/compare/...@sage/xtrem~tools@15.0.2) (2022-01-01)

### Bug Fixes


### Features


# [15.0.1](https://github.com/compare/...@sage/xtrem~tools@15.0.1) (2021-12-31)

### Bug Fixes


### Features


# [15.0.0](https://github.com/compare/...@sage/xtrem~tools@15.0.0) (2021-12-30)

### Bug Fixes


### Features


# [14.0.29](https://github.com/compare/...@sage/xtrem~tools@14.0.29) (2021-12-30)

### Bug Fixes


### Features


# [14.0.28](https://github.com/compare/...@sage/xtrem~tools@14.0.28) (2021-12-29)

### Bug Fixes


### Features


# [14.0.27](https://github.com/compare/...@sage/xtrem~tools@14.0.27) (2021-12-28)

### Bug Fixes


### Features


# [14.0.26](https://github.com/compare/...@sage/xtrem~tools@14.0.26) (2021-12-27)

### Bug Fixes


### Features


# [14.0.25](https://github.com/compare/...@sage/xtrem~tools@14.0.25) (2021-12-27)

### Bug Fixes


### Features


# [14.0.24](https://github.com/compare/...@sage/xtrem~tools@14.0.24) (2021-12-22)

### Bug Fixes

* XT-16239 fixed api package.json ([#4334](https://github.com/issues/4334))  ([ac249aa](https://github.com/commit/ac249aa4d1c162d1dd74083c8568f525dd6744da))

### Features


# [14.0.23](https://github.com/compare/...@sage/xtrem~tools@14.0.23) (2021-12-21)

### Bug Fixes

* **glossary:** XGL-3 fix language filters ([#4263](https://github.com/issues/4263))  ([bfb65ee](https://github.com/commit/bfb65ee27a150ee351841d9d7fedb79695349afa))
* **glossary:** XGL-4 remove unused pages ([#4265](https://github.com/issues/4265))  ([e0e08f2](https://github.com/commit/e0e08f2ec8103eccf3b80fb8c02f16e8012b49ed))
* **glossary:** XGL-8 order terms by language code ([#4267](https://github.com/issues/4267))  ([3de299a](https://github.com/commit/3de299ab239562e6981062fa3c5ac399d28237c8))

### Features

* **client-gen:** fix package patch version build-api XT-14252 ([#3947](https://github.com/issues/3947))  ([1be1aba](https://github.com/commit/1be1abaac7b19b16f430d9483272b971152b8cbb))
* **glossary:** XGL-83 Add new line buttons ([#4291](https://github.com/issues/4291))  ([afc65ba](https://github.com/commit/afc65bac1ede1bbaff850982fed4b3f89c1e4659))

# [14.0.22](https://github.com/compare/...@sage/xtrem~tools@14.0.22) (2021-12-15)

### Bug Fixes


### Features

* **xtrem-cli:** generate routing.json file for notification listeners (XT-11999) ([#3889](https://github.com/issues/3889))  ([38c15fb](https://github.com/commit/38c15fb99791176fb1ce46b7078204b04f2a10f1))

# [14.0.21](https://github.com/compare/...@sage/xtrem~tools@14.0.21) (2021-12-14)

### Bug Fixes


### Features


# [14.0.20](https://github.com/compare/...@sage/xtrem~tools@14.0.20) (2021-12-13)

### Bug Fixes


### Features


# [14.0.19](https://github.com/compare/...@sage/xtrem~tools@14.0.19) (2021-12-12)

### Bug Fixes


### Features


# [14.0.18](https://github.com/compare/...@sage/xtrem~tools@14.0.18) (2021-12-11)

### Bug Fixes


### Features


# [14.0.17](https://github.com/compare/...@sage/xtrem~tools@14.0.17) (2021-12-10)

### Bug Fixes


### Features


# [14.0.16](https://github.com/compare/...@sage/xtrem~tools@14.0.16) (2021-12-09)

### Bug Fixes


### Features


# [14.0.15](https://github.com/compare/...@sage/xtrem~tools@14.0.15) (2021-12-09)

### Bug Fixes


### Features


# [14.0.14](https://github.com/compare/...@sage/xtrem~tools@14.0.14) (2021-12-08)

### Bug Fixes


### Features


# [14.0.13](https://github.com/compare/...@sage/xtrem~tools@14.0.13) (2021-12-07)

### Bug Fixes


### Features


# [14.0.12](https://github.com/compare/...@sage/xtrem~tools@14.0.12) (2021-12-07)

### Bug Fixes


### Features


# [14.0.11](https://github.com/compare/...@sage/xtrem~tools@14.0.11) (2021-12-06)

### Bug Fixes


### Features


# [14.0.10](https://github.com/compare/...@sage/xtrem~tools@14.0.10) (2021-12-05)

### Bug Fixes


### Features


# [14.0.9](https://github.com/compare/...@sage/xtrem~tools@14.0.9) (2021-12-04)

### Bug Fixes


### Features


# [14.0.8](https://github.com/compare/...@sage/xtrem~tools@14.0.8) (2021-12-03)

### Bug Fixes


### Features


# [14.0.7](https://github.com/compare/...@sage/xtrem~tools@14.0.7) (2021-12-03)

### Bug Fixes


### Features


# [14.0.6](https://github.com/compare/...@sage/xtrem~tools@14.0.6) (2021-12-02)

### Bug Fixes


### Features


# [14.0.5](https://github.com/compare/...@sage/xtrem~tools@14.0.5) (2021-12-02)

### Bug Fixes


### Features


# [14.0.4](https://github.com/compare/...@sage/xtrem~tools@14.0.4) (2021-12-02)

### Bug Fixes


### Features


# [14.0.3](https://github.com/compare/...@sage/xtrem~tools@14.0.3) (2021-12-02)

### Bug Fixes


### Features


# [14.0.2](https://github.com/compare/...@sage/xtrem~tools@14.0.2) (2021-12-01)

### Bug Fixes


### Features


# [14.0.1](https://github.com/compare/...@sage/xtrem~tools@14.0.1) (2021-11-29)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/xtrem~tools@14.0.0) (2021-11-29)

### Bug Fixes


### Features


# [13.0.28](https://github.com/compare/...@sage/xtrem~tools@13.0.28) (2021-11-29)

### Bug Fixes


### Features


# [13.0.27](https://github.com/compare/...@sage/xtrem~tools@13.0.27) (2021-11-28)

### Bug Fixes


### Features


# [13.0.26](https://github.com/compare/...@sage/xtrem~tools@13.0.26) (2021-11-27)

### Bug Fixes


### Features


# [13.0.25](https://github.com/compare/...@sage/xtrem~tools@13.0.25) (2021-11-26)

### Bug Fixes


### Features


# [13.0.24](https://github.com/compare/...@sage/xtrem~tools@13.0.24) (2021-11-25)

### Bug Fixes


### Features

* **glossary:** XGL-102 Change status from Deprecated to Not recommended ([#3878](https://github.com/issues/3878))  ([a5bb80c](https://github.com/commit/a5bb80c8af2f59b363ce1c712faccb44c58638af))
* **glossary:** XGL-41 Update the glossary content to the latest ([#3888](https://github.com/issues/3888))  ([987ed39](https://github.com/commit/987ed39bdc04488d14018f0f18aa301603a118b5))

# [13.0.23](https://github.com/compare/...@sage/xtrem~tools@13.0.23) (2021-11-24)

### Bug Fixes


### Features

* **glossary:** XT-13379 Removed termProducts from the left list ([#3883](https://github.com/issues/3883))  ([e3420b2](https://github.com/commit/e3420b2fbaf6a75e15a5fdacbcf957d2e3bfacd3))

# [13.0.22](https://github.com/compare/...@sage/xtrem~tools@13.0.22) (2021-11-23)

### Bug Fixes


### Features


# [13.0.21](https://github.com/compare/...@sage/xtrem~tools@13.0.21) (2021-11-22)

### Bug Fixes


### Features


# [13.0.20](https://github.com/compare/...@sage/xtrem~tools@13.0.20) (2021-11-22)

### Bug Fixes


### Features


# [13.0.19](https://github.com/compare/...@sage/xtrem~tools@13.0.19) (2021-11-19)

### Bug Fixes


### Features


# [13.0.18](https://github.com/compare/...@sage/xtrem~tools@13.0.18) (2021-11-18)

### Bug Fixes


### Features


# [13.0.17](https://github.com/compare/...@sage/xtrem~tools@13.0.17) (2021-11-18)

### Bug Fixes


### Features


# [13.0.16](https://github.com/compare/...@sage/xtrem~tools@13.0.16) (2021-11-17)

### Bug Fixes


### Features


# [13.0.15](https://github.com/compare/...@sage/xtrem~tools@13.0.15) (2021-11-17)

### Bug Fixes


### Features

* XT-776 split isVitalChild into isVitalReferenceChild and isVitalCollectionChild ([#3650](https://github.com/issues/3650))  ([2f275b1](https://github.com/commit/2f275b11d51f1b318f08a16d609c1aa6ea32c21b))

# [13.0.14](https://github.com/compare/...@sage/xtrem~tools@13.0.14) (2021-11-14)

### Bug Fixes


### Features


# [13.0.13](https://github.com/compare/...@sage/xtrem~tools@13.0.13) (2021-11-13)

### Bug Fixes


### Features


# [13.0.12](https://github.com/compare/...@sage/xtrem~tools@13.0.12) (2021-11-12)

### Bug Fixes


### Features


# [13.0.11](https://github.com/compare/...@sage/xtrem~tools@13.0.11) (2021-11-11)

### Bug Fixes


### Features


# [13.0.10](https://github.com/compare/...@sage/xtrem~tools@13.0.10) (2021-11-10)

### Bug Fixes


### Features


# [13.0.9](https://github.com/compare/...@sage/xtrem~tools@13.0.9) (2021-11-09)

### Bug Fixes


### Features


# [13.0.8](https://github.com/compare/...@sage/xtrem~tools@13.0.8) (2021-11-09)

### Bug Fixes


### Features


# [13.0.7](https://github.com/compare/...@sage/xtrem~tools@13.0.7) (2021-11-09)

### Bug Fixes


### Features


# [13.0.6](https://github.com/compare/...@sage/xtrem~tools@13.0.6) (2021-11-08)

### Bug Fixes


### Features


# [13.0.5](https://github.com/compare/...@sage/xtrem~tools@13.0.5) (2021-11-07)

### Bug Fixes


### Features


# [13.0.4](https://github.com/compare/...@sage/xtrem~tools@13.0.4) (2021-11-06)

### Bug Fixes


### Features


# [13.0.3](https://github.com/compare/...@sage/xtrem~tools@13.0.3) (2021-11-05)

### Bug Fixes


### Features

* XT-4131 align versions across packages ([#3530](https://github.com/issues/3530))  ([f1f900a](https://github.com/commit/f1f900ae61a90a0da1164f4466926be22b20ba0a))

# [13.0.2](https://github.com/compare/...@sage/xtrem~tools@13.0.2) (2021-11-04)

### Bug Fixes


### Features


# [13.0.1](https://github.com/compare/...@sage/xtrem~tools@13.0.1) (2021-11-03)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/xtrem~tools@13.0.0) (2021-11-03)

### Bug Fixes


### Features


# [12.0.35](https://github.com/compare/...@sage/xtrem~tools@12.0.35) (2021-11-02)

### Bug Fixes


### Features


# [12.0.34](https://github.com/compare/...@sage/xtrem~tools@12.0.34) (2021-10-31)

### Bug Fixes


### Features


# [12.0.33](https://github.com/compare/...@sage/xtrem~tools@12.0.33) (2021-10-30)

### Bug Fixes


### Features


# [12.0.32](https://github.com/compare/...@sage/xtrem~tools@12.0.32) (2021-10-29)

### Bug Fixes


### Features


# [12.0.31](https://github.com/compare/...@sage/xtrem~tools@12.0.31) (2021-10-28)

### Bug Fixes


### Features


# [12.0.30](https://github.com/compare/...@sage/xtrem~tools@12.0.30) (2021-10-28)

### Bug Fixes


### Features


# [12.0.29](https://github.com/compare/...@sage/xtrem~tools@12.0.29) (2021-10-26)

### Bug Fixes


### Features


# [12.0.28](https://github.com/compare/...@sage/xtrem~tools@12.0.28) (2021-10-26)

### Bug Fixes


### Features


# [12.0.27](https://github.com/compare/...@sage/xtrem~tools@12.0.27) (2021-10-25)

### Bug Fixes


### Features


# [12.0.26](https://github.com/compare/...@sage/xtrem~tools@12.0.26) (2021-10-25)

### Bug Fixes


### Features


# [12.0.25](https://github.com/compare/...@sage/xtrem~tools@12.0.25) (2021-10-24)

### Bug Fixes


### Features


# [12.0.24](https://github.com/compare/...@sage/xtrem~tools@12.0.24) (2021-10-24)

### Bug Fixes


### Features


# [12.0.23](https://github.com/compare/...@sage/xtrem~tools@12.0.23) (2021-10-23)

### Bug Fixes


### Features


# [12.0.22](https://github.com/compare/...@sage/xtrem~tools@12.0.22) (2021-10-22)

### Bug Fixes


### Features


# [12.0.21](https://github.com/compare/...@sage/xtrem~tools@12.0.21) (2021-10-22)

### Bug Fixes

* **glossary:** X3TRADOC-8990 change empty message of search page  ([27fbf9f](https://github.com/commit/27fbf9f642893d664a94ab2e411e67b9a661c811))

### Features


# [12.0.20](https://github.com/compare/...@sage/xtrem~tools@12.0.20) (2021-10-20)

### Bug Fixes

* **glossary:** X3TRADOC-8990 change empty message of admin board  ([0314dbd](https://github.com/commit/0314dbd70374070e750ca61b5733c566b3c37a33))

### Features


# [12.0.19](https://github.com/compare/...@sage/xtrem~tools@12.0.19) (2021-10-19)

### Bug Fixes

* XT-12547 sort imports and GraphApi extensions in api.d.ts ([#3303](https://github.com/issues/3303))  ([edcf6b8](https://github.com/commit/edcf6b8d811e47377e9c4d34d4967e762c1c831a))

### Features


# [12.0.18](https://github.com/compare/...@sage/xtrem~tools@12.0.18) (2021-10-18)

### Bug Fixes


### Features

* **glossary:** X3TRADOC-9030 Replace Go to term with on-click event ([#3269](https://github.com/issues/3269))  ([85ae21d](https://github.com/commit/85ae21dcd810ab018aefeebe38cae87f165e8c81))

# [12.0.17](https://github.com/compare/...@sage/xtrem~tools@12.0.17) (2021-10-17)

### Bug Fixes


### Features


# [12.0.16](https://github.com/compare/...@sage/xtrem~tools@12.0.16) (2021-10-17)

### Bug Fixes

* **glossary:** X3TRADOC-8239 Adding language flag  ([99493ff](https://github.com/commit/99493ff603dd23e782dc235dc0b068ac298b59c3))
* **glossary:** X3TRADOC-8239 updates based on review  ([14a1fe4](https://github.com/commit/14a1fe4a3eb77333429927e885c5b2ac906ee62f))

### Features


# [12.0.15](https://github.com/compare/...@sage/xtrem~tools@12.0.15) (2021-10-16)

### Bug Fixes


### Features


# [12.0.14](https://github.com/compare/...@sage/xtrem~tools@12.0.14) (2021-10-15)

### Bug Fixes


### Features


# [12.0.13](https://github.com/compare/...@sage/xtrem~tools@12.0.13) (2021-10-14)

### Bug Fixes


### Features


# [12.0.12](https://github.com/compare/...@sage/xtrem~tools@12.0.12) (2021-10-13)

### Bug Fixes


### Features


# [12.0.11](https://github.com/compare/...@sage/xtrem~tools@12.0.11) (2021-10-12)

### Bug Fixes


### Features


# [12.0.10](https://github.com/compare/...@sage/xtrem~tools@12.0.10) (2021-10-12)

### Bug Fixes


### Features


# [12.0.9](https://github.com/compare/...@sage/xtrem~tools@12.0.9) (2021-10-10)

### Bug Fixes


### Features


# [12.0.8](https://github.com/compare/...@sage/xtrem~tools@12.0.8) (2021-10-09)

### Bug Fixes


### Features


# [12.0.7](https://github.com/compare/...@sage/xtrem~tools@12.0.7) (2021-10-08)

### Bug Fixes


### Features


# [12.0.6](https://github.com/compare/...@sage/xtrem~tools@12.0.6) (2021-10-08)

### Bug Fixes

* **glossary:** X3TRADOC-8944 Error Internal error: getNodeData returns more than 1 record when adding a related term ([#3104](https://github.com/issues/3104))  ([843993d](https://github.com/commit/843993d6707c73954e533602673bafde387fd3e6))
* **glossary:** X3TRADOC-8958 Failure Upgrade environment dev cluster glossary ([#3122](https://github.com/issues/3122))  ([9030e12](https://github.com/commit/9030e12e0a5c0e4503e45ce5534681bcc2f2aba8))

### Features


# [12.0.5](https://github.com/compare/...@sage/xtrem~tools@12.0.5) (2021-10-07)

### Bug Fixes


### Features

* **glossary:** X3TRADOC-8236 update the glossary content to the latest ([#3099](https://github.com/issues/3099))  ([61106ee](https://github.com/commit/61106ee781021bc82ae36f1e4d9be6c63116366f))

# [12.0.4](https://github.com/compare/...@sage/xtrem~tools@12.0.4) (2021-10-06)

### Bug Fixes


### Features


# [12.0.3](https://github.com/compare/...@sage/xtrem~tools@12.0.3) (2021-10-05)

### Bug Fixes

* add ExtractEdges type to client & ui XT-1263 ([#126](https://github.com/issues/126))  ([f877410](https://github.com/commit/f877410ae7afa5f53f05f10578ce28fe81e49bd8))
* config and structure fixes ([#1357](https://github.com/issues/1357))  ([bdd8f5a](https://github.com/commit/bdd8f5a89ea10a7376e3a4a2c9d51b77b882af9e))
* read node related to int reference parameter (XT-2775) ([#453](https://github.com/issues/453))  ([69e39f6](https://github.com/commit/69e39f6eacbd73d410c19995d222f2b380581aa7))
* update tsconfigs for shared-functions and client-functions folders and updated client-compilation in cli XT-4904 ([#798](https://github.com/issues/798))  ([183d0d6](https://github.com/commit/183d0d663ae2fa7aed071b6faa937c14f9cba8e6))
* use same version of node for ci and docker images (X3-246964) ([#1187](https://github.com/issues/1187))  ([e2e5b7b](https://github.com/commit/e2e5b7b46c589ebe94632f0b416a387333971413))
* version bump  ([be66252](https://github.com/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-9712 reformatted all csv files ([#2859](https://github.com/issues/2859))  ([b4634a9](https://github.com/commit/b4634a9508c1ab1beec07f460d00a8eef0f87cdf))
* XT-999999 bump patch version on all packages  ([50fe0df](https://github.com/commit/50fe0df1993adccfb8fd1bcf03b0b2cb188c9c88))
* XT-999999 caret prefix ([#1010](https://github.com/issues/1010))  ([751810e](https://github.com/commit/751810e67229000d850d522a49429db070659998))
* XT-999999 fix package versions ([#1780](https://github.com/issues/1780))  ([4bd5d4a](https://github.com/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))
* **glossary:** X3TRADOC-7628 Search In Progress ([#318](https://github.com/issues/318))  ([fe8affb](https://github.com/commit/fe8affb5b8d3cf00bbc13c78848bdcd514073041))
* **glossary:** X3TRADOC-7741 fixed the localize message not to use variable ([#60](https://github.com/issues/60))  ([736ae4e](https://github.com/commit/736ae4ea9643610922dd1c851ae10b6baffe86b5))
* **glossary:** X3TRADOC-7766  Add Callback To Nav Panel Option List ([#484](https://github.com/issues/484))  ([2434479](https://github.com/commit/2434479ad2b20a2479b9302c00646b1dd2d33fb5))
* **glossary:** X3TRADOC-7798  Renaming of labels (Exact match, Similar terms, Exact matches) ([#653](https://github.com/issues/653))  ([33d8c7e](https://github.com/commit/33d8c7ee0ba0cfa96827bafd6e0db5fdf58e4f9d))
* **glossary:** X3TRADOC-8113 Replace language flags codes to the language.csv file ([#1042](https://github.com/issues/1042))  ([9599659](https://github.com/commit/95996599acc39469253672dd4b7f76ef0c6965d9))
* **glossary:** X3TRADOC-8124 Update glossary.csv to reflect structure changes ([#1069](https://github.com/issues/1069))  ([bd664be](https://github.com/commit/bd664be5c4949939a9d95d0f9fbaa2a43e49aadc))
* **glossary:** X3TRADOC-8124 update glossary.csv to reflect structure changes ([#1151](https://github.com/issues/1151))  ([ee3084f](https://github.com/commit/ee3084f6f0d98bd821fc5ab0927779eab127ee53))
* **glossary:** X3TRADOC-8145 Site map fails to load due to the server change ([#1103](https://github.com/issues/1103))  ([dceb616](https://github.com/commit/dceb616c46213a8f3d3cf4784b0a2fe0bea57322))
* **glossary:** X3TRADOC-8157 fix to camel case on status ([#1483](https://github.com/issues/1483))  ([a6c67b6](https://github.com/commit/a6c67b6fa2f4d8ba0e39708f756d1ff4664bc7d5))
* **glossary:** X3TRADOC-8157 Small fixes to the navigation panel and language field ([#1157](https://github.com/issues/1157))  ([2941f72](https://github.com/commit/2941f7289938efa7c30a524e2c872f3715d4d05d))
* **glossary:** X3TRADOC-8187 Added text of empty state in Search  ([66cdeaa](https://github.com/commit/66cdeaa2477b3086dbe9124dd68d6c59b99c608a))
* **glossary:** X3TRADOC-8187 Added updated text  ([7b54dda](https://github.com/commit/7b54dda9f16b2074cf154e577acc3b50e818cb90))
* **glossary:** X3TRADOC-8187 Improve quality of empty state images  ([16ec271](https://github.com/commit/16ec271426b78faa59c0c58c4443ddde1d0a2010))
* **glossary:** X3TRADOC-8187 smaller image for admin page  ([dfad392](https://github.com/commit/dfad392a45bc7cfd599dc61292030ad4ed03aa57))
* **glossary:** X3TRADOC-8190 Language, Abbreviation & Definition fields should be read-only  ([0415d76](https://github.com/commit/0415d76fb3550f75b674acc7d025bde35232a1a3))
* **glossary:** X3TRADOC-8191 Definition field is too small  ([e21693e](https://github.com/commit/e21693e1a5350bcb2a9b1788d5763627c7f567ff))
* **glossary:** X3TRADOC-8193 Improvements to Where used grid  ([dc372f9](https://github.com/commit/dc372f9d71036fd6ecf389194c15ad545c04d6c4))
* **glossary:** X3TRADOC-8195 Improvements to Translations grid  ([697a5e3](https://github.com/commit/697a5e3268b001ac43896126d827d53c9f65cc15))
* **glossary:** X3TRADOC-8200 Remove definition from navigation  ([8a00f46](https://github.com/commit/8a00f464e294744992b7a9f9ebc5cd8241f08f9a))
* **glossary:** X3TRADOC-8204 Remove unused menus ([#1354](https://github.com/issues/1354))  ([78673c0](https://github.com/commit/78673c0a03bad7f249c8d1af148553cb02b76bbb))
* **glossary:** X3TRADOC-8247 Search results are lost and navigation panel closes  ([f1ae3b0](https://github.com/commit/f1ae3b01f39f21543e85010b3c952ffb258360ba))
* **glossary:** X3TRADOC-8361 Title change for Old admin page ([#1690](https://github.com/issues/1690))  ([544b5ce](https://github.com/commit/544b5ce9b987d007959238f77e64100756942bbc))
* **glossary:** X3TRADOC-8587 Showing selected term in the ag-Grid ([#2167](https://github.com/issues/2167))  ([9d2c00a](https://github.com/commit/9d2c00a467944c71b7841d751e7769e16570fe07))
* **glossary:** X3TRADOC-8673 fix the glossary start ([#2411](https://github.com/issues/2411))  ([e0a135d](https://github.com/commit/e0a135d58474214768edbcf984b66d78c81e6fcc))
* **glossary:** X3TRADOC-8681 Remove admin menu item for the release  ([73bc9fa](https://github.com/commit/73bc9faf02461ca95c7625281a42aca65b29e837))
* **xtrem-core:** first pass (XT-9032) ([#2271](https://github.com/issues/2271))  ([218c7f2](https://github.com/commit/218c7f25e51cb522acacb4677004df8867de0ced))
* **xtrem-glossary:** X3TRADOC-7081 Change language select field to dropdownlist field ([#347](https://github.com/issues/347))  ([ba4e98d](https://github.com/commit/ba4e98d3138b6aa5dfa7dd090624934ef5c20f5a))

### Features

* (glossary) X3TRADOC-7807 new desktop navigation menu ([#481](https://github.com/issues/481))  ([9720113](https://github.com/commit/972011309d3f0b908b7fc737aceba2f9add23006))
* build ui artifacts in prod mode for release ([#2418](https://github.com/issues/2418))  ([ea01782](https://github.com/commit/ea017820731ad19951b81d8bd649eb9ca2c00eb3))
* cli faster build ([#1349](https://github.com/issues/1349))  ([e2fb434](https://github.com/commit/e2fb434c7abdb33807cc5964d894e617cf2c6bf7))
* control unique index not nullable XT-6566 ([#2708](https://github.com/issues/2708))  ([d42d405](https://github.com/commit/d42d4053546f063b3691d590575caa6e99068db8))
* nested validation XT-9238  ([#2728](https://github.com/issues/2728))  ([60310de](https://github.com/commit/60310de02299a4aa373c4e4f2cff145a2b8dec37))
* restore datatype postgres XT-852 ([#1077](https://github.com/issues/1077))  ([2b29725](https://github.com/commit/2b29725e1d39c4891dbd5c99332179d4979419fe))
* row -> record renaming 1 ([#214](https://github.com/issues/214))  ([779f090](https://github.com/commit/779f0906ddde795b3857004f63629720fd05b7bc))
* row -> record renaming 2 ([#215](https://github.com/issues/215))  ([6871edf](https://github.com/commit/6871edfe6acd810d876bbae32f8a35fb661002ec))
* row -> record renaming 3 ([#216](https://github.com/issues/216))  ([aa944a8](https://github.com/commit/aa944a875f6f8cfe94e7c9bcf80c50206c365a29))
* static content field ([#210](https://github.com/issues/210))  ([4ba9a96](https://github.com/commit/4ba9a96a9892258ea75c5c9f9e5c3ab8fce414d2))
* using column bind for column translation keys ([#1231](https://github.com/issues/1231))  ([213f00b](https://github.com/commit/213f00b14fef161472b5d75d20da21d56caedf8e))
* X3TRADOC-7335 add a new translation of the glossary term ([#263](https://github.com/issues/263))  ([957fd8a](https://github.com/commit/957fd8a642581e8b33b02af9c7c55c4f3438870e))
* XT-10348 add identified by and reference table to schema gen ([#2688](https://github.com/issues/2688))  ([0a7151a](https://github.com/commit/0a7151a941f9aab6c2e38b87eeceea503c5b9c52))
* XT-3029 S3 cache for builds ([#59](https://github.com/issues/59))  ([328026a](https://github.com/commit/328026a23f04a23db7180cdde5c270b44adee609))
* XT-4920 updating of enum types in nodes and api to union string ([#1149](https://github.com/issues/1149))  ([097909a](https://github.com/commit/097909a82182189967d3eb15b0cde0bf2392324d))
* XT-999999 fixed APIs ([#622](https://github.com/issues/622))  ([8a24f8e](https://github.com/commit/8a24f8e7315076c9764f703d6c8dce1bbe7b6050))
* XT-999999 fixed version of packages ([#1089](https://github.com/issues/1089))  ([4030a1f](https://github.com/commit/4030a1f9edbf466ab080ebb6a8547643713cf439))
* **core:** XT-6755 added computeValue ([#1582](https://github.com/issues/1582))  ([ad67508](https://github.com/commit/ad67508deea7ffd5d38b445e35e7522d47031452))
* **glossary:** X3TRADOC-5921 Warn if duplicate ([#1193](https://github.com/issues/1193))  ([d0f2caa](https://github.com/commit/d0f2caa50d13f1b42009e8683b8423f29dd4e476))
* **glossary:** X3TRADOC-7081 Drop-down list with nodes ([#377](https://github.com/issues/377))  ([ab7b5e8](https://github.com/commit/ab7b5e84d33440c72204e43eab08535c847ce86f))
* **glossary:** X3TRADOC-7336 updates based on review  ([a1e053d](https://github.com/commit/a1e053d4e82ed241192f824f6996c32e68be2c38))
* **glossary:** X3TRADOC-7758 Unsaved changes message in search ([#349](https://github.com/issues/349))  ([e98abfb](https://github.com/commit/e98abfb771ab2ea6bb30b2d3580fc5fb18fc7b04))
* **glossary:** X3TRADOC-7774 Add flags for English (Australia) & English (South Africa) ([#541](https://github.com/issues/541))  ([5c4abba](https://github.com/commit/5c4abbafc121ce5cf81d66067113c33ff6991ec8))
* **glossary:** X3TRADOC-7861 Change language(s) and term(s) fields in the helper panel to static content field ([#566](https://github.com/issues/566))  ([82a69e5](https://github.com/commit/82a69e511e4e5750cc0bfc636fbbc7161a8f4aae))
* **glossary:** X3TRADOC-8101 Create navigation panel for terms & add language filter ([#1054](https://github.com/issues/1054))  ([f17bdef](https://github.com/commit/f17bdef1ef64562f36f342c9c64c65400f490b0e))
* **glossary:** X3TRADOC-8103 Add feedback and advance filter options to the top panel ([#1153](https://github.com/issues/1153))  ([47e0323](https://github.com/commit/47e03236d7e33c966245689494d66cbc3a9b3751))
* **glossary:** X3TRADOC-8104 agGrid with the translations  ([6fc8b58](https://github.com/commit/6fc8b58bbb50cadf8a19355946d209c0d3b405b9))
* **glossary:** X3TRADOC-8104 updates based on review  ([186781d](https://github.com/commit/186781d0da7a7c46ac853d12f5dbce9f27a8ecc4))
* **glossary:** X3TRADOC-8104 updates based on review  ([0d5e055](https://github.com/commit/0d5e05576da28032b391c8c8a0a902bfd7204755))
* **glossary:** X3TRADOC-8105 Changes due to platform  ([1558313](https://github.com/commit/15583133395555c014691028f438b1083ae28802))
* **glossary:** X3TRADOC-8105 Previous/Next button  ([82c0cc3](https://github.com/commit/82c0cc3435657aed54cbebd3fb59757e2dbad298))
* **glossary:** X3TRADOC-8106 agGrid with products & domains ([#1105](https://github.com/issues/1105))  ([c993983](https://github.com/commit/c9939836a7369138f03a070758c9cfdd3ec36b54))
* **glossary:** X3TRADOC-8107 Define main section  ([8a02dd1](https://github.com/commit/8a02dd1ed92c464b03843f1214d0d5f1115312f7))
* **glossary:** X3TRADOC-8120 Empty state  ([5192576](https://github.com/commit/51925765efc24c95e04d75d05b83165ef64049ac))
* **glossary:** X3TRADOC-8122 add flags to glossary node ([#1028](https://github.com/issues/1028))  ([2bd1af2](https://github.com/commit/2bd1af21a655973af9e6f768628198615047145f))
* **glossary:** X3TRADOC-8236 Update the glossary content to the latest ([#2972](https://github.com/issues/2972))  ([a3b56a2](https://github.com/commit/a3b56a26a296e9b5566dd91ecad6a028f976e421))
* **glossary:** X3TRADOC-8314 Create navigation panel for terms & add language filter ([#1677](https://github.com/issues/1677))  ([855f984](https://github.com/commit/855f9847ff55b901447bb481fa71d65e2a7f92f4))
* **glossary:** X3TRADOC-8315 admin page Previous/Next buttons ([#1683](https://github.com/issues/1683))  ([02a2e81](https://github.com/commit/02a2e81f562fbbd01d969dfeb561592ab6f687d8))
* **glossary:** X3TRADOC-8316 crud buttons ([#1694](https://github.com/issues/1694))  ([80f8a4d](https://github.com/commit/80f8a4deaa9006e96bc5b28e2aeb228d975c86a3))
* **glossary:** X3TRADOC-8318 admin empty state  ([c832832](https://github.com/commit/c8328320ac2b718d5c151ade90408185977e6962))
* **glossary:** X3TRADOC-8318 clean install  ([35bcd95](https://github.com/commit/35bcd95d6248df9865f4ec92b59ff2f76ead530e))
* **glossary:** X3TRADOC-8318 remove space  ([a8d5c03](https://github.com/commit/a8d5c0303fcef039000528c36448e5a754ad3614))
* **glossary:** X3TRADOC-8318 remove space  ([4797500](https://github.com/commit/47975002bca7801e25e28c8691df80b54a3c2054))
* **glossary:** X3TRADOC-8334 Create new admin page ([#1652](https://github.com/issues/1652))  ([832067f](https://github.com/commit/832067fb6f9b2d4c8c593723ba45cffd27a08331))
* **glossary:** X3TRADOC-8338 Tabs in the central panel ([#1931](https://github.com/issues/1931))  ([434327e](https://github.com/commit/434327e6d0b63159beac471a8c05db0b8e30436c))
* **glossary:** X3TRADOC-8346 Languages and terms grid ([#1785](https://github.com/issues/1785))  ([ef10102](https://github.com/commit/ef10102d0dcb7d617ac2eb16f983cbe73f64748f))
* **glossary:** X3TRADOC-8347  Term, abbreviation & definition fields ([#1806](https://github.com/issues/1806))  ([c1478f7](https://github.com/commit/c1478f7a33f3c0f98614905f9cb0953d841214e9))
* **glossary:** X3TRADOC-8348 Where used grid ([#2284](https://github.com/issues/2284))  ([5ff789a](https://github.com/commit/5ff789ae5f5bb334a277a2458e6902b44acf0ead))
* **glossary:** X3TRADOC-8349 adding a new term  ([76b9ce6](https://github.com/commit/76b9ce69a3129251ed492ca1937271b4c4bc2b99))
* **glossary:** X3TRADOC-8349 Adding a new term in English  ([bdebc3a](https://github.com/commit/bdebc3ad36747dfdf2039640745ea65ab82ab33d))
* **glossary:** X3TRADOC-8349 Adding new term in English  ([ac405bf](https://github.com/commit/ac405bf7a19cf4d25232a28d98d566e410e8c573))
* **glossary:** X3TRADOC-8350 adding new translation ([#2452](https://github.com/issues/2452))  ([525e2ca](https://github.com/commit/525e2ca1100c343a198b40bbeebc9b75a5485556))
* **glossary:** X3TRADOC-8351 Editing a term  ([5aa69ae](https://github.com/commit/5aa69ae563efc5616226cf364d77f9c42b780be2))
* **glossary:** X3TRADOC-8351 Fix the merge conflict  ([516635b](https://github.com/commit/516635ba654d2697198b774ab83513e07ce350e9))
* **glossary:** X3TRADOC-8351 Fix the merge conflict  ([2af5f2c](https://github.com/commit/2af5f2cd3fce678a685fba8cbf88bf154f6ded33))
* **glossary:** X3TRADOC-8352 Admin Deleting a term  ([c59e955](https://github.com/commit/c59e9552ac08babf60eaa9247b01b6f0359615fd))
* **glossary:** X3TRADOC-8353 navigating between different languages for the same term ([#1922](https://github.com/issues/1922))  ([cf3de3e](https://github.com/commit/cf3de3eeee512386da45c9196ad608319ac84f29))
* **glossary:** X3TRADOC-8354 Admin Deleting a translation  ([04b46fa](https://github.com/commit/04b46fa73fc839f61d9bbff52dc35f494e571a6a))
* **glossary:** X3TRADOC-8682 Restore admin menu  ([c378a1e](https://github.com/commit/c378a1e003428f5e2ae99fc5c36953ac3b56a552))
* **glossary:** X3TRADOC-8686 display product, domain & definition in the navigation panel ([#2682](https://github.com/issues/2682))  ([c10bf6e](https://github.com/commit/c10bf6ec44e33a74547c2712316187ef7da47d4c))
* **glossary:** X3TRADOC-8742 Search in abbreviation field ([#2960](https://github.com/issues/2960))  ([faac603](https://github.com/commit/faac603b2a47127119c13b1dc4c9eb4e1c9cecf9))
* **platform:** XT-99999 Fix linter issues ([#1090](https://github.com/issues/1090))  ([7f3a4b9](https://github.com/commit/7f3a4b9a5efa4063eb3662059a8441a23e8434b6))
* **xtrem-client-gen:** XT-4667 add new binding clientNode interface ([#1951](https://github.com/issues/1951))  ([a1b6e63](https://github.com/commit/a1b6e63628240a47899e7951be0cc27eea453b97))
* **xtrem-core/collection:** Add required property to collection.  ([#495](https://github.com/issues/495))  ([89088db](https://github.com/commit/89088db7c589ae8c5b372cf1358a3c3eaae7e927))
* **xtrem:** XT-4812 refactor menu item names ([#820](https://github.com/issues/820))  ([689be88](https://github.com/commit/689be88e75c78544e5bd2ead862fb8abcb8c239d))

