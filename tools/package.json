{"name": "@sage/xtrem~tools", "description": "Umbrella project for Xtrem development", "version": "54.0.58", "license": "UNLICENSED", "scripts": {"build": "cd .. && pnpm build:tools", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "cd .. && pnpm lint:tools", "sqs:clean": "cd .. && pnpm sqs:clean", "sqs:reset": "cd .. && pnpm sqs:reset", "sqs:setup": "cd .. && pnpm sqs:setup", "sqs:stop": "cd .. && pnpm sqs:stop", "test": "cd .. && pnpm test:tools"}}