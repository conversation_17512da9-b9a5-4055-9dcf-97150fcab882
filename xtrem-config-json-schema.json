{"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "definitions": {"AddOnConfig": {"additionalProperties": false, "properties": {"folder": {"type": "string"}}, "type": "object"}, "AppsConfig": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "AuthenticationContainerConfig": {"additionalProperties": false, "description": "The config used to start the Authentication service container in prod-ui mode.\n\nThe values for <clientId> and <clientSecret> can be found in Keeper (Global XTreem/Authentication service/Authentication service)", "properties": {"clientId": {"description": "The CLIENT_ID to start the authentication service container", "type": "string"}, "clientSecret": {"description": "The CLIENT_SECRET to start the authentication service container", "type": "string"}}, "type": "object"}, "AuthenticationServiceConfig": {"additionalProperties": false, "properties": {"interopUrl": {"type": "string"}, "ssl": {"$ref": "#/definitions/Pick<TlsOptions,\"key\"|\"cert\"|\"ca\">"}}, "type": "object"}, "AwsConfig": {"additionalProperties": false, "properties": {"region": {"type": "string"}}, "type": "object"}, "BlockList": {"additionalProperties": false, "description": "The `BlockList` object can be used with some network APIs to specify rules for\ndisabling inbound or outbound access to specific IP addresses, IP ranges, or\nIP subnets.", "properties": {"rules": {"description": "The list of rules added to the blocklist.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ClusterConfig": {"additionalProperties": false, "properties": {"numberOfForkedProcesses": {"type": "number"}}, "type": "object"}, "CopilotConfig": {"additionalProperties": false, "properties": {"accessCodeLifeTimeInMinutes": {"description": "ttl for the access code in minutes", "type": "number"}, "audience": {"description": "Copilot sage id audience", "type": "string"}, "clientId": {"description": "Copilot sage id client id", "type": "string"}, "clientSecret": {"description": "Copilot sage id client secret", "type": "string"}, "gmsClient": {"description": "Client name passed to the GMS Service e.g. sdmo_v1", "type": "string"}, "oauthEndpointUrl": {"description": "oauth url", "type": "string"}, "serviceUrl": {"description": "Copilot service url", "type": "string"}}, "type": "object"}, "DebugMetricsOptions": {"additionalProperties": {"$ref": "#/definitions/T"}, "description": "Configuration of debug metrics", "type": "object"}, "Dict<Iterable<string>|null>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "Dict<LogDomain>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "Dict<QueueConfig>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "Dict<S3Config>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "Dict<WebSocketConfig>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "Dict<any>": {"additionalProperties": {"$ref": "#/definitions/T"}, "type": "object"}, "ExportCsvOptions": {"additionalProperties": false, "properties": {"chunkSize": {"type": "number"}, "maxRetryUploadedFileCreation": {"type": "number"}}, "type": "object"}, "GraphQlConfig": {"additionalProperties": false, "properties": {"isReadonly": {"type": "boolean"}, "keepLocalConfig": {"type": "boolean"}, "maxNodesPerPage": {"type": "number"}, "timeLimitInSeconds": {"type": "number"}}, "type": "object"}, "ImportCsvOptions": {"additionalProperties": false, "properties": {"chunkSize": {"type": "number"}}, "type": "object"}, "InteropConfig": {"additionalProperties": false, "properties": {"appsHealthCheckSeconds": {"description": "interval of apps health check in seconds (default to 60)", "type": "number"}, "concurrentMessagesLimit": {"description": "size of the receiving message funnel", "type": "number"}, "concurrentNotificationsLimit": {"description": "size of the receiving notification funnel", "type": "number"}, "devEndpoint": {"description": "elasticmq endpoint", "type": "string"}, "graphqlTimeLimitInSeconds": {"description": "timeout in seconds for the interop graqhql request to be completed (default to 120)", "type": "number"}, "heartbeatSeconds": {"description": "heartbeat rate to refresh list of live containers", "type": "number"}, "listenerMonitoringSeconds": {"description": "interval of monitoring the listening queues in seconds (default to 1)", "type": "number"}, "messageVisibilitySeconds": {"description": "time in seconds received message is made invisible on SQS (default 30 seconds)", "type": "number"}, "queueUrlDeadLetterTemplate": {"description": "handlebars template for dead letter queue URL (cluster V2)", "type": "string"}, "queueUrlTemplate": {"description": "handlebars template for queue URL (cluster V2)", "type": "string"}, "queues": {"$ref": "#/definitions/Dict<QueueConfig>"}, "receiveLowerBound": {"description": "lower bound list size for the messages that are in progress (default to 6)", "type": "number"}, "receivePollingSeconds": {"description": "interval to wait when polling receive queue(default to 1)", "type": "number"}, "receiveRetryCount": {"description": "maximum number of retries trying to receive messages from SQS queue before error (default to 3)", "type": "number"}, "receiveRetrySeconds": {"description": "interval of retries in seconds (default to 1)", "type": "number"}, "receiveUpperBound": {"description": "upper bound list size for the messages that are in progress (default to 11)", "type": "number"}, "remoteQuery": {"$ref": "#/definitions/InteropRemoteQueryConfig", "description": "remote query configuration"}, "routingPollingSeconds": {"description": "interval of notifications pooling in seconds (default to 1)", "type": "number"}, "routingReadCount": {"description": "maximum number of notifications pooling (default to 3)", "type": "number"}, "runningJobListTtlInSeconds": {"description": "The time in seconds used to refresh the cache of SysNotificationState", "type": "number"}, "sendRetryCount": {"description": "maximum number of retries trying to receive messages from SQS queue before error (default to 3)", "type": "number"}, "sendRetrySeconds": {"description": "interval of retries in seconds (default to 90)", "type": "number"}, "sqsGroupsPerTenantUser": {"description": "Number of SQS groups per tenant user (default to 5).\nA given tenant user cannot have more than this number of SQS messages being processed simultaneously", "type": "number"}}, "type": "object"}, "InteropRemoteQueryConfig": {"additionalProperties": false, "properties": {"maxNodesPerPage": {"description": "Maximum number of nodes per page", "type": "number"}, "maxNodesPerQuery": {"description": "maximum number of nodes per query result", "type": "number"}}, "type": "object"}, "KeyObject": {"additionalProperties": false, "properties": {"passphrase": {"description": "Optional passphrase.", "type": "string"}, "pem": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}], "description": "Private keys in PEM format."}}, "type": "object"}, "LogOptions": {"additionalProperties": false, "properties": {"json": {"type": "boolean"}, "noColor": {"type": "boolean"}}, "type": "object"}, "LogsConfig": {"additionalProperties": false, "properties": {"disabled": {"description": "Are all logs disabled?", "type": "boolean"}, "disabledForTests": {"description": "Are all logs disabled when running tests?", "type": "boolean"}, "domains": {"$ref": "#/definitions/Dict<LogDomain>", "additionalProperties": true}, "filenamePrefix": {"type": "string"}, "options": {"$ref": "#/definitions/LogOptions"}, "outputFolder": {"type": "string"}}, "type": "object"}, "NewRelicConfig": {"additionalProperties": false, "properties": {"accountId": {"type": "string"}, "applicationId": {"type": "string"}, "licenceKey": {"type": "string"}, "trustKey": {"type": "string"}}, "type": "object"}, "PendoOptions": {"additionalProperties": false, "properties": {"apiKey": {"description": "The API key", "type": "string"}, "clusterTag": {"description": "pendo tag to identify cluster type", "type": "string"}, "subscriptionId": {"description": "pendo subscription id", "type": "string"}}, "type": "object"}, "Pick<TlsOptions,\"key\"|\"cert\"|\"ca\">": {"additionalProperties": false, "properties": {"ca": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Optionally override the trusted CA certificates. De<PERSON><PERSON> is to trust\nthe well-known CAs curated by Mozilla. Mozilla's CAs are completely\nreplaced when CAs are explicitly specified using this option."}, "cert": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Cert chains in PEM format. One cert chain should be provided per\nprivate key. Each cert chain should consist of the PEM formatted\ncertificate for a provided private key, followed by the PEM\nformatted intermediate certificates (if any), in order, and not\nincluding the root CA (the root CA must be pre-known to the peer,\nsee ca). When providing multiple cert chains, they do not have to\nbe in the same order as their private keys in key. If the\nintermediate certificates are not provided, the peer will not be\nable to validate the certificate, and the handshake will fail."}, "key": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"$ref": "#/definitions/KeyObject"}, {"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Private keys in PEM format. PEM allows the option of private keys\nbeing encrypted. Encrypted keys will be decrypted with\noptions.passphrase. Multiple keys using different algorithms can be\nprovided either as an array of unencrypted key strings or buffers,\nor an array of objects in the form {pem: <string|buffer>[,\npassphrase: <string>]}. The object form can only occur in an array.\nobject.passphrase is optional. Encrypted keys will be decrypted with\nobject.passphrase if provided, or options.passphrase if it is not."}}, "type": "object"}, "PrefetchConfig": {"additionalProperties": false, "description": "Configuration for SQL prefetch", "properties": {"isDisabled": {"description": "Is prefetch disabled", "type": "boolean"}, "logCounters": {"description": "Do we log the SQL counters?\nCounters are collected for each transaction, and logged at the end of the transaction.\nCounters are also collected globally, and logged every 5 (configurable) seconds.", "type": "boolean"}, "logCountersIntervalInSeconds": {"description": "Number of seconds between two logs of the global SQL counters", "type": "number"}, "logStatements": {"description": "Do we log SQL statements (only verb + node name)", "type": "boolean"}, "spiedNodeNames": {"description": "Node names for which we are logging SQL sql statements\nEntries prefixed by ~ are interpreted as regex", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PxfObject": {"additionalProperties": false, "properties": {"buf": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}], "description": "PFX or PKCS12 encoded private key and certificate chain."}, "passphrase": {"description": "Optional passphrase.", "type": "string"}}, "type": "object"}, "ReportOptions": {"additionalProperties": false, "properties": {"browserTimeout": {"description": "The maximum time in milliseconds to wait for the browser to start. Pass 0 to disable it. (default to 180000)", "type": "number"}, "maxTotalPages": {"type": "number"}, "pageOpeningTimeout": {"type": "number"}, "pdfTransformationTimeout": {"description": "The maximum time in milliseconds for page PDF transformation. Pass 0 to disable it. (default to 120000)", "type": "number"}, "protocolTimeout": {"description": "The maximum time for individual protocol calls in milliseconds (default to 600000)", "type": "number"}}, "type": "object"}, "S3StorageConfig": {"additionalProperties": false, "properties": {"localBasePath": {"type": "string"}, "s3BucketUrlPrefix": {"description": "For presigned URLs, we need this when adding an exception to CSP for uploading of files by the client\nExample: https://xtrem-dev-eu-showcase.s3.eu-west-1.amazonaws.com", "type": "string"}, "s3ClusterBucket": {"description": "Cluster's S3 bucket name used to pass to file storage instance, to upload/download tenant specific files", "type": "string"}, "s3UpgradeMetricsFolder": {"description": "The (optional) folder (s3ClusterBucket) from where the upgrade metrics should be uploaded", "type": "string"}}, "type": "object"}, "SecureContext": {"additionalProperties": false, "properties": {"context": {}}, "type": "object"}, "SecurityConfig": {"additionalProperties": false, "properties": {"audience": {"type": "string"}, "enableAutoRefresh": {"type": "boolean"}, "issuer": {"type": "string"}, "jsEval": {"additionalProperties": false, "properties": {"timeoutInMillis": {"type": "number"}}, "type": "object"}, "jwksUrl": {"type": "string"}, "loginUrl": {"type": "string"}, "redirectUrl": {"type": "string"}, "renewalThreshold": {"type": "number"}, "renewalUrl": {"type": "string"}, "services": {"$ref": "#/definitions/SecurityServicesConfig"}, "sizeLimits": {"$ref": "#/definitions/SizeLimits"}, "tls": {"additionalProperties": false, "properties": {"extraCaFiles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "string"}]}}, "type": "object"}}, "type": "object"}, "SecurityServicesConfig": {"additionalProperties": false, "properties": {"helmet": {"additionalProperties": false, "properties": {"csp": {"additionalProperties": false, "properties": {"directives": {"$ref": "#/definitions/Dict<Iterable<string>|null>", "additionalProperties": true}}, "type": "object"}, "disabledOptions": {"additionalProperties": false, "properties": {"csp": {"type": "boolean"}, "hsts": {"type": "boolean"}}, "type": "object"}, "hsts": {"anyOf": [{"$ref": "#/definitions/StrictTransportSecurityOptions"}, {"type": "boolean"}]}}, "type": "object"}, "tokenInvalidation": {"additionalProperties": false, "properties": {"active": {"type": "boolean"}}, "type": "object"}, "tooBusy": {"additionalProperties": false, "properties": {"disabled": {"type": "boolean"}, "intervalInMillis": {"type": "number"}, "maxLagInMillis": {"type": "number"}, "retryAfterInSeconds": {"type": "number"}, "smoothingFactor": {"type": "number"}}, "type": "object"}}, "type": "object"}, "ServerConfig": {"additionalProperties": false, "properties": {"interopPort": {"type": "number"}, "metricsPort": {"type": "number"}, "port": {"type": "number"}, "requestFunnelSizeFactor": {"description": "the factor to apply to the max value of the sql config in order to compute the final request funnel size", "type": "number"}, "ssl": {"$ref": "#/definitions/TlsOptions"}, "worker": {"$ref": "#/definitions/WorkerConfig"}}, "type": "object"}, "ServiceOptionStatus": {"enum": ["experimental", "released", "workInProgress"], "type": "string"}, "ServiceOptions": {"additionalProperties": false, "properties": {"level": {"$ref": "#/definitions/ServiceOptionStatus"}}, "type": "object"}, "SizeLimits": {"additionalProperties": false, "properties": {"maxRequestSize": {"type": "string"}, "maxStreamSize": {"type": "string"}, "maxUploadSize": {"type": "string"}}, "type": "object"}, "SqlConfig": {"additionalProperties": false, "properties": {"connectionMaxRetries": {"description": "Number of times to retry SQL connection - default 3", "type": "number"}, "connectionRetryMillis": {"description": "Milliseconds to wait before retrying connection - default 2000", "type": "number"}, "connectionTimeoutMillis": {"description": "Number of milliseconds to wait before timing out when connecting a new client - default 5000", "type": "number"}, "database": {"type": "string"}, "delayBeforeRetry": {"description": "Delay before retrying SQL statement on error", "type": "number"}, "hostname": {"type": "string"}, "idleTimeoutMillis": {"description": "Number of milliseconds before a client is terminated if it is idle - default 10000", "type": "number"}, "mapArgsInLogs": {"description": "Should all the $xx parameters be replaced by their value ?", "type": "boolean"}, "max": {"description": "Pool size - default 20", "type": "number"}, "maxRetriesOnTransactionConflicts": {"description": "Max number of retries on conflicts between transactions", "type": "number"}, "maxSubQueryDepth": {"description": "Maximum subquery depth that the ts-to-sql converter accepts", "type": "number"}, "maxTries": {"description": "Max number of attempts to execute a SQL statement error", "type": "number"}, "maxUses": {"description": "Number of transactions per connection before closing to cycle connections - default 7500", "type": "number"}, "password": {"type": "string"}, "poolMaxIdleSeconds": {"type": "number"}, "port": {"type": "number"}, "readonlyHostname": {"description": "Read-Only db replica hostname", "type": "string"}, "readonlyPort": {"description": "Read-Only db replica port", "type": "number"}, "sqlStatementCacheSize": {"description": "Size of cache of SQL statements - default 1000", "type": "number"}, "ssl": {"anyOf": [{"$ref": "#/definitions/TlsConnectionOptions"}, {"type": "boolean"}], "description": "SSL config"}, "statementTimeoutMillis": {"description": "Number of milliseconds before a statement in query will time out, default is no timeout.\nhttps://node-postgres.com/apis/client\nhttps://stackoverflow.com/questions/59155572/how-to-set-query-timeout-in-relation-to-statement-timeout", "type": "number"}, "subscriber": {"$ref": "#/definitions/SubscriberConfig", "description": "Subscriber config"}, "sysDatabase": {"type": "string"}, "sysPassword": {"type": "string"}, "sysUser": {"type": "string"}, "user": {"type": "string"}}, "type": "object"}, "StorageConfig": {"additionalProperties": false, "properties": {"managedExternal": {"description": "Is storage managed by an external storage engine (not PostgreSQL)", "type": "boolean"}, "maxConnections": {"description": "pool size, for external storage engines (postgres uses storage.sql.max)", "type": "number"}, "prefetch": {"$ref": "#/definitions/PrefetchConfig", "description": "SQL prefetch configuration"}, "sql": {"$ref": "#/definitions/SqlConfig", "description": "Postgres configuration"}}, "type": "object"}, "StrictTransportSecurityOptions": {"additionalProperties": false, "properties": {"includeSubDomains": {"type": "boolean"}, "maxAge": {"type": "number"}, "preload": {"type": "boolean"}}, "type": "object"}, "SubscriberConfig": {"additionalProperties": false, "properties": {"paranoidChecking": {"anyOf": [{"const": false, "type": "boolean"}, {"type": "number"}], "description": "Interval in ms to run a trivial query on the DB to see if\nthe database connection still works.\nDefaults to 30s."}, "retryInterval": {"anyOf": [{"additionalProperties": false, "type": "object"}, {"type": "number"}], "description": "How much time to wait between reconnection attempts (if failed).\nCan also be a callback returning a delay in milliseconds.\nDefaults to 500 ms."}, "retryLimit": {"description": "How many attempts to reconnect after connection loss.\nDefaults to no limit, but a default retryTimeout is set.", "type": "number"}, "retryTimeout": {"description": "Timeout in ms after which to stop retrying and just fail. Defaults to 3000 ms.", "type": "number"}}, "type": "object"}, "SystemConfig": {"additionalProperties": false, "properties": {"dnsCache": {"additionalProperties": false, "description": "interval of dns cache in seconds (default to 30)", "properties": {"cachesize": {"description": "maximum number of entries to store in the cache (default to 1000)", "type": "number"}, "enable": {"description": "enable dns cache (default to true)", "type": "boolean"}, "ttl": {"description": "time to live in seconds (default to 30)", "type": "number"}}, "type": "object"}, "natIpAdresses": {"description": "a comma separated list of nat IP addresses", "type": "string"}}, "type": "object"}, "T": {"additionalProperties": false, "type": "object"}, "TlsConnectionOptions": {"additionalProperties": false, "properties": {"ALPNCallback": {"additionalProperties": false, "description": "If set, this will be called when a client opens a connection using the ALPN extension.\nOne argument will be passed to the callback: an object containing `servername` and `protocols` fields,\nrespectively containing the server name from the SNI extension (if any) and an array of\nALPN protocol name strings. The callback must return either one of the strings listed in `protocols`,\nwhich will be returned to the client as the selected ALPN protocol, or `undefined`,\nto reject the connection with a fatal alert. If a string is returned that does not match one of\nthe client's ALPN protocols, an error will be thrown.\nThis option cannot be used with the `ALPNProtocols` option, and setting both options will throw an error.", "type": "object"}, "ALPNProtocols": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"items": {"type": "number"}, "type": "array"}, {"items": {"items": {"type": "number"}, "type": "array"}, "type": "array"}], "description": "An array of strings or a Buffer naming possible ALPN protocols.\n(Protocols should be ordered by their priority.)"}, "SNICallback": {"additionalProperties": false, "description": "SNICallback(servername, cb) <Function> A function that will be\ncalled if the client supports SNI TLS extension. Two arguments\nwill be passed when called: servername and cb. SNICallback should\ninvoke cb(null, ctx), where ctx is a SecureContext instance.\n(tls.createSecureContext(...) can be used to get a proper\nSecureContext.) If SNICallback wasn't provided the default callback\nwith high-level API will be used (see below).", "type": "object"}, "allowPartialTrustChain": {"description": "Treat intermediate (non-self-signed)\ncertificates in the trust CA certificate list as trusted.", "type": "boolean"}, "ca": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Optionally override the trusted CA certificates. De<PERSON><PERSON> is to trust\nthe well-known CAs curated by Mozilla. Mozilla's CAs are completely\nreplaced when CAs are explicitly specified using this option."}, "cert": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Cert chains in PEM format. One cert chain should be provided per\nprivate key. Each cert chain should consist of the PEM formatted\ncertificate for a provided private key, followed by the PEM\nformatted intermediate certificates (if any), in order, and not\nincluding the root CA (the root CA must be pre-known to the peer,\nsee ca). When providing multiple cert chains, they do not have to\nbe in the same order as their private keys in key. If the\nintermediate certificates are not provided, the peer will not be\nable to validate the certificate, and the handshake will fail."}, "ciphers": {"description": "Cipher suite specification, replacing the default. For more\ninformation, see modifying the default cipher suite. Permitted\nciphers can be obtained via tls.getCiphers(). Cipher names must be\nuppercased in order for OpenSSL to accept them.", "type": "string"}, "clientCertEngine": {"description": "Name of an OpenSSL engine which can provide the client certificate.", "type": "string"}, "crl": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "PEM formatted CRLs (Certificate Revocation Lists)."}, "dhparam": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}], "description": "`'auto'` or custom Di<PERSON><PERSON>-<PERSON> parameters, required for non-ECDHE perfect forward secrecy.\nIf omitted or invalid, the parameters are silently discarded and DHE ciphers will not be available.\nECDHE-based perfect forward secrecy will still be available."}, "ecdhCurve": {"description": "A string describing a named curve or a colon separated list of curve\nNIDs or names, for example P-521:P-384:P-256, to use for ECDH key\nagreement. Set to auto to select the curve automatically. Use\ncrypto.getCurves() to obtain a list of available curve names. On\nrecent releases, openssl ecparam -list_curves will also display the\nname and description of each available elliptic curve. Default:\ntls.DEFAULT_ECDH_CURVE.", "type": "string"}, "enableTrace": {"default": false, "description": "When enabled, TLS packet trace information is written to `stderr`. This can be\nused to debug TLS connection problems.", "type": "boolean"}, "honorCipherOrder": {"description": "Attempt to use the server's cipher suite preferences instead of the\nclient's. When true, causes SSL_OP_CIPHER_SERVER_PREFERENCE to be\nset in secureOptions", "type": "boolean"}, "key": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"$ref": "#/definitions/KeyObject"}, {"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Private keys in PEM format. PEM allows the option of private keys\nbeing encrypted. Encrypted keys will be decrypted with\noptions.passphrase. Multiple keys using different algorithms can be\nprovided either as an array of unencrypted key strings or buffers,\nor an array of objects in the form {pem: <string|buffer>[,\npassphrase: <string>]}. The object form can only occur in an array.\nobject.passphrase is optional. Encrypted keys will be decrypted with\nobject.passphrase if provided, or options.passphrase if it is not."}, "maxVersion": {"description": "Optionally set the maximum TLS version to allow. One\nof `'TLSv1.3'`, `'TLSv1.2'`, `'TLSv1.1'`, or `'TLSv1'`. Cannot be specified along with the\n`secureProtocol` option, use one or the other.\n**Default:** `'TLSv1.3'`, unless changed using CLI options. Using\n`--tls-max-v1.2` sets the default to `'TLSv1.2'`. Using `--tls-max-v1.3` sets the default to\n`'TLSv1.3'`. If multiple of the options are provided, the highest maximum is used.", "enum": ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"], "type": "string"}, "minVersion": {"description": "Optionally set the minimum TLS version to allow. One\nof `'TLSv1.3'`, `'TLSv1.2'`, `'TLSv1.1'`, or `'TLSv1'`. Cannot be specified along with the\n`secureProtocol` option, use one or the other.  It is not recommended to use\nless than TLSv1.2, but it may be required for interoperability.\n**Default:** `'TLSv1.2'`, unless changed using CLI options. Using\n`--tls-v1.0` sets the default to `'TLSv1'`. Using `--tls-v1.1` sets the default to\n`'TLSv1.1'`. Using `--tls-min-v1.3` sets the default to\n'TLSv1.3'. If multiple of the options are provided, the lowest minimum is used.", "enum": ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"], "type": "string"}, "passphrase": {"description": "Shared passphrase used for a single private key and/or a PFX.", "type": "string"}, "pfx": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"$ref": "#/definitions/PxfObject"}, {"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "PFX or PKCS12 encoded private key and certificate chain. pfx is an\nalternative to providing key and cert individually. PFX is usually\nencrypted, if it is, passphrase will be used to decrypt it. Multiple\nPFX can be provided either as an array of unencrypted PFX buffers,\nor an array of objects in the form {buf: <string|buffer>[,\npassphrase: <string>]}. The object form can only occur in an array.\nobject.passphrase is optional. Encrypted PFX will be decrypted with\nobject.passphrase if provided, or options.passphrase if it is not."}, "privateKeyEngine": {"description": "Name of an OpenSSL engine to get private key from. Should be used\ntogether with privateKeyIdentifier.", "type": "string"}, "privateKeyIdentifier": {"description": "Identifier of a private key managed by an OpenSSL engine. Should be\nused together with privateKeyEngine. Should not be set together with\nkey, because both options define a private key in different ways.", "type": "string"}, "rejectUnauthorized": {"default": true, "description": "If true the server will reject any connection which is not\nauthorized with the list of supplied CAs. This option only has an\neffect if requestCert is true.", "type": "boolean"}, "requestCert": {"description": "If true the server will request a certificate from clients that\nconnect and attempt to verify that certificate. Defaults to\nfalse.", "type": "boolean"}, "secureContext": {"$ref": "#/definitions/SecureContext", "description": "An optional TLS context object from tls.createSecureContext()"}, "secureOptions": {"description": "Optionally affect the OpenSSL protocol behavior, which is not\nusually necessary. This should be used carefully if at all! Value is\na numeric bitmask of the SSL_OP_* options from OpenSSL Options", "type": "number"}, "secureProtocol": {"description": "Legacy mechanism to select the TLS protocol version to use, it does\nnot support independent control of the minimum and maximum version,\nand does not support limiting the protocol to TLSv1.3. Use\nminVersion and maxVersion instead. The possible values are listed as\nSSL_METHODS, use the function names as strings. For example, use\n'TLSv1_1_method' to force TLS version 1.1, or 'TLS_method' to allow\nany TLS protocol version up to TLSv1.3. It is not recommended to use\nTLS versions less than 1.2, but it may be required for\ninteroperability. Default: none, see minVersion.", "type": "string"}, "sessionIdContext": {"description": "Opaque identifier used by servers to ensure session state is not\nshared between applications. Unused by clients.", "type": "string"}, "sessionTimeout": {"description": "The number of seconds after which a TLS session created by the\nserver will no longer be resumable. See Session Resumption for more\ninformation. Default: 300.", "type": "number"}, "sigalgs": {"description": "Colon-separated list of supported signature algorithms. The list\ncan contain digest algorithms (SHA256, MD5 etc.), public key\nalgorithms (RSA-PSS, ECDSA etc.), combination of both (e.g\n'RSA+SHA384') or TLS v1.3 scheme names (e.g. rsa_pss_pss_sha512).", "type": "string"}, "ticketKeys": {"description": "48-bytes of cryptographically strong pseudo-random data.\nSee Session Resumption for more information.", "items": {"type": "number"}, "type": "array"}}, "type": "object"}, "TlsOptions": {"additionalProperties": false, "properties": {"ALPNCallback": {"additionalProperties": false, "description": "If set, this will be called when a client opens a connection using the ALPN extension.\nOne argument will be passed to the callback: an object containing `servername` and `protocols` fields,\nrespectively containing the server name from the SNI extension (if any) and an array of\nALPN protocol name strings. The callback must return either one of the strings listed in `protocols`,\nwhich will be returned to the client as the selected ALPN protocol, or `undefined`,\nto reject the connection with a fatal alert. If a string is returned that does not match one of\nthe client's ALPN protocols, an error will be thrown.\nThis option cannot be used with the `ALPNProtocols` option, and setting both options will throw an error.", "type": "object"}, "ALPNProtocols": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"items": {"type": "number"}, "type": "array"}, {"items": {"items": {"type": "number"}, "type": "array"}, "type": "array"}], "description": "An array of strings or a Buffer naming possible ALPN protocols.\n(Protocols should be ordered by their priority.)"}, "SNICallback": {"additionalProperties": false, "description": "SNICallback(servername, cb) <Function> A function that will be\ncalled if the client supports SNI TLS extension. Two arguments\nwill be passed when called: servername and cb. SNICallback should\ninvoke cb(null, ctx), where ctx is a SecureContext instance.\n(tls.createSecureContext(...) can be used to get a proper\nSecureContext.) If SNICallback wasn't provided the default callback\nwith high-level API will be used (see below).", "type": "object"}, "allowHalfOpen": {"default": false, "description": "Indicates whether half-opened TCP connections are allowed.", "type": "boolean"}, "allowPartialTrustChain": {"description": "Treat intermediate (non-self-signed)\ncertificates in the trust CA certificate list as trusted.", "type": "boolean"}, "blockList": {"$ref": "#/definitions/BlockList", "description": "`blockList` can be used for disabling inbound\naccess to specific IP addresses, IP ranges, or IP subnets. This does not\nwork if the server is behind a reverse proxy, NAT, etc. because the address\nchecked against the block list is the address of the proxy, or the one\nspecified by the NAT."}, "ca": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Optionally override the trusted CA certificates. De<PERSON><PERSON> is to trust\nthe well-known CAs curated by Mozilla. Mozilla's CAs are completely\nreplaced when CAs are explicitly specified using this option."}, "cert": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Cert chains in PEM format. One cert chain should be provided per\nprivate key. Each cert chain should consist of the PEM formatted\ncertificate for a provided private key, followed by the PEM\nformatted intermediate certificates (if any), in order, and not\nincluding the root CA (the root CA must be pre-known to the peer,\nsee ca). When providing multiple cert chains, they do not have to\nbe in the same order as their private keys in key. If the\nintermediate certificates are not provided, the peer will not be\nable to validate the certificate, and the handshake will fail."}, "ciphers": {"description": "Cipher suite specification, replacing the default. For more\ninformation, see modifying the default cipher suite. Permitted\nciphers can be obtained via tls.getCiphers(). Cipher names must be\nuppercased in order for OpenSSL to accept them.", "type": "string"}, "clientCertEngine": {"description": "Name of an OpenSSL engine which can provide the client certificate.", "type": "string"}, "crl": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "PEM formatted CRLs (Certificate Revocation Lists)."}, "dhparam": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"type": "string"}], "description": "`'auto'` or custom Di<PERSON><PERSON>-<PERSON> parameters, required for non-ECDHE perfect forward secrecy.\nIf omitted or invalid, the parameters are silently discarded and DHE ciphers will not be available.\nECDHE-based perfect forward secrecy will still be available."}, "ecdhCurve": {"description": "A string describing a named curve or a colon separated list of curve\nNIDs or names, for example P-521:P-384:P-256, to use for ECDH key\nagreement. Set to auto to select the curve automatically. Use\ncrypto.getCurves() to obtain a list of available curve names. On\nrecent releases, openssl ecparam -list_curves will also display the\nname and description of each available elliptic curve. Default:\ntls.DEFAULT_ECDH_CURVE.", "type": "string"}, "enableTrace": {"default": false, "description": "When enabled, TLS packet trace information is written to `stderr`. This can be\nused to debug TLS connection problems.", "type": "boolean"}, "handshakeTimeout": {"description": "Abort the connection if the SSL/TLS handshake does not finish in the\nspecified number of milliseconds. A 'tlsClientError' is emitted on\nthe tls.Server object whenever a handshake times out. Default:\n120000 (120 seconds).", "type": "number"}, "highWaterMark": {"default": "See [stream.getDefaultHighWaterMark()](https://nodejs.org/docs/latest-v22.x/api/stream.html#streamgetdefaulthighwatermarkobjectmode).", "description": "Optionally overrides all `net.Socket`s' `readableHighWaterMark` and `writableHighWaterMark`.", "type": "number"}, "honorCipherOrder": {"description": "Attempt to use the server's cipher suite preferences instead of the\nclient's. When true, causes SSL_OP_CIPHER_SERVER_PREFERENCE to be\nset in secureOptions", "type": "boolean"}, "keepAlive": {"default": false, "description": "If set to `true`, it enables keep-alive functionality on the socket immediately after a new incoming connection is received,\nsimilarly on what is done in `socket.setKeepAlive([enable][, initialDelay])`.", "type": "boolean"}, "keepAliveInitialDelay": {"default": 0, "description": "If set to a positive number, it sets the initial delay before the first keepalive probe is sent on an idle socket.", "type": "number"}, "key": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"$ref": "#/definitions/KeyObject"}, {"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "Private keys in PEM format. PEM allows the option of private keys\nbeing encrypted. Encrypted keys will be decrypted with\noptions.passphrase. Multiple keys using different algorithms can be\nprovided either as an array of unencrypted key strings or buffers,\nor an array of objects in the form {pem: <string|buffer>[,\npassphrase: <string>]}. The object form can only occur in an array.\nobject.passphrase is optional. Encrypted keys will be decrypted with\nobject.passphrase if provided, or options.passphrase if it is not."}, "maxVersion": {"description": "Optionally set the maximum TLS version to allow. One\nof `'TLSv1.3'`, `'TLSv1.2'`, `'TLSv1.1'`, or `'TLSv1'`. Cannot be specified along with the\n`secureProtocol` option, use one or the other.\n**Default:** `'TLSv1.3'`, unless changed using CLI options. Using\n`--tls-max-v1.2` sets the default to `'TLSv1.2'`. Using `--tls-max-v1.3` sets the default to\n`'TLSv1.3'`. If multiple of the options are provided, the highest maximum is used.", "enum": ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"], "type": "string"}, "minVersion": {"description": "Optionally set the minimum TLS version to allow. One\nof `'TLSv1.3'`, `'TLSv1.2'`, `'TLSv1.1'`, or `'TLSv1'`. Cannot be specified along with the\n`secureProtocol` option, use one or the other.  It is not recommended to use\nless than TLSv1.2, but it may be required for interoperability.\n**Default:** `'TLSv1.2'`, unless changed using CLI options. Using\n`--tls-v1.0` sets the default to `'TLSv1'`. Using `--tls-v1.1` sets the default to\n`'TLSv1.1'`. Using `--tls-min-v1.3` sets the default to\n'TLSv1.3'. If multiple of the options are provided, the lowest minimum is used.", "enum": ["TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"], "type": "string"}, "noDelay": {"default": false, "description": "If set to `true`, it disables the use of <PERSON><PERSON>'s algorithm immediately after a new incoming connection is received.", "type": "boolean"}, "passphrase": {"description": "Shared passphrase used for a single private key and/or a PFX.", "type": "string"}, "pauseOnConnect": {"default": false, "description": "Indicates whether the socket should be paused on incoming connections.", "type": "boolean"}, "pfx": {"anyOf": [{"items": {"type": "number"}, "type": "array"}, {"items": {"anyOf": [{"$ref": "#/definitions/PxfObject"}, {"items": {"type": "number"}, "type": "array"}, {"type": "string"}]}, "type": "array"}, {"type": "string"}], "description": "PFX or PKCS12 encoded private key and certificate chain. pfx is an\nalternative to providing key and cert individually. PFX is usually\nencrypted, if it is, passphrase will be used to decrypt it. Multiple\nPFX can be provided either as an array of unencrypted PFX buffers,\nor an array of objects in the form {buf: <string|buffer>[,\npassphrase: <string>]}. The object form can only occur in an array.\nobject.passphrase is optional. Encrypted PFX will be decrypted with\nobject.passphrase if provided, or options.passphrase if it is not."}, "privateKeyEngine": {"description": "Name of an OpenSSL engine to get private key from. Should be used\ntogether with privateKeyIdentifier.", "type": "string"}, "privateKeyIdentifier": {"description": "Identifier of a private key managed by an OpenSSL engine. Should be\nused together with privateKeyEngine. Should not be set together with\nkey, because both options define a private key in different ways.", "type": "string"}, "pskIdentityHint": {"description": "hint to send to a client to help\nwith selecting the identity during TLS-PSK negotiation. Will be ignored\nin TLS 1.3. Upon failing to set pskIdentityHint `tlsClientError` will be\nemitted with `ERR_TLS_PSK_SET_IDENTIY_HINT_FAILED` code.", "type": "string"}, "rejectUnauthorized": {"default": true, "description": "If true the server will reject any connection which is not\nauthorized with the list of supplied CAs. This option only has an\neffect if requestCert is true.", "type": "boolean"}, "requestCert": {"description": "If true the server will request a certificate from clients that\nconnect and attempt to verify that certificate. Defaults to\nfalse.", "type": "boolean"}, "secureContext": {"$ref": "#/definitions/SecureContext", "description": "An optional TLS context object from tls.createSecureContext()"}, "secureOptions": {"description": "Optionally affect the OpenSSL protocol behavior, which is not\nusually necessary. This should be used carefully if at all! Value is\na numeric bitmask of the SSL_OP_* options from OpenSSL Options", "type": "number"}, "secureProtocol": {"description": "Legacy mechanism to select the TLS protocol version to use, it does\nnot support independent control of the minimum and maximum version,\nand does not support limiting the protocol to TLSv1.3. Use\nminVersion and maxVersion instead. The possible values are listed as\nSSL_METHODS, use the function names as strings. For example, use\n'TLSv1_1_method' to force TLS version 1.1, or 'TLS_method' to allow\nany TLS protocol version up to TLSv1.3. It is not recommended to use\nTLS versions less than 1.2, but it may be required for\ninteroperability. Default: none, see minVersion.", "type": "string"}, "sessionIdContext": {"description": "Opaque identifier used by servers to ensure session state is not\nshared between applications. Unused by clients.", "type": "string"}, "sessionTimeout": {"description": "The number of seconds after which a TLS session created by the\nserver will no longer be resumable. See Session Resumption for more\ninformation. Default: 300.", "type": "number"}, "sigalgs": {"description": "Colon-separated list of supported signature algorithms. The list\ncan contain digest algorithms (SHA256, MD5 etc.), public key\nalgorithms (RSA-PSS, ECDSA etc.), combination of both (e.g\n'RSA+SHA384') or TLS v1.3 scheme names (e.g. rsa_pss_pss_sha512).", "type": "string"}, "ticketKeys": {"description": "48-bytes of cryptographically strong pseudo-random data.", "items": {"type": "number"}, "type": "array"}}, "type": "object"}, "UiConfig": {"additionalProperties": false, "properties": {"exclude": {"items": {"type": "string"}, "type": "array"}, "include": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UpgradeOptions": {"additionalProperties": false, "properties": {"activateNewPackages": {"type": "boolean"}, "fullReloadOfSetupLayer": {"description": "Temp hack for upgrade on showcase (https://jira.sage.com/browse/XT-23045)\nShould all the CSV files from the SETUP layer be reloaded at the end of the upgrade ?\nThis will bypass the lookup from the git repo and reload all the **setup** CSV files", "type": "boolean"}, "upgradeOnly": {"description": "associated with the CLI upgrade option --prod this option makes it possible to skip\nthe playing of recorded SQL files.", "type": "boolean"}}, "type": "object"}, "WorkerConfig": {"additionalProperties": false, "properties": {"workersPerRequestSource": {"type": "number"}}, "type": "object"}, "WorkflowOptions": {"additionalProperties": false, "properties": {"captureFetchSize": {"description": "Max number of processes that we try to resume at once", "type": "number"}, "capturePollingMaxSeconds": {"description": "Maximum wait time when when capturing processes", "type": "number"}, "capturePollingMinSeconds": {"description": "Minimum wait time when capturing processes", "type": "number"}, "unresponsiveDelayInSeconds": {"description": "Delay after which we consider a process as killed if it has not updated its state", "type": "number"}}, "type": "object"}}, "properties": {"addOns": {"$ref": "#/definitions/AddOnConfig"}, "agGridLicenceKey": {"description": "Licence key for ag-grid", "type": "string"}, "app": {"description": "App name for this container in a multi-app infrastructure", "type": "string"}, "applicationId": {"description": "application id registered in api gateway", "type": "string"}, "applicationName": {"description": "application name registered in api gateway", "type": "string"}, "apps": {"$ref": "#/definitions/AppsConfig", "description": "apps, from apps.yml (next to xtrem-config.yml or under /infra)"}, "asyncContextTableName": {"type": "string"}, "authentication": {"$ref": "#/definitions/AuthenticationServiceConfig"}, "authenticationContainer": {"$ref": "#/definitions/AuthenticationContainerConfig", "description": "The configuration to start the authentication service container in prod-ui mode"}, "aws": {"$ref": "#/definitions/AwsConfig"}, "baseUrl": {"type": "string"}, "binaryStreamContentTypes": {"description": "Content types allowed for binary streams (regex list)", "items": {"type": "string"}, "type": "array"}, "cli": {"additionalProperties": false, "description": "CLI plugins", "properties": {"plugins": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "cluster": {"$ref": "#/definitions/ClusterConfig"}, "clusterId": {"type": "string"}, "copilot": {"$ref": "#/definitions/CopilotConfig", "description": "Configuration for copilot chat and insights"}, "debugMetrics": {"$ref": "#/definitions/DebugMetricsOptions", "description": "Configuration of debug metrics"}, "deploymentMode": {"enum": ["development", "production"], "type": "string"}, "disableUiTemplateCache": {"description": "Disables caching the index.html template, it is practical when working on changes for xtrem-standalone in watch mode", "type": "boolean"}, "documentationServiceUrl": {"description": "URL of the documentation service where user help pages are located", "type": "string"}, "email": {"type": "string"}, "endpoint": {"type": "string"}, "env": {"additionalProperties": false, "properties": {"isCI": {"type": "boolean"}}, "type": "object"}, "errorMonitoringInterval": {"description": "interval of error monitoring in seconds (default to 3600)", "type": "number"}, "errorMonitoringThreshold": {"description": "number of errors during the monitoring interval that will cause a process exit (default to 10)", "type": "number"}, "exportCsv": {"$ref": "#/definitions/ExportCsvOptions"}, "extends": {"description": "Path of config file name that we are extending", "type": "string"}, "extensionPath": {"type": "string"}, "graphql": {"$ref": "#/definitions/GraphQlConfig"}, "ignoreVendorProtection": {"description": "Ignore vendor protection rule on data update", "type": "boolean"}, "importCsv": {"$ref": "#/definitions/ImportCsvOptions"}, "interop": {"$ref": "#/definitions/InteropConfig"}, "login": {"description": "Login is the EM login", "type": "string"}, "logs": {"$ref": "#/definitions/LogsConfig"}, "newRelic": {"$ref": "#/definitions/NewRelicConfig", "description": "Configuration properties used for client error reporting and benchmarking"}, "noUi": {"type": "boolean"}, "operatorUserHashSecret": {"type": "string"}, "originFolder": {"description": "The folder where the config was loaded from", "type": "string"}, "packages": {"$ref": "#/definitions/Dict<any>", "additionalProperties": true}, "pendo": {"$ref": "#/definitions/PendoOptions"}, "prodUi": {"description": "Deploy the production UI application instead of the developer consumer mock", "type": "boolean"}, "productName": {"description": "Visible product name, used in application header and metadata fields.", "type": "string"}, "publicAppUrl": {"description": "Public url of the app on this container", "type": "string"}, "reportOptions": {"$ref": "#/definitions/ReportOptions"}, "s3": {"$ref": "#/definitions/Dict<S3Config>", "additionalProperties": true}, "s3Storage": {"$ref": "#/definitions/S3StorageConfig"}, "scope": {"description": "scope of api call", "type": "string"}, "security": {"$ref": "#/definitions/SecurityConfig"}, "semVerCompatibilityLevel": {"description": "The level at which we will be validating the package version differences", "enum": ["major", "minor", "patch"], "type": "string"}, "server": {"$ref": "#/definitions/ServerConfig"}, "serviceOptions": {"$ref": "#/definitions/ServiceOptions", "description": "The level at which service options are displayed and enabled in the product\nIf this attribute is not present it is set to workInProgress in dev mode,\nand to released in production mode."}, "settings": {"$ref": "#/definitions/Dict<any>", "additionalProperties": true}, "storage": {"$ref": "#/definitions/StorageConfig"}, "system": {"$ref": "#/definitions/SystemConfig"}, "telemetrySalt": {"description": "Salt that is used to create anonym unique user and tenant IDs", "type": "string"}, "tenantId": {"type": "string"}, "textServerUrl": {"type": "string"}, "textStreamContentTypes": {"description": "Content types allowed for text streams (regex list)", "items": {"type": "string"}, "type": "array"}, "textStreamLazyLoadLength": {"description": "text stream length that will be used to check if a text stream is lazy loaded", "type": "number"}, "ui": {"$ref": "#/definitions/UiConfig"}, "uiBroadcastTimeout": {"description": "Configuration for message broadcasting", "type": "number"}, "uiConfigPath": {"type": "string"}, "upgrade": {"$ref": "#/definitions/UpgradeOptions", "description": "Options for upgrade."}, "user": {"type": "string"}, "webSocket": {"$ref": "#/definitions/Dict<WebSocketConfig>", "additionalProperties": true}, "workflow": {"$ref": "#/definitions/WorkflowOptions"}, "xtremDeploymentCoreTableName": {"type": "string"}}, "type": "object"}