{"name": "@sage/xtrem~scripts", "description": "Umbrella project for Xtrem scripts", "version": "54.0.58", "license": "UNLICENSED", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.714.0", "@aws-sdk/client-sso-oidc": "^3.714.0", "@aws-sdk/client-sts": "^3.714.0", "@aws-sdk/credential-provider-node": "^3.714.0", "@aws-sdk/credential-providers": "^3.714.0", "@aws-sdk/s3-request-presigner": "^3.714.0", "depcheck": "^1.3.1", "fuzzy": "^0.1.3", "istanbul-reports": "^3.1.5", "lodash": "^4.17.21", "open": "^10.1.0", "prettier": "^3.3.3", "ts-node": "^10.3.0", "typescript": "~5.8.0", "js-yaml": "^4.1.0"}, "scripts": {"artilley:scan-reports": "ts-node --transpile-only artilley/scan-reports.ts", "documentation:documentation": "ts-node --transpile-only documentation/documentation.ts", "esmconvert": "ts-node --transpile-only esm-converter", "json:schema:doc": "node json-schema-doc.mjs", "sql:explain-sql-log": "ts-node --transpile-only sql/explain-sql-log.ts", "sumologic:transform-logs": "ts-node --transpile-only sumologic/transform-logs.ts", "release:packages": "ts-node --transpile-only release/release-packages.ts", "release:create-empty-sql-file": "ts-node --transpile-only release/create-empty-sql-file.ts", "release:version": "ts-node --transpile-only release/version.ts"}, "devDependencies": {"@types/istanbul-reports": "^3.0.1", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2"}}