#!/usr/bin/env bash

# Execute cucumber tests

# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)

# Env:

# SMOKE_TEST_TYPE: ${{ parameters.smokeTestType }}
# CLUSTER_ID: ${{ parameters.clusterId }}
# CLUSTER_NAME: ${{ parameters.clusterName }}
# SKIP_STAGE_TENANT_PROVISIONNING: ${{ parameters.parameters.SKIP_STAGE_TENANT_PROVISIONNING }}
# AUTHENTICATION_TYPE: ${{ parameters.authenticationType }}
# TARGET_URL: ${{parameters.TARGET_URL}}
# SCOPE: ${{ parameters.scope }}
# ENDPOINT_NAME_1: ${{ parameters.parameters.endPointName1 }}
# TENANT: ${{ parameters.tenant }}
# TENANT_NAME: ${{ parameters.tenantName }}
# XTREM_TEST_MAX_INSTANCES: ${{ parameters.XTREM_TEST_MAX_INSTANCES }}
# CUCUMBER_TAGS: ${{ parameters.CUCUMBER_TAGS }}
# testUser:  $(testUser)
# timeout: '$(timeout)'
# timeoutLocks: '$(timeoutLocks)'
# timeoutCucumber: '$(timeoutCucumber)'
# timeoutWaitFor: '$(timeoutWaitFor)'
# timeoutWaitForLoading: '$(timeoutWaitForLoading)'
# JAVA_HOME: $(JAVA_HOME_11_X64)
# PATH: $(JAVA_HOME_11_X64)/bin;$(PATH)
# LOGIN_USER_NAME: $(loginUserName)
# LOGIN_PASSWORD: $(loginPassword)
# SAGE_INTACTT_PASSWORD: $(sageIntacctPassword)
# SAGE_FRP_1000_PASSWORD: $(sageFrp1000Password)
# downloadFolder: $(downloadFolder)

# shellcheck disable=SC2164
# shellcheck disable=SC2116
# shellcheck disable=SC2086
# shellcheck disable=SC2181

mkdir "${1}/cucumber-json"
mkdir "${1}/cucumber-results"
mkdir "${1}/cucumber-junit/"
mkdir "${1}/cucumber-accessibility"
mkdir "${1}/cucumber-progress-results"
mkdir "${1}/allure-results"
cd "${2}/${SCOPE}/"

export downloadFolder="${downloadFolder}"
export XTREM_TEST_MAX_INSTANCES=${XTREM_TEST_MAX_INSTANCES}
export authenticationType="${AUTHENTICATION_TYPE}"
export tenantName="${TENANT_NAME}"
export SCOPE="${SCOPE}"
export testUser="${testUser}"

export loginUserName=${LOGIN_USER_NAME}
export loginPassword=${LOGIN_PASSWORD}
export loginUserName2=${LOGIN_USER_NAME2}
export loginPassword2=${LOGIN_PASSWORD2}

if [[  ${SCOPE} != "x3-services" &&  ${SCOPE} != "wh-services" ]]; then
    export sageIntacctPassword=${SAGE_INTACTT_PASSWORD}
    export sageFrp1000Password=${SAGE_FRP_1000_PASSWORD}
fi

if [ "${XTREM_TEST_MAX_INSTANCES}" == "none" ]; then
    echo "Maximum instances parameter is not set, defaulting to 1"
    export XTREM_TEST_MAX_INSTANCES=1
else
    echo "XTREM_TEST_MAX_INSTANCES=${XTREM_TEST_MAX_INSTANCES}"
fi

if [ "${CUCUMBER_TAGS_INPUT}" == "none" ]; then
    echo "No cucumber tag applied"
else
    echo "Cucumber tags exported:"
    echo "${CUCUMBER_TAGS_INPUT}"
    export CUCUMBER_TAGS="${CUCUMBER_TAGS_INPUT}"
fi

if [[ ${SCOPE} == "x3-services" || ${SCOPE} == "wh-services" ]]; then

    export TARGET_URL="${TARGET_URL}"
    export endPointName1="${ENDPOINT_NAME_1}"
    export stopTestOnError=true

    echo "Execute functional tests for ${SCOPE}"
    pnpm run test:functional:report

else
    if [[ ${AUTHENTICATION_TYPE} = "unsecure" ]]; then

        case "$CLUSTER_NAME" in
            deveu/ci-v2 | deveu/cls-release)
                export TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                echo URL defaulted to TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                ;;
            qana/cls-release | qana/curr-prd)
                export TARGET_URL="https://login.na.qa-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                echo URL defaulted to TARGET_URL="https://login.na.qa-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                ;;
            ppeu1/cls-release | ppeu1/pp-prd)
                export TARGET_URL="https://login.eu1.pp-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                echo URL defaulted to TARGET_URL="https://login.eu1.pp-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
                ;;
            *)
                echo "cluster not allowed for unsecure login"
            ;;
        esac
        export smokeTestsAuthSecret="${SMOKE_TEST_AUTH_SECRET}"
        export smokeTestAuthEnvName="${SMOKE_TEST_AUTH_ENV_NAME}"
        export tenantId="${TENANT}"

    else
        export TARGET_URL="${TARGET_URL}"
        export tenantApp="${TENANT_APP}"
        export tenantAppName="${TENANT_APP_NAME}"

    fi
    if [ "${SMOKE_TEST_TYPE}" == "static" ] || [ "${SMOKE_TEST_TYPE}" == "all" ]; then
        if [ "${SMOKE_TEST_TYPE}" == "static" ]; then
            npmCommand="test:smoke:static:ci"
        fi
        if [ "${SMOKE_TEST_TYPE}" == "all" ]; then
            npmCommand="test:smoke:ci"
        fi
        scope=$(echo "${2}/${SCOPE}/*/*")
        failed=0
        for dir in $scope; do
            echo "Directory: $dir"
            if [[ -d "$dir" && "$dir" != *"functional-tests"* && "$dir" != *"smoke-tests-minimum"* && -n $(find "$dir" -type f -name "*.feature") ]]; then
                if [ -f $dir/package.json ]; then
                    cd $dir
                    echo "Execute smoke test ${SMOKE_TEST_TYPE} for $dir"
                    pnpm run $npmCommand --if-present
                    if [ $? -ne 0 ]; then failed=$((failed + 1)); fi
                else
                    echo "$dir is empty"
                fi
            else
                echo "$dir is not a directory or contains 'functional-tests' or 'smoke-test-minimum' or doesn't contain any feature file, skipping..."
            fi
        done
        if [ $failed -ne 0 ]; then
            echo "Tests FAILED in $failed packages !!!"
            exit 1
        fi
    else
        if [ "${SMOKE_TEST_TYPE}" == "minimum" ]; then
            npmCommand="test:smoke:minimum:ci"
            scope=$(echo "${2}/${SCOPE}/*/*")
            failed=0
            for dir in $scope; do
                echo "Directory: $dir"
                if [[ -d "$dir" && "$dir" == *"smoke-tests-minimum"* && -n $(find "$dir" -type f -name "*.feature") ]]; then
                    if [ -f $dir/package.json ]; then
                        cd $dir
                        echo "Execute smoke test ${SMOKE_TEST_TYPE} for $dir"
                        pnpm run $npmCommand --if-present
                        if [ $? -ne 0 ]; then failed=$((failed + 1)); fi
                    else
                        echo "$dir is empty"
                    fi
                else
                    echo "$dir is not a directory or does not contain 'smoke-tests-minimum' or doesn't contain any feature file, skipping..."
                fi
            done
            if [ $failed -ne 0 ]; then
                echo "Tests FAILED in $failed packages !!!"
                exit 1
            fi
        else
            echo "Execute functional tests for ${SCOPE}"
            pnpm run test:functional:ci
        fi
    fi
fi
