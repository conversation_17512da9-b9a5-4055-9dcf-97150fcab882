{"name": "@sage/xtrem~services", "description": "Umbrella project for Xtrem development", "version": "54.0.58", "license": "UNLICENSED", "scripts": {"build": "cd .. && pnpm build:services", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:modified": "cd .. && pnpm build:modified", "extract:demo:data": "cd main/xtrem-services-main && pnpm extract:demo:data", "extract:layer:data": "cd main/xtrem-services-main && pnpm extract:layer:data", "extract:qa:data": "cd main/xtrem-services-main && pnpm extract:qa:data", "lint": "cd .. && pnpm lint:services", "load:demo:data": "cd main/xtrem-services-main && pnpm load:demo:data", "load:layer:data": "cd main/xtrem-services-main && pnpm load:layer:data", "load:qa:data": "cd main/xtrem-services-main && pnpm load:qa:data", "load:setup:data": "cd main/xtrem-services-main && pnpm load:setup:data", "load:test:data": "cd main/xtrem-services-main && pnpm load:test:data", "postgres:clean": "docker rm xtrem_postgres || exit 0", "postgres:reset": "pnpm postgres:stop && pnpm postgres:clean && pnpm postgres:setup", "postgres:setup": "docker run --shm-size=1g -p 5432:5432 -e POSTGRES_PASSWORD=secret -d --restart unless-stopped --name xtrem_postgres postgres:$(cat ../.pgdbrc)-alpine -c max_locks_per_transaction=256", "postgres:stop": "docker stop xtrem_postgres || exit 0", "schema:reset": "cd main/xtrem-services-main && pnpm schema:reset", "schema:upgrade:test": "cd main/xtrem-services-main && pnpm schema:upgrade:test", "sqs:clean": "cd .. && pnpm sqs:clean", "sqs:reset": "cd .. && pnpm sqs:reset", "sqs:setup": "cd .. && pnpm sqs:setup", "sqs:stop": "cd .. && pnpm sqs:stop", "start": "cd main/xtrem-services-main && npm start", "test": "cd .. && pnpm test:services", "test:functional": "../scripts/lerna-cache/test-functional.sh", "test:functional:ci": "../scripts/lerna-cache/test-functional.sh :ci", "test:smoke:ci": "cd .. && pnpm test:smoke:ci", "test:smokecd": "../scripts/lerna-cache/test-integration-smoke-cd.sh", "test:integration": "xtrem test --integration --browser"}}