{"name": "@sage/k6", "version": "2.0.58", "description": "K6 package for performance.", "private": true, "main": "index.ts", "author": "", "license": "AGPL-3.0", "bugs": {"url": "https://github.com/loadimpact/k6/issues"}, "files": ["build"], "homepage": "https://github.com/loadimpact/k6#readme", "packageManager": "pnpm@9.15.9", "devDependencies": {"@sage/xtrem-manufacturing-api": "workspace:*", "@sage/xtrem-purchasing-api": "workspace:*", "@sage/xtrem-distribution-api": "workspace:*", "@sage/xtrem-communication-api": "workspace:*", "@sage/xtrem-sales-api": "workspace:*", "@sage/xtrem-finance-data-api": "workspace:*", "@sage/xtrem-technical-data-api": "workspace:*", "@sage/xtrem-stock-data-api": "workspace:*", "@sage/xtrem-stock-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-master-data-api": "workspace:*", "@sage/xtrem-mrp-data-api": "workspace:*", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-ui": "workspace:*", "json-to-graphql-query": "^2.2.5", "@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.27.0", "@types/k6": "^1.0.2", "@types/webpack": "^5.28.5", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "typescript": "~5.8.0", "webpack": "^5.99.0", "webpack-cli": "^6.0.1", "webpack-glob-entries": "^1.0.1", "handlebars": "^4.7.8", "lodash": "^4.17.21", "@types/lodash": "^4.14.198"}, "dependencies": {"ts-loader": "^9.5.2"}, "scripts": {"build": "tsc -b -v .", "bundle": "webpack", "run:work-order": "pnpm bundle && k6 run dist/work-order.js", "run:item-crud": "pnpm bundle && k6 run dist/item-crud.js", "run:item": "pnpm bundle && k6 run dist/item.js", "run:flow": "pnpm bundle && k6 run dist/flow.js", "run:perf": "pnpm bundle && k6 run dist/perf.js", "run:purchase-order": "pnpm bundle && k6 run dist/create-purchase-order.js", "run:purchase-order-flow": "pnpm bundle && k6 run dist/purchase-order-flow.js", "run:sales-order": "pnpm bundle && k6 run dist/sales-order.js", "run:stockReceipt": "pnpm bundle && k6 run dist/stock-receipt.js", "run:bom": "pnpm bundle && k6 run dist/bill-of-material.js"}}