{"name": "@sage/xtrem~authorization-test", "description": "Xtrem authorization test", "version": "54.0.58", "xtrem": {}, "author": "sage", "license": "UNLICENSED", "keywords": ["xtreem", "authorization", "functional test"], "main": "build/index.js", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-declarations": "workspace:*", "@sage/xtrem-finance": "workspace:*", "@sage/xtrem-mailer": "workspace:*", "@sage/xtrem-manufacturing": "workspace:*", "@sage/xtrem-master-data": "workspace:*", "@sage/xtrem-purchasing": "workspace:*", "@sage/xtrem-reporting": "workspace:*", "@sage/xtrem-sales": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-structure": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-technical-data": "workspace:*", "@sage/xtrem-ui": "workspace:*"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "typescript": "~5.8.0"}, "scripts": {"build": "tsc", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "xtrem lint", "load:qa:data": "xtrem layers --load setup,qa", "start": "xtrem start", "test:functional": "xtrem test --integration", "test:functional:ci": "xtrem test --integration --ci", "xtrem": "xtrem", "view-report": "node ../../../scripts/allure/view-report.js"}}