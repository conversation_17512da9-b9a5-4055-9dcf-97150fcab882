{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "description", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"description (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "30", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "40", "path": "activeRange", "locale": "", "dataType": "date<PERSON><PERSON><PERSON>", "isCustom": false, "description": "active range"}, {"_id": "50", "path": "!site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "60", "path": "resourceImage", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "resource image"}, {"_id": "70", "path": "*weeklyShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "weekly shift (#id)"}, {"_id": "80", "path": "resourceGroup", "locale": "", "dataType": "reference", "isCustom": false, "description": "resource group (#id|site)"}, {"_id": "90", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "100", "path": "efficiency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "efficiency"}, {"_id": "110", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "120", "path": "#capabilities", "locale": "", "dataType": "collection", "isCustom": false, "description": "capabilities"}, {"_id": "130", "path": "*id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "140", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "150", "path": "dateStartValid", "locale": "", "dataType": "date", "isCustom": false, "description": "date start valid (YYYY-MM-DD)"}, {"_id": "160", "path": "dateEndValid", "locale": "", "dataType": "date", "isCustom": false, "description": "date end valid (YYYY-MM-DD)"}, {"_id": "170", "path": "*capabilityLevel", "locale": "", "dataType": "reference", "isCustom": false, "description": "capability level (#id)"}, {"_id": "180", "path": "machine", "locale": "", "dataType": "reference", "isCustom": false, "description": "machine (#id|site)"}, {"_id": "190", "path": "tool", "locale": "", "dataType": "reference", "isCustom": false, "description": "tool (#id|site)"}, {"_id": "200", "path": "service", "locale": "", "dataType": "reference", "isCustom": false, "description": "service (#id)"}, {"_id": "210", "path": "#resourceCostCategories", "locale": "", "dataType": "collection", "isCustom": false, "description": "resource cost categories"}, {"_id": "220", "path": "*costCategory", "locale": "", "dataType": "reference", "isCustom": false, "description": "cost category (#id)"}, {"_id": "230", "path": "setupCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "setup cost"}, {"_id": "240", "path": "runCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "run cost"}, {"_id": "250", "path": "*costUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "cost unit (#id)"}]}