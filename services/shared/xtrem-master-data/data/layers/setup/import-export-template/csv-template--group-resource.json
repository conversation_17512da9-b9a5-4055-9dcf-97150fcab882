{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "description", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"description (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "30", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "40", "path": "activeRange", "locale": "", "dataType": "date<PERSON><PERSON><PERSON>", "isCustom": false, "description": "active range"}, {"_id": "50", "path": "!site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "60", "path": "resourceImage", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "resource image"}, {"_id": "70", "path": "*weeklyShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "weekly shift (#id)"}, {"_id": "80", "path": "efficiency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "efficiency"}, {"_id": "90", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "100", "path": "type", "locale": "", "dataType": "enum(labor,machine,subcontract,tool)", "isCustom": false, "description": "type"}, {"_id": "110", "path": "minCapabilityLevel", "locale": "", "dataType": "reference", "isCustom": false, "description": "min capability level (#id)"}, {"_id": "120", "path": "#resourceCostCategories", "locale": "", "dataType": "collection", "isCustom": false, "description": "resource cost categories"}, {"_id": "130", "path": "*costCategory", "locale": "", "dataType": "reference", "isCustom": false, "description": "cost category (#id)"}, {"_id": "140", "path": "setupCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "setup cost"}, {"_id": "150", "path": "runCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "run cost"}, {"_id": "160", "path": "*costUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "cost unit (#id)"}, {"_id": "170", "path": "#replacements", "locale": "", "dataType": "collection", "isCustom": false, "description": "replacements"}, {"_id": "180", "path": "*replacement", "locale": "", "dataType": "reference", "isCustom": false, "description": "replacement (#id|site)"}]}