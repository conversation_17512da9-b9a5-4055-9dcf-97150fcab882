import type {
    BusinessEntityAddress,
    DeliveryDetail,
    DeliveryMode,
    GraphApi,
    Incoterm,
} from '@sage/xtrem-master-data-api';
import type { Country } from '@sage/xtrem-structure-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';
import { validPhoneNumber } from '../client-functions/address-validation';
import { deliveryAddressValues, setDefaultWorkingDays } from '../client-functions/business-entity-customer';
import { invalidPostalCode, invalidZipCode } from '../client-functions/business-entity-localize';
import { getBindedValues } from '../client-functions/common';
import { validPostcodeEntry } from '../shared-functions/address-functions';

@ui.decorators.page<BusinessEntityAddressPanel, BusinessEntityAddress>({
    title: 'Business entity address panel',
    node: '@sage/xtrem-master-data/BusinessEntityAddress',
    mode: 'tabs',
    module: 'master-data',
    businessActions() {
        return [this.cancel, this.confirm];
    },
    onLoad() {
        this.$.page.title = this.$.queryParameters.title.toString();
        this.$.loader.isHidden = true;
        this.shippingSection.isHidden = !this.$.queryParameters.isCustomer;
        const deliveryDetail = this.deliveryDetail.value;
        const fields = this._pageMetadata.uiComponentProperties;
        // delivery detail management :
        if (deliveryDetail) {
            this.isShippingAddress.value = true;
            Object.entries(fields).forEach(([key, field]) => {
                // is it possible to type field ?
                const { bind } = field as any;
                if (bind) {
                    const firstLevel = Object.keys(bind).at(0);
                    if (firstLevel === 'deliveryDetail' && typeof bind.deliveryDetail !== 'boolean') {
                        const secondLevel = Object.keys(bind.deliveryDetail).at(0);
                        _.set(this, `${key}.value`, _.get(deliveryDetail, `${secondLevel}`));
                    }
                }
            });
        }
    },
})
export class BusinessEntityAddressPanel extends ui.Page<GraphApi, BusinessEntityAddress> {
    @ui.decorators.pageAction<BusinessEntityAddressPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.setPageClean();
            this.$.finish({ address: null });
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.pageAction<BusinessEntityAddressPanel>({
        title: 'Ok',
        buttonType: 'primary',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
                return;
            }
            const address = getBindedValues(this);
            if (!this.isShippingAddress.value && address.deliveryDetail) {
                delete address.deliveryDetail;
            }
            if (address.deliveryDetail) {
                address.deliveryDetail = deliveryAddressValues({
                    ...address.deliveryDetail,
                    workDaysSelection: this.workDaysSelection.value,
                    isActive: this.isActiveShippingAddress.value ? this.isActiveShippingAddress.value : false,
                    isPrimary: this.isPrimaryShippingAddress.value ? this.isPrimaryShippingAddress.value : false,
                    leadTime: this.deliveryLeadTime.value,
                });
            }
            // creation case
            this.$.finish({ address });
        },
    })
    confirm: ui.PageAction;

    getSerializedValues() {
        const { values: address } = this.$;
        return address;
    }

    @ui.decorators.textField<BusinessEntityAddressPanel>({}) _id: ui.fields.Text;

    @ui.decorators.section<BusinessEntityAddressPanel>({ title: 'Address' }) mainSection: ui.containers.Section;

    @ui.decorators.block<BusinessEntityAddressPanel>({
        parent() {
            return this.mainSection;
        },
        title: 'Address information',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<BusinessEntityAddressPanel>({ title: 'Information', isHidden: true })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingSection;
        },
        title: 'Shipping information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.block<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingSection;
        },
        title: 'Shipping information',
        isTitleHidden: true,
        isHidden() {
            return !this.deliveryDetail.value;
        },
    })
    shippingBlock: ui.containers.Block;

    @ui.decorators.switchField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
    })
    isActive: ui.fields.Switch;

    @ui.decorators.checkboxField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Primary address',
    })
    isPrimary: ui.fields.Checkbox;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
        maxLength: 80,
        width: 'large',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Address line 1',
        maxLength: 150,
        width: 'large',
    })
    addressLine1: ui.fields.Text;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Address line 2',
        maxLength: 150,
        width: 'large',
    })
    addressLine2: ui.fields.Text;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'City',
        maxLength: 100,
        width: 'large',
    })
    city: ui.fields.Text;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title() {
            return this.loadRegionTitle();
        },
        maxLength: 35,
    })
    region: ui.fields.Text;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title() {
            return this.loadZipTitle();
        },
        maxLength: 10,
        validation(val) {
            if (val && this.country.value?.id && !validPostcodeEntry(val)) {
                return this.country.value.zipLabel === 'zipCode' ? invalidZipCode : invalidPostalCode;
            }
            return undefined;
        },
    })
    postcode: ui.fields.Text;

    @ui.decorators.referenceField<BusinessEntityAddressPanel, Country>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select country',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
            ui.nestedFields.technical({ bind: 'regionLabel' }),
            ui.nestedFields.technical({ bind: 'zipLabel' }),
        ],
    })
    country: ui.fields.Reference<Country>;

    @ui.decorators.textField<BusinessEntityAddressPanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Phone number',
        maxLength: 20,
        validation: phoneNumber => validPhoneNumber(phoneNumber),
    })
    locationPhoneNumber: ui.fields.Text;

    @ui.decorators.switchField<BusinessEntityAddressPanel>({
        parent() {
            return this.informationBlock;
        },
        title: 'Ship-to address',
        bind: { deliveryDetail: true },
        onChange() {
            this.shippingBlock.isHidden = !this.isShippingAddress.value;
            setDefaultWorkingDays(this);
        },
    })
    isShippingAddress: ui.fields.Switch;

    @ui.decorators.referenceField<BusinessEntityAddressPanel, DeliveryDetail>({
        title: 'Delivery detail',
        bind: 'deliveryDetail',
        node: '@sage/xtrem-master-data/DeliveryDetail',
        valueField: '_id',
        columns: [
            ui.nestedFields.technical<BusinessEntityAddressPanel, DeliveryDetail, Incoterm>({
                bind: 'incoterm',
                node: '@sage/xtrem-master-data/Incoterm',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isFridayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isMondayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isPrimary' }),
            ui.nestedFields.technical({ bind: 'isSaturdayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isSundayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isThursdayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isTuesdayWorkDay' }),
            ui.nestedFields.technical({ bind: 'isWednesdayWorkDay' }),
            ui.nestedFields.technical({ bind: 'leadTime' }),
            ui.nestedFields.technical<BusinessEntityAddressPanel, DeliveryDetail, DeliveryMode>({
                bind: 'mode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<BusinessEntityAddressPanel, DeliveryDetail, Site>({
                bind: 'shipmentSite',
                node: '@sage/xtrem-system/Site',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: 'workDaysSelection' }),
        ],
    })
    deliveryDetail: ui.fields.Reference<DeliveryDetail>;

    @ui.decorators.switchField<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Active',
        bind: { deliveryDetail: { isActive: true } },
    })
    isActiveShippingAddress: ui.fields.Switch;

    @ui.decorators.checkboxField<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Primary ship-to address',
        bind: { deliveryDetail: { isPrimary: true } },
    })
    isPrimaryShippingAddress: ui.fields.Checkbox;

    @ui.decorators.referenceField<BusinessEntityAddressPanel, Site>({
        parent() {
            return this.shippingBlock;
        },
        bind: { deliveryDetail: { shipmentSite: true } },
        title: 'Shipping site',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select site',
        filter: { isInventory: { _eq: true } },
    })
    shipmentSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<BusinessEntityAddressPanel, DeliveryMode>({
        parent() {
            return this.shippingBlock;
        },
        bind: { deliveryDetail: { mode: true } },
        node: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        isMandatory() {
            return !!this.isShippingAddress.value;
        },
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select delivery mode',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: '_id' })],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.numericField<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingBlock;
        },
        bind: { deliveryDetail: { leadTime: true } },
        title: 'Delivery lead time',
        postfix: 'day(s)',
    })
    deliveryLeadTime: ui.fields.Numeric;

    @ui.decorators.referenceField<BusinessEntityAddressPanel, Incoterm>({
        parent() {
            return this.shippingBlock;
        },
        bind: { deliveryDetail: { incoterm: true } },
        node: '@sage/xtrem-master-data/Incoterm',
        title: 'Incoterms® rule',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select incoterm',
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.multiDropdownField<BusinessEntityAddressPanel>({
        parent() {
            return this.shippingBlock;
        },
        optionType: '@sage/xtrem-master-data/WeekDays',
        title: 'Working days',
        width: 'large',
        bind: { deliveryDetail: { workDaysSelection: true } },
    })
    workDaysSelection: ui.fields.MultiDropdown;

    loadRegionTitle() {
        return this.country.value?.regionLabel
            ? ui.localizeEnumMember('@sage/xtrem-structure/RegionLabel', this.country.value.regionLabel)
            : ui.localizeEnumMember('@sage/xtrem-structure/RegionLabel', 'stateCountyRegion');
    }

    loadZipTitle() {
        return this.country.value?.zipLabel
            ? ui.localizeEnumMember('@sage/xtrem-structure/ZipLabel', this.country.value.zipLabel)
            : ui.localizeEnumMember('@sage/xtrem-structure/ZipLabel', 'postalCode');
    }
}
