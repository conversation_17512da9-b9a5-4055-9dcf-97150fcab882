-- Rollback script of script-XT94109 for OCEA
-- Only for OCEA
INSERT INTO %%SCHEMA_NAME%%.unbilled_account_receivable_result_line
("_tenant_id", "_id", "_sort_value", input_set, customer, currency, financial_site, sales_unit, net_price, item, site, quantity, account_item, invoiced_quantity, credited_quantity, returned_quantity, invoice_issuable_quantity, invoice_issuable_amount, shipment_number, shipment_internal_id, document_date, ship_to_customer, "_create_user", "_update_user", "_create_stamp", "_update_stamp", "_update_tick", "_source_id", "_custom_data")
VALUES(%%TENANT_ID%%, 1235, 260, 202, 82668, 245, 308, 3168, 20.21, 202596, 308, 1, 5933, 0, 0, 0, 1, 20.21, 'SH240156', 21853, '2025-01-20 16:22:25.196', 82668, 1, 1, '2025-05-22 16:22:25.196', '2025-06-02 13:02:53.536', 11, '', '{}'::jsonb);
