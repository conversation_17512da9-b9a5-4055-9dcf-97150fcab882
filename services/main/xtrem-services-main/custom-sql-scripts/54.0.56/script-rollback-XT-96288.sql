-- Description: XT96288 - Rollback script for material tracking and stock journal entries

INSERT INTO %%SCHEMA_NAME%%.material_tracking
("_tenant_id", "_id", "number", work_order, site, entry_date, effective_date, "_create_user", "_update_user", "_create_stamp", "_update_stamp", "_update_tick", "_source_id", "_custom_data")
VALUES(%%TENANT_ID%%, 15496, 'WT25003546', 14183, 185, '2025-05-16', '2025-05-16', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:44.150', 9, '', '{}'::jsonb);

INSERT INTO %%SCHEMA_NAME%%.base_document_line
("_tenant_id", "_id", "_constructor", "_create_user", "_update_user", "_create_stamp", "_update_stamp", "_update_tick", "_source_id", "_custom_data")
VALUES(%%TENANT_ID%%, 1000315758, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:49.735', 3, '', NULL),
(%%TENANT_ID%%, 1000315759, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:50.580', 3, '', NULL),
(%%TENANT_ID%%, 1000315752, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:45.379', 3, '', NULL),
(%%TENANT_ID%%, 1000315753, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:46.107', 3, '', NULL),
(%%TENANT_ID%%, 1000315754, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:47.039', 3, '', NULL),
(%%TENANT_ID%%, 1000315755, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:47.661', 3, '', NULL),
(%%TENANT_ID%%, 1000315756, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:48.446', 3, '', NULL),
(%%TENANT_ID%%, 1000315757, 'MaterialTrackingLine', 1, 1, '2025-05-16 17:23:43.366', '2025-05-16 17:23:49.075', 3, '', NULL);

INSERT INTO %%SCHEMA_NAME%%.material_tracking_line
("_tenant_id","_id","_sort_value","document",stock_transaction_status,work_order_line,material_type,quantity_in_stock_unit,is_active_quantity,completed,stored_dimensions,stored_attributes,line,analytical_data,"_custom_data")
VALUES(%%TENANT_ID%%,1000315752,10,15496,'inProgress',1000313371,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,20,93,'{}'),
(%%TENANT_ID%%,1000315753,20,15496,'inProgress',1000313372,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,30,93,'{}'),
(%%TENANT_ID%%,1000315754,30,15496,'inProgress',1000313373,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,40,93,'{}'),
(%%TENANT_ID%%,1000315755,40,15496,'inProgress',1000313374,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,50,93,'{}'),
(%%TENANT_ID%%,1000315756,50,15496,'inProgress',1000313375,'WO',53.2000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,60,93,'{}'),
(%%TENANT_ID%%,1000315757,60,15496,'inProgress',1000313376,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,70,93,'{}'),
(%%TENANT_ID%%,1000315758,70,15496,'inProgress',1000313377,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,80,93,'{}'),
(%%TENANT_ID%%,1000315759,80,15496,'inProgress',1000313378,'WO',38.0000000000,false,false,'{"dimensionType01": "HOUS", "dimensionType02": "MAN"}',NULL,90,93,'{}');

INSERT INTO %%SCHEMA_NAME%%.base_stock_detail
("_tenant_id", "_id", "_constructor", "_sort_value", document_line, site, item, stock_unit, quantity_in_stock_unit, "_create_user", "_update_user", "_create_stamp", "_update_stamp", "_update_tick", "_source_id", "_custom_data")
VALUES(%%TENANT_ID%%, 485183, 'StockIssueDetail', 48518300, 1000315752, 185, 86922, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:45.379', '2025-05-16 17:23:45.379', 2, '', NULL),
(%%TENANT_ID%%, 485184, 'StockIssueDetail', 48518400, 1000315753, 185, 86921, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:46.107', '2025-05-16 17:23:46.107', 2, '', NULL),
(%%TENANT_ID%%, 485186, 'StockIssueDetail', 48518600, 1000315755, 185, 86447, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:47.661', '2025-05-16 17:23:47.661', 2, '', NULL),
(%%TENANT_ID%%, 485187, 'StockIssueDetail', 48518700, 1000315756, 185, 86441, 1623, -53.2000000000, 1, 1, '2025-05-16 17:23:48.446', '2025-05-16 17:23:48.446', 2, '', NULL),
(%%TENANT_ID%%, 485185, 'StockIssueDetail', 48518500, 1000315754, 185, 130126, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:47.039', '2025-05-16 17:23:47.039', 2, '', NULL),
(%%TENANT_ID%%, 485188, 'StockIssueDetail', 48518800, 1000315757, 185, 86923, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:49.075', '2025-05-16 17:23:49.075', 2, '', NULL),
(%%TENANT_ID%%, 485189, 'StockIssueDetail', 48518900, 1000315758, 185, 86630, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:49.735', '2025-05-16 17:23:49.735', 2, '', NULL),
(%%TENANT_ID%%, 485190, 'StockIssueDetail', 48519000, 1000315759, 185, 87023, 1616, -38.0000000000, 1, 1, '2025-05-16 17:23:50.580', '2025-05-16 17:23:50.580', 2, '', NULL);

INSERT INTO %%SCHEMA_NAME%%.stock_journal
("_tenant_id", "_id", "sequence", site, item, stock_unit, quantity_in_stock_unit, effective_date, "owner", source_document_line, lot, status, "location", order_cost, valued_cost, is_adjusted, reason_code, stock_detail, non_absorbed_amount, movement_type, active_quantity_in_stock_unit, document_line, order_amount, movement_amount, "_create_user", "_update_user", "_create_stamp", "_update_stamp", "_update_tick", "_source_id", "_custom_data")
VALUES(%%TENANT_ID%%, 577239, 99996, 185, 86441, 1623, -53.2000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.0900000000, 0.0900000000, false, NULL, 485187, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -53.2000000000, 1000315756, -4.7900000000, -4.7900000000, 1, 1, '2025-05-16 17:23:48.446', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577238, 99996, 185, 86447, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.0280000000, 0.0280000000, false, NULL, 485186, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315755, -1.0600000000, -1.0600000000, 1, 1, '2025-05-16 17:23:47.661', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577241, 99996, 185, 86630, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.1020000000, 0.1020000000, false, NULL, 485189, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315758, -3.8800000000, -3.8800000000, 1, 1, '2025-05-16 17:23:49.735', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577236, 99999, 185, 86921, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.2699930000, 0.2699930000, false, NULL, 485184, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315753, -10.2600000000, -10.2600000000, 1, 1, '2025-05-16 17:23:46.107', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577235, 99999, 185, 86922, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.3025950000, 0.3025950000, false, NULL, 485183, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315752, -11.5000000000, -11.5000000000, 1, 1, '2025-05-16 17:23:45.379', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577240, 99997, 185, 86923, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.6853880000, 0.6853880000, false, NULL, 485188, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315757, -26.0400000000, -26.0400000000, 1, 1, '2025-05-16 17:23:49.075', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577242, 99997, 185, 87023, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.2000000000, 0.2000000000, false, NULL, 485190, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315759, -7.6000000000, -7.6000000000, 1, 1, '2025-05-16 17:23:50.580', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb),
(%%TENANT_ID%%, 577237, 99997, 185, 130126, 1616, -38.0000000000, '2025-05-16', 'HOUS', NULL, NULL, 97, 2062, 0.1845640000, 0.1845640000, false, NULL, 485185, 0.0000000000, 'issue'::%%SCHEMA_NAME%%."stock_movement_type_enum", -38.0000000000, 1000315754, -7.0100000000, -7.0100000000, 1, 1, '2025-05-16 17:23:47.039', '2025-06-11 14:42:19.268', 2, '', '{}'::jsonb);
