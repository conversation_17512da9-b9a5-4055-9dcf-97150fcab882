-- Script to update the completed flag to false for all purchase receipt lines belongig to purchase order PO250003 for OCEA
-- Only for OCEA
UPDATE %%SCHEMA_NAME%%.purchase_receipt_line
SET completed = false
WHERE _tenant_id = %%TENANT_ID%% AND _id IN ('1000896349', '1000896350') AND completed = true
returning _id;

UPDATE %%SCHEMA_NAME%%.base_document
SET display_status = 'partiallyReceived'
WHERE _tenant_id = %%TENANT_ID%% AND _id = 80360 AND number = 'PO250100'
returning _id;

UPDATE %%SCHEMA_NAME%%.purchase_order
SET receipt_status = 'partiallyReceived'
WHERE _tenant_id = %%TENANT_ID%% AND _id = 80360
returning _id;

UPDATE %%SCHEMA_NAME%%.purchase_order_line
SET line_receipt_status = 'partiallyReceived'
WHERE _tenant_id = %%TENANT_ID%% AND _id = 1000808694
returning _id;
