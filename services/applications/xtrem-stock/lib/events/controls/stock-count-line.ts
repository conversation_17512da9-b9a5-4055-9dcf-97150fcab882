import type { ValidationContext } from '@sage/xtrem-core';
import { LocalizedError, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as _ from 'lodash';
import * as xtremStock from '../..';

export async function checkExistingStockLine(cx: ValidationContext, stockCountLine: xtremStock.nodes.StockCountLine) {
    // Since we add a draft status first we always add stock records to the stock count line - need to rework this
    // check for manually added lines on stock count

    if (stockCountLine.$.status !== 'added') return;
    const stockCount = await stockCountLine.document;
    if (stockCount.isConfirmingStockCountCreation) return;

    const stockDetailLot = await (await stockCountLine.stockDetail)?.stockDetailLot;
    const lot = (await stockDetailLot?.lot) ?? null;
    const lotNumber = await stockDetailLot?.lotNumber;

    // If the line does not create a new lot and there is no link with a Stock record,
    // we need to check if the stock record already exists
    if (!lotNumber && !(await stockCountLine.stockRecord)) {
        const existingStockRecord = await xtremStockData.functions.stockLib.getStockRecord(stockCountLine.$.context, {
            item: await stockCountLine.item,
            site: await (await stockCountLine.document).stockSite,
            location: await stockCountLine.location,
            lot,
            status: await stockCountLine.stockStatus,
            stockUnit: await stockCountLine.stockUnit,
            owner: await stockCountLine.owner,
        });
        if (existingStockRecord) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/pages__stock_count_line__not_add_a_line_matching_with_a_stock_record',
                'You need to change the details on this stock count line because it duplicates existing stock information. Item: {{item}}, site: {{site}}, location: {{location}}, lot: {{lot}}, status: {{status}}, stock unit: {{stockUnit}}, owner: {{owner}}.',
                {
                    item: await (await stockCountLine.item).id,
                    site: await (await (await stockCountLine.document).stockSite).id,
                    location: (await (await stockCountLine.location)?.id) ?? 'null',
                    lot: lot ? await lot.id : 'null',
                    status: await (await stockCountLine.stockStatus)?.id,
                    stockUnit: await (await stockCountLine.stockUnit).id,
                    owner: await stockCountLine.owner,
                },
            );
            return;
        }
    }

    const lotCreateData = lotNumber ? { id: lotNumber, sublot: (await stockDetailLot?.sublot) || null } : undefined;

    try {
        await xtremStock.functions.stockCountLib.existingStockCountLine(
            stockCountLine.$.context,
            true,
            {
                item: (await stockCountLine.item)._id,
                stockStatus: (await stockCountLine.stockStatus)?._id ?? null,
                location: (await stockCountLine.location)?._id,
                stockUnit: (await stockCountLine.stockUnit)._id,
                owner: await stockCountLine.owner,
                lot: lot?._id,
                document: {
                    stockSite: (await stockCountLine.site)._id,
                },
            },
            lotCreateData,
        );
    } catch (error) {
        if (!(error instanceof LocalizedError)) throw error;

        cx.error.add(error.message);
    }
}

export async function controlStockDetailConsistency(
    cx: ValidationContext,
    stockCountLine: xtremStock.nodes.StockCountLine,
) {
    // if the line concerns an item that had no stock and the counted quantity is 0, we don't need to check the stock detail
    if (await stockCountLine.isNonStockItem) return;

    const stockDetail = await stockCountLine.stockDetail;
    if (stockDetail) {
        const stockDetailCharacteristics = {
            site: (await stockDetail.site)?._id,
            item: (await stockDetail.item)?._id,
            status: (await stockDetail.status)?._id,
            location: (await stockDetail.location)?._id,
            stockUnit: (await stockDetail.stockUnit)?._id,
            owner: await stockDetail.owner,
        };
        const stockCountLineCharacteristics = {
            site: (await stockCountLine.site)?._id,
            item: (await stockCountLine.item)?._id,
            status: (await stockCountLine.stockStatus)?._id,
            location: (await stockCountLine.location)?._id,
            stockUnit: (await stockCountLine.stockUnit)?._id,
            owner: await stockCountLine.owner,
        };

        if (!_.isEqual(stockDetailCharacteristics, stockCountLineCharacteristics)) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_count_line__stock_detail_inconsistent_with_line',
                'The stock detail needs to be the same as the stock count line.',
            );
        }
    }
}

export async function controlStockDetailLot(cx: ValidationContext, stockCountLine: xtremStock.nodes.StockCountLine) {
    const item = await stockCountLine.item;
    const stockDetailLot = await (await stockCountLine.stockDetail)?.stockDetailLot;

    if ((await stockCountLine.isNonStockItem) || (await stockCountLine.status) === 'excluded') return;

    if ((await item.lotManagement) !== 'notManaged') {
        if (
            !stockDetailLot ||
            ((await stockDetailLot.lot) === null &&
                (await stockDetailLot.lotNumber) === '' &&
                !(await item.lotSequenceNumber))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_count_line__lot_mandatory',
                'You need to select a lot for the item: {{itemId}}.',
                { itemId: await item.id },
            );
        } else {
            if (
                (await item.lotManagement) === 'lotSublotManagement' &&
                (await stockDetailLot.lot) === null &&
                (await stockDetailLot.sublot) === ''
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line__sublot_mandatory',
                    'You need to select a sublot for the item: {{itemId}}.',
                    { itemId: await item.id },
                );
            }
            if (
                (await item.isExpiryManaged) &&
                (await stockDetailLot.lot) === null &&
                !(await stockDetailLot.expirationDate)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-stock/nodes__stock_count_line__expiration_date_mandatory',
                    'You need to select an expiration date for the item and for the lot: item {{itemId}}, lot {{lotId}}.',
                    { itemId: await item.id, lotId: await stockDetailLot?.lotNumber },
                );
            }
        }
    }
}

export async function controlLineCanBeModified(cx: ValidationContext, stockCountLine: xtremStock.nodes.StockCountLine) {
    if (
        stockCountLine.$.status === NodeStatus.modified &&
        (await stockCountLine.stockTransactionStatus) === 'completed' &&
        !(await stockCountLine.forceUpdateForStock) &&
        !(await stockCountLine.forceUpdateForFinance)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__update_forbidden_count_line_posted',
            'The stockCountLine line {{id}} cannot be updated. It was already posted.',
            { id: stockCountLine._id },
        );
    }
}

export async function controlLineCanBeAdded(cx: ValidationContext, stockCountLine: xtremStock.nodes.StockCountLine) {
    if (stockCountLine.$.status === NodeStatus.added && (await (await stockCountLine.document).status) === 'closed') {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__cannot_add_line_if_counting_closed',
            'You cannot add a line if the stock count is Closed.',
        );
    }
}

export async function controlSerialNumberItem(cx: ValidationContext, stockCountLine: xtremStock.nodes.StockCountLine) {
    if (
        (await stockCountLine.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) &&
        stockCountLine.$.status === NodeStatus.added &&
        (await stockCountLine.document).$.status !== NodeStatus.added &&
        (await (await stockCountLine.item).serialNumberManagement) === 'managed'
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-stock/nodes__stock_count_line__forbid_addition_serialized_managed_item',
            'A stock line for a serialized item cannot be added to an existing stock count.',
        );
    }
}
