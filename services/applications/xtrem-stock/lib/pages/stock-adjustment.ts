import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { Currency, Item, Location, SequenceNumber, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { ReasonCode } from '@sage/xtrem-master-data-api-partial';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stock } from '@sage/xtrem-master-data/build/lib/menu-items/stock';
import type {
    GraphApi,
    StockAdjustmentDisplayStatus,
    StockAdjustmentLine,
    StockAdjustmentLineBinding,
    StockAdjustment as StockAdjustmentNode,
} from '@sage/xtrem-stock-api';
import type {
    StockAdjustmentDetail,
    StockDetailStatus,
    StockDocumentTransactionStatus,
    StockStatus,
} from '@sage/xtrem-stock-data-api';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { getSite } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-stock-adjustment';
import * as StockAdjustmentFunctions from '../client-functions/stock-adjustment';
import * as StockAdjustmentActionFunctions from '../client-functions/stock-adjustment-action-functions';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockAdjustment>({
    menuItem: stock,
    priority: 200,
    title: 'Stock adjustment',
    objectTypeSingular: 'Stock adjustment',
    objectTypePlural: 'Stock adjustments',
    idField() {
        return this.number;
    },
    module: 'stock',
    hasAttachmentsSection: true,
    mode: 'tabs',
    node: '@sage/xtrem-stock/StockAdjustment',
    headerLabel() {
        return this.displayStatus;
    },
    headerSection() {
        return this.mainSection;
    },
    navigationPanel: {
        orderBy: { effectiveDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({ bind: 'number', title: 'Number', isMandatory: true }),
            line2: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date', isMandatory: true }),
            reasonCode: ui.nestedFields.reference({
                bind: 'reasonCode',
                valueField: 'name',
                node: '@sage/xtrem-master-data/ReasonCode',
                tunnelPage: undefined,
                title: 'Adjustment reason',
            }),
            titleRight: ui.nestedFields.label({
                title: 'Status',
                optionType: '@sage/xtrem-stock/StockAdjustmentDisplayStatus',
                bind: 'displayStatus',
                isMandatory: true,
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            description: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            isSetDimensionsMainListHidden: ui.nestedFields.technical({ bind: 'isSetDimensionsMainListHidden' }),
        },
        dropdownActions: [
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockAdjustmentNode>) {
                    const site = await getSite({
                        stockPage: this,
                        siteId: rowItem.stockSite?._id ?? '',
                    });
                    await StockAdjustmentActionFunctions.setDimensions({
                        stockAdjustmentPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        site,
                    });
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<StockAdjustmentNode>) {
                    return rowItem.isSetDimensionsMainListHidden || false;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<StockAdjustmentNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-stock/StockAdjustment',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockAdjustmentNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { stockTransactionStatus: rowItem.stockTransactionStatus },
                        recordId,
                    });
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _ne: 'adjusted' } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Details required', graphQLFilter: { displayStatus: { _eq: 'detailsRequired' } } },
            { title: 'Details entered', graphQLFilter: { displayStatus: { _eq: 'detailsEntered' } } },
            { title: 'Stock posting in progress', graphQLFilter: { displayStatus: { _eq: 'stockPostingInProgress' } } },
            { title: 'Stock posting error', graphQLFilter: { displayStatus: { _eq: 'stockPostingError' } } },
            { title: 'Adjusted', graphQLFilter: { displayStatus: { _eq: 'adjusted' } } },
        ],
    },
    async onLoad() {
        if (this._id.value) {
            this.issueStepSequence.statuses = StockAdjustmentActionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockAdjustmentDisplayStatus,
            );
        }
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockAdjustment,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
        if (!this.$.recordId) {
            this.effectiveDate.value = DateValue.today().toString();
        }
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.stockSite.value,
        });
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message || '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        this.manageDisplayButtonGoToSysNotificationPageAction();
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.defaultDimension,
            ],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.repost, this.post, this.saveStockAdjustment];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this._id.value) {
            this.issueStepSequence.statuses = StockAdjustmentActionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockAdjustmentDisplayStatus,
            );
        }
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveStockAdjustment,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
    },
    onError(error: string) {
        this.$.loader.isHidden = true;
        return MasterDataUtils.formatError(this, error);
    },
})
export class StockAdjustment extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.section<StockAdjustment>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockAdjustment>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<StockAdjustment>({
        title: 'Lines',
    })
    linesSection: ui.containers.Section;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockAdjustment>({
        title: 'Posting',
        isHidden() {
            return !(this.stockSite.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockAdjustment>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.textField<StockAdjustment>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.stepSequenceField<StockAdjustment>({
        parent() {
            return this.mainBlock;
        },
        options: StockAdjustmentActionFunctions.adjustmentStepSequenceOptions,
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    issueStepSequence: ui.fields.StepSequence;

    @ui.decorators.textField<StockAdjustment>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly: true,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockAdjustment, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.reference<StockAdjustment, Site, Currency>({
                bind: 'financialCurrency',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({
                        bind: 'symbol',
                    }),
                    ui.nestedFields.numeric({
                        bind: 'decimalDigits',
                    }),
                ],
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.reference<StockAdjustment, Site, Company>({
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                isHidden: true,
                columns: [
                    ui.nestedFields.checkbox({
                        bind: 'doStockPosting',
                    }),
                ],
            }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        placeholder: 'Select...',
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            if (this.stockSite.value?.isLocationManaged) {
                this.lines.showColumn('location');
            } else {
                this.lines.hideColumn('location');
            }
            await this.lines.redraw();
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.stockSite.value,
            });
        },
    })
    stockSite: ui.fields.Reference;

    @ui.decorators.dateField<StockAdjustment>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        fetchesDefaults: true,
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<StockAdjustment, ReasonCode>({
        parent() {
            return this.mainBlock;
        },
        title: 'Adjustment reason',
        lookupDialogTitle: 'Select reason code',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({
                bind: 'quantityIncreaseAdjustment',
                title: 'Quantity increase',
            }),
            ui.nestedFields.checkbox({
                bind: 'quantityDecreaseAdjustment',
                title: 'Quantity decrease',
            }),
        ],
        width: 'medium',
        isMandatory: true,
        onChange() {
            this.description.value = this.reasonCode.value?.name;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    reasonCode: ui.fields.Reference;

    @ui.decorators.textField<StockAdjustment>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<StockAdjustment>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    isSetDimensionsMainListHidden: ui.fields.Checkbox;

    @ui.decorators.labelField<StockAdjustment>({
        title: 'Display status',
        optionType: '@sage/xtrem-stock/StockAdjustmentDisplayStatus',
        style() {
            return StockPillColor.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<StockAdjustmentDisplayStatus>;

    @ui.decorators.labelField<StockAdjustment>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        style() {
            return StockPillColor.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<StockAdjustment, StockAdjustmentLineBinding>({
        title: 'Lines',
        bind: 'lines',
        canSelect: false,
        node: '@sage/xtrem-stock/StockAdjustmentLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.linesSection;
        },
        columns: [
            ui.nestedFields.label<StockAdjustment, StockAdjustmentLineBinding>({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-stock-data/StockAdjustmentDisplayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.technical<StockAdjustment, StockAdjustmentLineBinding>({ bind: 'stockTransactionStatus' }),
            ui.nestedFields.reference<StockAdjustment, StockAdjustmentLineBinding, Item>({
                title: 'Item',
                valueField: 'name',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                lookupDialogTitle: 'Select item',
                placeholder: 'Select...',
                width: 'medium',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.reference<StockAdjustment, Item, UnitOfMeasure>({
                        title: 'Unit',
                        valueField: 'name',
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.text({ bind: 'lotManagement', title: 'Lot management' }),
                    ui.nestedFields.reference<StockAdjustment, Item, SequenceNumber>({
                        bind: 'lotSequenceNumber',
                        node: '@sage/xtrem-master-data/SequenceNumber',
                        valueField: 'id',
                        isHidden: true,
                    }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
                ],
                isReadOnly(rowId: any, rowData: any) {
                    return StockAdjustment.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                filter() {
                    const filter = { _and: [{}] };
                    if (this.stockSite.value) {
                        filter._and.push({
                            itemSites: { _atLeast: 1, site: this.stockSite.value._id },
                        });
                    }
                    return filter;
                },
                async onChange(_id: number, rowData: ui.PartialNodeWithId<StockAdjustmentLineBinding>) {
                    const newData = {
                        ...rowData,
                        stockUnit: rowData.item?.stockUnit,
                        unitCost: '0',
                        quantityInStockUnit: '0',
                        stockValue: '0',
                    };
                    if (rowData.item) {
                        newData.unitCost = (
                            await StockDataUtils.getLineCost(this, {
                                item: rowData.item._id,
                                site: this.stockSite.value?._id,
                                dateOfValuation: this.effectiveDate.value,
                            })
                        ).orderCost;
                        newData.quantityInStockUnit = extractEdges(
                            await this.$.graph
                                .node('@sage/xtrem-master-data/ItemSite')
                                .query(
                                    ui.queryUtils.edgesSelector(
                                        {
                                            inStockQuantity: true,
                                        },
                                        {
                                            filter: {
                                                item: { _id: rowData.item._id },
                                                site: { _id: this.stockSite.value?._id },
                                            },
                                        },
                                    ),
                                )
                                .execute(),
                        )[0].inStockQuantity;
                        newData.stockValue = String(+(newData.unitCost ?? 0) * +(newData.quantityInStockUnit ?? 0));
                    }

                    const { storedAttributes, storedDimensions } =
                        await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                            page: this,
                            _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                            dimensionDefinitionLevel: 'stockDirect',
                            site: this.stockSite.value,
                            item: rowData.item,
                        });
                    newData.storedAttributes = storedAttributes;
                    newData.storedDimensions = storedDimensions;

                    this.lines.setRecordValue(newData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Stock quantity',
                bind: 'quantityInStockUnit',
                scale(val, rowData) {
                    return StockAdjustment.unitScale(rowData);
                },
                postfix(val, rowData) {
                    return StockAdjustment.unitPostfix(rowData);
                },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<StockAdjustment, StockAdjustmentLineBinding, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                bind: 'stockUnit',
                minLookupCharacters: 1,
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Adjustment quantity',
                bind: 'adjustmentQuantityInStockUnit',
                isReadOnly(rowId: any, rowData: any) {
                    return StockAdjustment.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                scale(val, rowData) {
                    return StockAdjustment.unitScale(rowData);
                },
                postfix(val, rowData) {
                    return StockAdjustment.unitPostfix(rowData);
                },
                onChange(_id: number, rowData: ui.PartialNodeWithId<StockAdjustmentLineBinding>) {
                    const newData: ui.PartialNodeWithId<StockAdjustmentLineBinding> = {
                        _id: rowData._id,
                        newStockQuantity: '0',
                    };
                    if (this.reasonCode.value?.quantityDecreaseAdjustment)
                        newData.newStockQuantity = String(
                            +(rowData.quantityInStockUnit || '0') - +(rowData.adjustmentQuantityInStockUnit || '0'),
                        );

                    if (this.reasonCode.value?.quantityIncreaseAdjustment)
                        newData.newStockQuantity = String(
                            +(rowData.quantityInStockUnit || '0') + +(rowData.adjustmentQuantityInStockUnit || '0'),
                        );

                    this.lines.setRecordValue(newData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Unit cost',
                bind: 'unitCost',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
                scale() {
                    return this.costScale();
                },
            }),
            ui.nestedFields.technical<StockAdjustment, StockAdjustmentLineBinding>({ bind: 'stockDetailStatus' }),
            ui.nestedFields.reference<StockAdjustment, StockAdjustmentLineBinding, Location>({
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                bind: 'location',
                minLookupCharacters: 1,
                lookupDialogTitle: 'Select location',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.reference<StockAdjustment, Location, Location['locationZone']>({
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: '_id',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.reference<
                                StockAdjustment,
                                Location['locationZone'],
                                Location['locationZone']['site']
                            >({
                                bind: 'site',
                                node: '@sage/xtrem-system/Site',
                                valueField: '_id',
                                isHidden: true,
                            }),
                        ],
                    }),
                ],
                filter() {
                    return {
                        locationZone: { site: { _id: { _eq: this.stockSite.value?._id } } },
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isReadOnly(rowId: any, rowData: any) {
                    return (
                        !this.stockSite.value?.isLocationManaged ||
                        StockAdjustment.lineStatusReadonly(rowData.stockTransactionStatus)
                    );
                },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
            }),
            ui.nestedFields.reference<StockAdjustment, StockAdjustmentLineBinding, StockStatus>({
                title: 'Quality control',
                bind: 'stockStatus',
                lookupDialogTitle: 'Select quality value',
                minLookupCharacters: 0,
                isReadOnly(rowId: any, rowData: any) {
                    return StockAdjustment.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Stock value',
                bind: 'stockValue',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
                scale() {
                    return this.costScale();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'newStockQuantity',
                title: 'New stock quantity',
                scale(_value, rowData) {
                    return StockAdjustment.unitScale(rowData);
                },
                postfix(_value, rowData) {
                    return StockAdjustment.unitPostfix(rowData);
                },
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'stockDetailsToAdjust' as any, isTransient: true }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Stock details`,
                isHidden(rowId, rowItem) {
                    return !(rowItem.item && rowItem.adjustmentQuantityInStockUnit);
                },
                async onClick(rowId: any, rowItem: ui.PartialCollectionValue<StockAdjustmentLineBinding>) {
                    await this.editStockDetails(rowId, +(rowItem.adjustmentQuantityInStockUnit || '0'), rowItem);
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(rowId: any, rowItem: ui.PartialCollectionValue<StockAdjustmentLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable:
                                    !StockAdjustment.lineStatusReadonly(rowData.stockTransactionStatus) ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled(_rowID, rowItem) {
                    return StockAdjustment.lineStatusReadonly(rowItem.stockTransactionStatus);
                },
                onClick(rowID: any) {
                    this.lines.removeRecord(rowID);
                    this.disableHeaderFields();
                },
            },
        ],
        fieldActions() {
            return [this.addAdjustLine];
        },
    })
    lines: ui.fields.Table<StockAdjustmentLineBinding>;

    @ui.decorators.tableField<StockAdjustment, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockAdjustment>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockAdjustment>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'stockAdjustment',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.pageAction<StockAdjustment>({
        title: 'Save',
        buttonType: 'primary',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validateResult = await this.validate();
            if (validateResult.length !== 0) {
                return;
            }
            StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(
                this.lines.value,
                this.lines,
                this.reasonCode.value?.quantityDecreaseAdjustment ? 'issue' : 'receipt',
            );
            await this.$standardSaveAction.execute();
        },
    })
    saveStockAdjustment: ui.PageAction;

    @ui.decorators.pageAction<StockAdjustment>({
        title() {
            if (this.displayStatus.value === 'stockPostingError') {
                return ui.localize(
                    '@sage/xtrem-stock/pages__stock_adjustment__stock_posting_error',
                    'Stock posting error',
                );
            }
            return ui.localize(
                '@sage/xtrem-stock/pages__stock_adjustment__stock_journal_inquiry',
                'Stock journal inquiry',
            );
        },
        isHidden() {
            return ['detailsRequired', 'detailsEntered'].includes(this.displayStatus.value || '');
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.lines,
                },
            );
            if (this.stockTransactionStatus.value === 'inProgress') {
                await StockAdjustmentFunctions.correctStockTransactionStatusInProgress({
                    stockAdjustmentPage: this,
                    stockAdjustmentId: this._id.value ?? '',
                });
            }
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockAdjustment>({
        title: 'Post stock',
        buttonType: 'primary',
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value || '') ||
                !this._id.value ||
                this.$.isDirty
            );
        },
        onError(error: string) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.post.isDisabled = true;
            await this.$.graph
                .node('@sage/xtrem-stock/StockAdjustment')
                .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                .execute();
            if (this._id.value) {
                await StockAdjustmentActionFunctions.checkForUpdate({
                    stockAdjustmentPage: this,
                    recordId: this._id.value,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockAdjustment>({
        title: 'Repost',
        isHidden() {
            return !(this.fromNotificationHistory && (this.stockSite?.value?.legalCompany?.doStockPosting || false));
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError((await this.adjustmentRepost()).message, this);
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    async adjustmentRepost() {
        const documentLines = this.lines.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        this.$.loader.isHidden = false;

        const postResult = await this.$.graph
            .node('@sage/xtrem-stock/StockAdjustment')
            .mutations.repost(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { stockAdjustment: this._id.value || '', documentLines },
            )
            .execute();

        this.$.loader.isHidden = true;
        if (!postResult.wasSuccessful) {
            this.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-stock/pages__stock_adjustment__repost_errors',
                    'Errors while reposting:',
                )}**\n${postResult.message}`,
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    @ui.decorators.pageAction<StockAdjustment>({
        icon: 'add',
        title: 'Add line',
        isDisabled() {
            return (
                !this.stockSite.value ||
                !this.reasonCode.value ||
                (!!this.stockTransactionStatus.value && this.stockTransactionStatus.value !== 'draft')
            );
        },
        onClick() {
            const lineData = {
                ...dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                    {} as any,
                    this._defaultDimensionsAttributes,
                ),
                stockDetailStatus: 'required' as StockDetailStatus,
                stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
                displayStatus: 'detailsRequired' as StockAdjustmentDisplayStatus,
            };
            this.lines.addRecord(lineData);
            this.disableHeaderFields();
        },
    })
    addAdjustLine: ui.PageAction;

    @ui.decorators.pageAction<StockAdjustment>({
        icon: 'none',
        title: 'Set dimensions',
        isHidden() {
            return this.isSetDimensionsMainListHidden.value === true;
        },
        isDisabled() {
            return !this.stockSite.value;
        },
        async onClick() {
            const filter = () => true;
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    setNewQuantityInStockUnit(_id: number, rowData: ui.PartialNodeWithId<StockAdjustmentLine>) {
        const newData: ui.PartialNodeWithId<StockAdjustmentLine> = {
            _id: rowData._id,
            newStockQuantity: '0',
        };
        if (this.reasonCode.value?.quantityDecreaseAdjustment)
            newData.newStockQuantity = String(
                +(rowData.quantityInStockUnit || '0') - +(rowData.adjustmentQuantityInStockUnit || '0'),
            );

        if (this.reasonCode.value?.quantityIncreaseAdjustment)
            newData.newStockQuantity = String(
                +(rowData.quantityInStockUnit || '0') + +(rowData.adjustmentQuantityInStockUnit || '0'),
            );

        this.lines.setRecordValue(newData);
    }

    disableHeaderFields() {
        const enteredLines = this.lines.value.length > 0;
        this.stockSite.isDisabled = enteredLines;
        this.reasonCode.isDisabled = enteredLines;
    }

    /**
     * Stock detail management.
     * Sets the stock details from line data where needed.
     */
    setStockDetails() {
        this.lines.value.forEach((adjustTableLine: any) => {
            if (!adjustTableLine.stockDetails?.length) {
                adjustTableLine.stockDetails = JSON.stringify(StockAdjustment.getDetailFromLine(adjustTableLine));
                this.lines.setRecordValue(adjustTableLine);
            }
        });
    }

    /**
     * Stock detail management.
     * Calls the StockAdjustDetailsPanel.
     * For the user to be able to enter the stock detail for a document line
     * Updates the stockDetails (to save data) and stockDetailsToAdjust (to h temporary the selected stock records to adjust) line properties
     * @param lineData
     */
    async editStockDetails(
        rowId: any,
        adjustmentQuantity: number,
        lineData: ui.PartialCollectionValue<StockAdjustmentLineBinding>,
    ) {
        if (this.reasonCode.value?.quantityDecreaseAdjustment) {
            const line = MasterDataUtils.removeExtractEdgesPartial(lineData);
            await MasterDataUtils.applyPanelToLineIfChanged(
                this.lines,
                StockDetailHelper.editStockDetails(this, line, {
                    movementType: 'issue',
                    data: {
                        isEditable: !StockAdjustment.lineStatusReadonly(line.stockTransactionStatus),
                        effectiveDate: this.effectiveDate.value || undefined,
                        documentLineSortValue: line._sortValue,
                        documentLine: rowId,
                        jsonStockDetails: line.jsonStockDetails,
                        item: line.item?._id,
                        stockSite: this.stockSite.value?._id,
                        quantity: +adjustmentQuantity,
                        number: this.number.value || undefined,
                        unit: line.item.stockUnit?._id,
                        stockStatus: line.stockStatus?._id,
                        location: line.location?._id,
                        orderCost: line.orderCost,
                        valuedCost: line.valuedCost,
                        searchCriteria: {
                            activeQuantityInStockUnit: +line.quantityInStockUnit,
                            item: line.item?._id,
                            site: this.stockSite.value?._id,
                            stockUnit: line.item.stockUnit?._id,
                            statusList: [],
                            ignoreExpirationDate: true,
                        },
                        stockDetailType: '@sage/xtrem-stock-data/StockAdjustmentDetail',
                    },
                }) as Promise<StockAdjustmentLineBinding>,
            );
        } else if (this.reasonCode.value?.quantityIncreaseAdjustment) {
            const line = MasterDataUtils.removeExtractEdgesPartial(lineData);
            try {
                await MasterDataUtils.applyPanelToLineIfChanged(
                    this.lines,
                    StockDetailHelper.editStockDetails(this as any, line, {
                        movementType: 'receipt',
                        data: {
                            isEditable: !StockAdjustment.lineStatusReadonly(line.stockTransactionStatus),
                            effectiveDate: this.effectiveDate.value || undefined,
                            documentLineSortValue: line._sortValue,
                            documentLine: rowId,
                            jsonStockDetails: line.jsonStockDetails,
                            item: line.item?._id,
                            stockSite: this.stockSite.value?._id,
                            quantity: +adjustmentQuantity,
                            unit: line.item.stockUnit?._id,
                            trackCheckStock: 'track',
                            stockStatus: line.stockStatus?._id || this.stockSite.value?.defaultStockStatus?._id,
                            location: line.location?._id || this.stockSite.value?.defaultLocation?._id,
                            existingLot: undefined,
                            lotCreateData: undefined,
                            orderCost: line.orderCost,
                            valuedCost: line.valuedCost,
                            stockTransactionStatus: line.stockTransactionStatus,
                            stockDetailType: '@sage/xtrem-stock-data/StockAdjustmentDetail',
                            fieldCustomizations: {
                                date: {
                                    isHidden: true,
                                },
                            },
                        },
                    }) as Promise<StockAdjustmentLineBinding>,
                );
            } catch (err) {
                console.error('Stock details onClick function', err);
            }
        }
    }

    /**
     * Stock detail management.
     * Returns an array of one StockLineDetail, with the data that's on the document line.
     * To be used when the user enters the data directly on the document line instead of going through the StockAdjustDetailsPanel
     * @param lineData
     * @returns array of one StockLineDetail
     */
    private static getDetailFromLine(lineData: any): Partial<StockAdjustmentDetail>[] {
        return [
            {
                quantityInStockUnit: lineData.adjustmentQuantityInStockUnit,
                stockStatus: lineData.stockStatus,
                owner: '',
                orderCost: lineData.orderCost,
                valuedCost: lineData.valuedCost,
                location: lineData.location,
            } as Partial<StockAdjustmentDetail>,
        ];
    }

    /**
     * Validates the page (including mandatory stock fields)
     * @returns string array (promise of) with all the error messages
     */
    async validate(): Promise<string[]> {
        // check if the serial numbers on all new (or updated) lines are unique
        if (this.$.isServiceOptionEnabled('serialNumberOption')) {
            const validationMessages = StockValuationLib.validateSerialNumbers(this);
            if (validationMessages && !!validationMessages.length) {
                this.$.showToast(validationMessages.join('\n\n'), { type: 'error' });
                return validationMessages;
            }
        }

        // TODO: remove quantity, status and location check when isMandatory on tables is working
        const validation = await this.$.page.validate();
        this.lines.value.forEach((adjustTableLine: any) => {
            if (!adjustTableLine.stockDetails?.length) {
                if (!adjustTableLine.adjustmentQuantityInStockUnit) {
                    validation.push(
                        ui.localize(
                            '@sage/xtrem-stock/pages__stock-adjustment-detail-panel__notification__The_adjustment_quantity_is_mandatory',
                            'The adjustment quantity is mandatory.',
                        ),
                    );
                }
            }
        });

        if (validation && !!validation.length) {
            this.$.showToast(validation.join('\n\n'), { type: 'error' });
        }
        return validation;
    }

    private static lineStatusReadonly(stockTransactionStatus: StockDocumentTransactionStatus) {
        return ['inProgress', 'completed'].includes(stockTransactionStatus);
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    costSymbol() {
        return this.stockSite.value?.financialCurrency.symbol || '';
    }

    costScale() {
        return this.stockSite.value?.financialCurrency.decimalDigits ?? 2;
    }

    static unitPostfix(rowData: any) {
        return rowData.stockUnit?.symbol;
    }

    static unitScale(rowData: any) {
        return rowData.stockUnit?.decimalDigits || 0;
    }

    setDeleteAction() {
        if (
            !this.$standardDeleteAction.isDisabled &&
            this.lines.value.some((line: ui.PartialCollectionValue<StockAdjustmentLineBinding>) =>
                StockAdjustment.lineStatusReadonly(line.stockTransactionStatus),
            )
        ) {
            this.$standardDeleteAction.isHidden = true;
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtons.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
