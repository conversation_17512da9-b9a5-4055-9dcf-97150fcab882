import type { ExtractEdges, ExtractEdgesPartial, NodeFilter } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { <PERSON><PERSON><PERSON>cy, Item, ItemSite, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stock } from '@sage/xtrem-master-data/build/lib/menu-items/stock';
import type {
    GraphApi,
    StockValueChangeLineBinding,
    StockValueChange as StockValueChangeNode,
} from '@sage/xtrem-stock-api';
import type { FifoValuationTier, StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import type * as StockInterfaces from '../client-functions/interfaces';

@ui.decorators.page<StockValueChange, StockValueChangeNode>({
    menuItem: stock,
    priority: 210,
    title: 'Stock value change',
    objectTypeSingular: 'Stock value change',
    objectTypePlural: 'Stock value changes',
    idField() {
        return this.number;
    },
    module: 'stock',
    hasAttachmentsSection: true,
    mode: 'tabs',
    headerSection() {
        return this.mainSection;
    },
    node: '@sage/xtrem-stock/StockValueChange',
    navigationPanel: {
        orderBy: { number: -1, postedDate: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                title: 'Site name',
                tunnelPage: undefined,
            }),
            siteID: ui.nestedFields.text({
                title: 'Site ID',
                bind: { site: { id: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'postedDate', title: 'Date' }),
            line3: ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: undefined,
                title: 'Item',
            }),
            itemId: ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            itemDescription: ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                title: 'Stock status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                bind: 'stockTransactionStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData.stockTransactionStatus,
                    ),
            }),
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.saveStockValueChange, this.repost, this.post];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveStockValueChange,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockValueChange,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.managePageActions();
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.site.value,
            item: this.item.value,
        });
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (!this.$.recordId) {
            this.stockTransactionStatus.value = 'draft';
        }
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message || '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        this.manageDisplayButtonGoToSysNotificationPageAction();
    },
})
export class StockValueChange
    extends ui.Page<GraphApi, StockValueChangeNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    defaultDimensionActionArray: ui.PageAction[];

    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.section<StockValueChange>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockValueChange>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<StockValueChange>({
        title: 'Lines',
    })
    linesSection: ui.containers.Section;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockValueChange>({
        title: 'Posting',
        isHidden() {
            return !(this.site.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockValueChange>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.textField<StockValueChange>({
        title: 'Number',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        maxLength: 24,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockValueChange, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.reference<StockValueChange, Site, Currency>({
                bind: 'financialCurrency',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.reference<StockValueChange, Site, Company>({
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                isHidden: true,
                columns: [
                    ui.nestedFields.checkbox({
                        bind: 'doStockPosting',
                    }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                ],
            }),
        ],
        filter() {
            return this.item.value
                ? ({
                      isActive: { _eq: true },
                      itemSites: {
                          _atLeast: 1,
                          _and: [{ item: this.item.value?._id }, { valuationMethod: { _ne: 'standardCost' } }],
                      },
                      // TS 5.2: added any cast to compile
                  } as any)
                : { isActive: { _eq: true } };
        },
        placeholder: 'Select...',
        width: 'medium',
        isMandatory: true,
        async onChange() {
            await this.updateHeaderValues();
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.site.value,
                item: this.item.value,
            });
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    site: ui.fields.Reference;

    @ui.decorators.referenceField<StockValueChange, Item>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item',
        shouldSuggestionsIncludeColumns: true,
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical<StockValueChange, Item, UnitOfMeasure>({ bind: { stockUnit: { _id: true } } }),
            ui.nestedFields.technical<StockValueChange, Item, UnitOfMeasure>({
                bind: { stockUnit: { name: true } },
            }),
            ui.nestedFields.technical<StockValueChange, Item, UnitOfMeasure>({
                bind: { stockUnit: { symbol: true } },
            }),
            ui.nestedFields.technical<StockValueChange, Item, UnitOfMeasure>({
                bind: { stockUnit: { id: true } },
            }),
            ui.nestedFields.technical<StockValueChange, Item, UnitOfMeasure>({
                bind: { stockUnit: { decimalDigits: true } },
            }),
        ],
        filter() {
            return this.site.value
                ? ({
                      isStockManaged: true,
                      itemSites: {
                          _atLeast: 1,
                          _and: [{ site: this.site.value?._id }, { valuationMethod: { _ne: 'standardCost' } }],
                      },
                  } as NodeFilter<Item>)
                : {
                      isStockManaged: true,
                  };
        },
        orderBy: {
            name: +1,
            id: +1,
        },
        async onChange() {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.site.value,
                item: this.item.value,
            });
            await this.updateHeaderValues();
            await this.lines.redraw();
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    item: ui.fields.Reference<Item>;

    async updateHeaderValues() {
        if (!this.item.value || !this.site.value) {
            this.valuationMethod.value = null;
            return;
        }

        const itemSites = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemSite')
                .query(
                    ui.queryUtils.edgesSelector<ItemSite>(
                        {
                            valuationMethod: true,
                        },
                        {
                            filter: {
                                _and: [{ item: this.item.value._id }, { site: this.site.value._id }],
                            },
                        },
                    ),
                )
                .execute(),
        ) as ExtractEdges<ItemSite>[];
        if (itemSites.length) {
            this.valuationMethod.value = itemSites[0].valuationMethod;
        }
    }

    @ui.decorators.textField<StockValueChange>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
    })
    description: ui.fields.Text;

    @ui.decorators.dropdownListField<StockValueChange>({
        parent() {
            return this.mainBlock;
        },
        title: 'Valuation method',
        optionType: '@sage/xtrem-master-data/costValuationMethod',
        isReadOnly: true,
    })
    valuationMethod: ui.fields.DropdownList;

    @ui.decorators.dateField<StockValueChange>({
        title: 'Date',
        bind: 'postedDate',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly: true,
    })
    postedDate: ui.fields.Date;

    @ui.decorators.labelField<StockValueChange>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return StockPillColor.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tile<StockValueChange>({
        parent() {
            return this.mainSection;
        },
        // TODO: DNE REFAC To be removed again later: The values on this tile are odd and may confuse the user.
        //       It was decided that we make it invisible until a better solution comes up.
        isHidden: true,
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<StockValueChange>({
        parent() {
            return this.tileContainer;
        },
        title: 'On hand',
        scale() {
            return MasterDataUtils.getScaleValue(0, this.item.value?.stockUnit?.decimalDigits);
        },
        postfix() {
            return this.item.value?.stockUnit?.symbol || '';
        },
        width: 'medium',
        isHidden: true,
    })
    totalQuantityInStock: ui.fields.Numeric;

    @ui.decorators.numericField<StockValueChange>({
        parent() {
            return this.tileContainer;
        },
        title: 'Value',
        unit() {
            return this.site.value?.financialCurrency;
        },
        width: 'medium',
        isHidden: true,
    })
    totalValueOfStock: ui.fields.Numeric;

    @ui.decorators.numericField<StockValueChange>({
        parent() {
            return this.tileContainer;
        },
        title: 'Unit cost',
        unit() {
            return this.site.value?.financialCurrency;
        },
        width: 'medium',
        // isHidden: true,
    })
    unitCost: ui.fields.Numeric;

    @ui.decorators.tableField<StockValueChange, StockValueChangeLineBinding>({
        title: 'Lines',
        bind: 'lines',
        canSelect: false,
        canExport: true,
        node: '@sage/xtrem-stock/StockValueChangeLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.linesSection;
        },
        isHidden() {
            return !this.valuationMethod.value;
        },
        columns: [
            ui.nestedFields.reference<StockValueChange, StockValueChangeLineBinding, FifoValuationTier>({
                title: 'Effective date',
                node: '@sage/xtrem-stock-data/FifoValuationTier',
                valueField: 'effectiveDate',
                bind: 'fifoCost',
                isReadOnly: true,
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.reference<StockValueChange, StockValueChangeLineBinding, FifoValuationTier>({
                title: 'Sequence',
                node: '@sage/xtrem-stock-data/FifoValuationTier',
                valueField: 'sequence',
                bind: 'fifoCost',
                isReadOnly: true,
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.link({
                title: 'Receipt',
                bind: { fifoCost: { receiptDocumentLine: { documentNumber: true } } },
                async onClick(_id: string, rowData: any) {
                    const result = await this.loadFifoValuationTier(rowData.fifoCost._id);
                    const fifoCost = result[0];
                    const pageName = fifoCost.receiptDocumentLine?._constructor
                        ? (DocumentLink.getDocumentPageName(fifoCost.receiptDocumentLine._constructor) as string)
                        : '';
                    const docId = fifoCost?.receiptDocumentLine?.documentId
                        ? fifoCost?.receiptDocumentLine?.documentId
                        : 0;
                    if (pageName.length && docId) {
                        this.$.router.goToExternal(`${pageName}/${btoa(JSON.stringify({ _id: docId }))}`);
                    }
                },
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.reference<StockValueChange, StockValueChangeLineBinding, FifoValuationTier>({
                title: 'Receipt quantity',
                node: '@sage/xtrem-stock-data/FifoValuationTier',
                valueField: 'receiptQuantity',
                bind: 'fifoCost',
                isReadOnly: true,
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.reference<StockValueChange, StockValueChangeLineBinding, FifoValuationTier>({
                title: 'Remaining quantity',
                node: '@sage/xtrem-stock-data/FifoValuationTier',
                valueField: 'remainingQuantity',
                bind: 'fifoCost',
                isReadOnly: true,
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantity',
                isReadOnly: true,
                isHidden() {
                    return !this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Unit cost',
                bind: 'unitCost',
                isReadOnly: true,
                unit() {
                    return this.site.value?.financialCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'New unit cost',
                bind: 'newUnitCost',
                isMandatory: true,
                isReadOnly(_id, rowData) {
                    return StockValueChange.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                onChange(_id, rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>) {
                    this.calculateAmount(rowData);
                },
                unit() {
                    return this.site.value?.financialCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                bind: 'amount',
                isReadOnly: true,
                unit() {
                    return this.site.value?.financialCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'New amount',
                bind: 'newAmount',
                isMandatory: true,
                isReadOnly(_id, rowData) {
                    return StockValueChange.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                onChange(_id, rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>) {
                    this.calculateCost(rowData);
                },
                scale() {
                    return MasterDataUtils.getScaleValue(2, this.site.value?.financialCurrency.decimalDigits);
                },
                prefix() {
                    return this.site.value?.financialCurrency.symbol || '';
                },
            }),
            ui.nestedFields.reference<StockValueChange, StockValueChangeLineBinding, FifoValuationTier>({
                title: 'Non-absorbed amount',
                node: '@sage/xtrem-stock-data/FifoValuationTier',
                valueField: 'nonAbsorbedAmount',
                bind: 'fifoCost',
                isReadOnly: true,
                isHidden() {
                    return this.isFifoCostFieldHidden();
                },
            }),
            ui.nestedFields.label<StockValueChange, StockValueChangeLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/stockTransactionStatus',
                isHidden() {
                    return ['draft', 'inProgress', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus('stockTransactionStatus', rowData.stockTransactionStatus),
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled(_rowId: string, rowItem: ui.PartialNodeWithId<StockValueChangeLineBinding>) {
                    return rowItem.stockTransactionStatus === 'completed';
                },
                onClick(recordId) {
                    this.lines.removeRecord(recordId);
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(_rowId: string, rowItem: ui.PartialNodeWithId<StockValueChangeLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable:
                                    !StockValueChange.lineStatusReadonly(rowItem.stockTransactionStatus) ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
        ],
        fieldActions() {
            return [this.addAverageCost, this.selectFromFifo, this.defaultDimension];
        },
    })
    lines: ui.fields.Table<StockValueChangeLineBinding>;

    @ui.decorators.tableField<StockValueChange, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockValueChange>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockValueChange>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'stockValueChange',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.pageAction<StockValueChange>({
        icon: 'save',
        title: 'Save',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validation = await this.$.page.validate();
            if (!validation.length) {
                const isDialog = this.$.isInDialog;
                await this.$standardSaveAction.execute(true);
                if (isDialog) return;
                const financeIntegrationCheckResult = await this.$.graph
                    .node('@sage/xtrem-stock/StockValueChange')
                    .mutations.financeIntegrationCheck(
                        {
                            wasSuccessful: true,
                            message: true,
                        },
                        { valueChange: this.$.recordId || '' },
                    )
                    .execute();

                if (!financeIntegrationCheckResult.wasSuccessful) {
                    this.$.showToast(
                        `**${ui.localize(
                            '@sage/xtrem-stock/pages__stock_value_change__save_warnings',
                            'Warnings while saving:',
                        )}**\n${financeIntegrationCheckResult.message}`,
                        { type: 'warning', timeout: 20000 },
                    );
                }
                if (this.$.isInTunnel) {
                    this.$.setPageClean();
                    this.$.loader.isHidden = true;
                    this.$.finish({ _id: this.$.queryParameters._id, [ui.SHOULD_REFRESH_DIALOG_RESULT]: true }); // return ID
                    return;
                }
                await this.$.router.refresh(true);
                this.$.setPageClean();
                await this.init();
            }
        },
    })
    saveStockValueChange: ui.PageAction;

    @ui.decorators.pageAction<StockValueChange>({
        title: 'Post stock',
        onError(e) {
            return MasterDataUtils.formatError(this, e);
        },
        isDisabled() {
            return this.$.isDirty || !this.$.recordId;
        },
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value || 'draft') || !this.lines.value.length
            );
        },
        async onClick() {
            await this.postStockChange();
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockValueChange>({
        title: 'Repost',
        isHidden() {
            return !(this.fromNotificationHistory && (this.site?.value?.legalCompany?.doStockPosting || false));
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError((await this.valueChangeRepost()).message, this);
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    async valueChangeRepost() {
        const documentLines = this.lines.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        this.$.loader.isHidden = false;

        const postResult = await this.$.graph
            .node('@sage/xtrem-stock/StockValueChange')
            .mutations.repost(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { stockValueChange: this.$.recordId || '', documentLines },
            )
            .execute();

        this.$.loader.isHidden = true;
        if (!postResult.wasSuccessful) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_value_change__repost_errors',
                    'Errors while reposting: {{errors}}',
                    { errors: postResult.message },
                ),
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    @ui.decorators.pageAction<StockValueChange>({
        icon: 'search',
        title: 'Add from FIFO stack',
        isHidden() {
            return this.isFifoCostFieldHidden();
        },
        isDisabled() {
            return this.stockTransactionStatus.value === 'completed';
        },
        async onClick() {
            await this.addFifoLines();
        },
    })
    selectFromFifo: ui.PageAction;

    @ui.decorators.pageAction<StockValueChange>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            return this.stockTransactionStatus.value === 'completed';
        },
        async onClick() {
            const filter = (line: ui.PartialNodeWithId<StockValueChangeLineBinding>) =>
                !StockValueChange.lineStatusReadonly(line.stockTransactionStatus);
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockValueChange>({
        icon: 'add',
        title: 'Add',
        isDisabled() {
            return this.lines.value.length > 0;
        },
        isHidden() {
            return this.valuationMethod.value !== 'averageCost';
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this._addStockAverageCostLines();
            this.$.loader.isHidden = true;
        },
    })
    addAverageCost: ui.PageAction;

    async addFifoLines() {
        const linesSelection: StockInterfaces.StockValueChangeFifo = await this.$.dialog.page(
            '@sage/xtrem-stock/FifoValuationTierTablePanel',
            {
                siteId: this.site.value?.id || '',
                itemId: this.item.value?.id || '',
                existingFifo: JSON.stringify(this.getFifoFromLines()),
            },
            {
                size: 'large',
            },
        );

        if (linesSelection?.lines.length) {
            this._addFifoLines(linesSelection);
        }
    }

    private _addFifoLines(linesSelection: StockInterfaces.StockValueChangeFifo) {
        linesSelection.lines.forEach(newLine => {
            const values = newLine;
            const lineData = {
                ...dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                    { _sortValue: (this.lines.value[this.lines.value.length - 1]?._sortValue || 0) + 10 } as any,
                    this._defaultDimensionsAttributes,
                ),
                stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
            };

            this.lines.addRecord({
                ...lineData,
                ...this.getNewLine({
                    ...lineData,
                    fifoCost: newLine,
                    amount: values.amount,
                    unitCost: values.unitCost,
                    quantity: newLine.remainingQuantity,
                }),
            });
        });
    }

    private async _addStockAverageCostLines() {
        const lineData = {
            ...dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                { _sortValue: (this.lines.value[this.lines.value.length - 1]?._sortValue || 0) + 10 } as any,
                this._defaultDimensionsAttributes,
            ),
            stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
        };
        let quantity = '0';
        let unitCost = '0';
        let amount = '0';
        if (this.item.value?._id && this.site.value?._id) {
            const averageCostSiteDetails = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-master-data/ItemSite')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                inStockQuantity: true,
                                stockValuationAtAverageCost: true,
                                averageCostValue: true,
                            },
                            {
                                filter: {
                                    item: { _id: this.item.value?._id },
                                    site: { _id: this.site.value?._id },
                                },
                            },
                        ),
                    )
                    .execute(),
            );
            if (averageCostSiteDetails.length) {
                const itemSite = averageCostSiteDetails[0];
                quantity = itemSite.inStockQuantity;
                unitCost = itemSite.averageCostValue;
                amount = itemSite.stockValuationAtAverageCost;
            }
        }
        const newLine = this.getNewLine({
            ...lineData,
            quantity,
            unitCost,
            amount,
        });
        this.lines.addOrUpdateRecordValue(newLine);
    }

    getNewLine(data: ExtractEdgesPartial<StockValueChangeLineBinding>) {
        return {
            ...data,
            newUnitCost: Number(data.unitCost).toFixed(
                MasterDataUtils.getScaleValue(2, this.site.value?.financialCurrency.decimalDigits),
            ),
            newAmount: Number(data.amount).toFixed(
                MasterDataUtils.getScaleValue(2, this.site.value?.financialCurrency.decimalDigits),
            ),
            amount: Number(data.amount).toFixed(
                MasterDataUtils.getScaleValue(2, this.site.value?.financialCurrency.decimalDigits),
            ),
            unitCost: Number(data.unitCost).toFixed(
                MasterDataUtils.getScaleValue(2, this.site.value?.financialCurrency.decimalDigits),
            ),
            stockTransactionStatus: 'draft',
        };
    }

    async init() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockValueChange,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.site.value,
        });
        this.managePageActions();
    }

    managePageActions() {
        this.setDeleteAction();
    }

    setDeleteAction() {
        if (!this.$standardDeleteAction.isDisabled) {
            this.$standardDeleteAction.isDisabled = this.stockTransactionStatus.value !== 'draft';
        }
    }

    private static lineStatusReadonly(stockTransactionStatus: StockDocumentTransactionStatus) {
        return ['completed'].includes(stockTransactionStatus);
    }

    private getStockDetailData(rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>): string {
        return JSON.stringify([
            {
                id: rowData._id,
                valuationMethod: this.valuationMethod.value,
                fifoValuationTier: rowData.fifoCost,
                amount: Number(rowData.newAmount ?? 0) - Number(rowData.amount ?? 0),
                site: this.site.value,
                item: this.item.value,
                stockUnit: this.item.value?.stockUnit,
                quantityInStockUnit: Number(rowData.quantity),
                documentLine: rowData._id,
            },
        ]);
    }

    async postStockChange() {
        this.$.loader.isHidden = false;
        if (this.$.recordId) {
            await this.$.graph
                .node('@sage/xtrem-stock/StockValueChange')
                .mutations.postToStock(true, { documentIds: [this.$.recordId] })
                .execute();
        }
        await this.$.router.refresh();
        await this.$.refreshNavigationPanel();

        this.$.loader.isHidden = true;
    }

    private updateLine(rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>) {
        rowData.jsonStockDetails = this.getStockDetailData(rowData);
        this.lines.addOrUpdateRecordValue(rowData);
    }

    private calculateAmount(rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>) {
        rowData.newAmount = String(Number(rowData.newUnitCost) * Number(rowData.quantity));
        this.updateLine(rowData);
    }

    private calculateCost(rowData: ui.PartialCollectionValue<StockValueChangeLineBinding>) {
        rowData.newUnitCost = String(Number(rowData.newAmount) / Number(rowData.quantity));
        this.updateLine(rowData);
    }

    async loadFifoValuationTier(documentID: string | number | boolean) {
        return withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/FifoValuationTier')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            receiptDocumentLine: {
                                _constructor: true,
                                documentNumber: true,
                                documentId: true,
                            },
                        },
                        {
                            filter: {
                                _id: documentID,
                            },
                        },
                    ),
                )
                .execute(),
        );
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    getFifoFromLines(): number[] {
        return this.lines.value.map(line => Number(line?.fifoCost?._id));
    }

    isFifoCostFieldHidden() {
        return this.valuationMethod.value !== 'fifoCost';
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
