import type { ExtractEdges } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { date } from '@sage/xtrem-date-time';
import type { Item, ItemSite, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { WorkOrderPanelParameters } from '@sage/xtrem-master-data/build/lib/client-functions/interfaces';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { MrpCalculation as MrpCalculationNode, MrpResultLineBinding, MrpWorkLine } from '@sage/xtrem-mrp-data-api';
import type { CalculationStatus } from '@sage/xtrem-mrp-data-api-partial';
import type { GraphApi } from '@sage/xtrem-stock-api';
import { StockReordering } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-reordering';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';

@ui.decorators.page<MrpCalculation, MrpCalculationNode>({
    menuItem: StockReordering,
    priority: 20,
    title: 'MRP calculation',
    objectTypeSingular: 'MRP calculation result',
    objectTypePlural: 'MRP calculation results',
    idField() {
        return this.description;
    },
    module: 'stock',
    mode: 'default',
    node: '@sage/xtrem-mrp-data/MrpCalculation',
    headerSection() {
        return this.mainSection;
    },
    headerLabel() {
        return this.calculationStatus;
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'bulkDelete',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
            },
        ],
        orderBy: { calculationDate: -1, description: 1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'description' }),
            titleRight: ui.nestedFields.date({ bind: 'calculationDate', title: 'Calculation date' }),
            line2Right: ui.nestedFields.label({
                title: 'Status',
                optionType: '@sage/xtrem-mrp-data/CalculationStatus',
                bind: 'calculationStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('CalculationStatus', rowData.calculationStatus),
            }),
            numberOfWeeks: ui.nestedFields.text({
                bind: 'numberOfWeeks',
                title: 'Period in weeks',
                isHiddenOnMainField: true,
            }),
            user: ui.nestedFields.reference({
                bind: 'user',
                valueField: 'displayName',
                node: '@sage/xtrem-system/User',
                isHiddenOnMainField: true,
                title: 'Calculation user',
                tunnelPage: undefined,
            }),
        },
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    createAction() {
        return this.createMrpCalculationRequest;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return masterDataUtils.formatError(this, error);
    },
    onLoad() {
        this.fromItemText.helperText = this.fromItem.value ? JSON.parse(this.fromItem.value).id : '';
        this.fromItemText.value = this.fromItem.value ? JSON.parse(this.fromItem.value).name : '';
        this.toItemText.helperText = this.toItem.value ? JSON.parse(this.toItem.value).id : '';
        this.toItemText.value = this.toItem.value ? JSON.parse(this.toItem.value).name : '';
        const companies = this.companies.value ? JSON.parse(this.companies.value).array : null;
        this.fieldSeparator1.isHidden = this.calculationStatus.value !== 'error';

        if (companies) {
            this.multiRefCompanies.value = companies;
        }

        const sites = this.sites.value ? JSON.parse(this.sites.value).array : null;

        if (sites) {
            this.multiRefSites.value = sites;
        }
    },
})
export class MrpCalculation extends ui.Page<GraphApi, MrpCalculationNode> {
    @ui.decorators.pageAction<MrpCalculation>({
        title: 'Create',
        icon: 'add',
        buttonType: 'primary',
        async onClick() {
            const request = await this.$.dialog.page(
                '@sage/xtrem-stock/MrpCalculationRequest',
                { called: true },
                {
                    rightAligned: false,
                    size: 'large',
                    resolveOnCancel: false,
                },
            );
            if (request) {
                // Need to wait for the asynchronous update before refreshing the page.
                await new Promise<void>(resolve => {
                    setTimeout(resolve, 250);
                });
                await this.$.refreshNavigationPanel();
            }
        },
    })
    createMrpCalculationRequest: ui.PageAction;

    @ui.decorators.section<MrpCalculation>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<MrpCalculation>({
        isOpen: true,
        isTitleHidden: true,
        title: 'Results',
    })
    linesSection: ui.containers.Section;

    @ui.decorators.block<MrpCalculation>({
        parent() {
            return this.mainSection;
        },
        title: 'Criteria',
        isTitleHidden: true,
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Description',
        width: 'medium',
        isReadOnly: true,
    })
    description: ui.fields.Text;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        width: 'medium',
        isHidden: true,
    })
    companies: ui.fields.Text;

    @ui.decorators.multiReferenceField<MrpCalculation, Company>({
        isTransient: true,
        title: 'Companies',
        node: '@sage/xtrem-system/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        width: 'medium',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        columns: [
            ui.nestedFields.label({ title: 'ID', bind: 'id', isTitleHidden: true }),
            ui.nestedFields.label({ title: 'Name', bind: 'name', isTitleHidden: true }),
        ],
    })
    multiRefCompanies: ui.fields.MultiReference<Company>;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        isHidden: true,
    })
    sites: ui.fields.Text;

    @ui.decorators.multiReferenceField<MrpCalculation, Site>({
        isTransient: true,
        title: 'Sites',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        width: 'medium',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        columns: [
            ui.nestedFields.label({ title: 'ID', bind: 'id', isTitleHidden: true }),
            ui.nestedFields.label({ title: 'Name', bind: 'name', isTitleHidden: true }),
        ],
    })
    multiRefSites: ui.fields.MultiReference<Site>;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        width: 'medium',
        isReadOnly: true,
        isTransient: true,
    })
    fromItemText: ui.fields.Text;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        width: 'medium',
        isHidden: true,
    })
    fromItem: ui.fields.Text;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        width: 'medium',
        isReadOnly: true,
        isTransient: true,
    })
    toItemText: ui.fields.Text;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        width: 'medium',
        isHidden: true,
    })
    toItem: ui.fields.Text;

    @ui.decorators.dateField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date',
        width: 'small',
        isReadOnly: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.numericField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Period in weeks',
        isReadOnly: true,
        width: 'small',
    })
    numberOfWeeks: ui.fields.Numeric;

    @ui.decorators.checkboxField<MrpCalculation>({
        title: 'Explode bill of material',
        parent() {
            return this.criteriaBlock;
        },
        isReadOnly: true,
        width: 'small',
    })
    explodeBillOfMaterial: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MrpCalculation>({
        title: 'Include sales quotes',
        parent() {
            return this.criteriaBlock;
        },
        isReadOnly: true,
        width: 'small',
    })
    isSalesQuoteIncluded: ui.fields.Checkbox;

    @ui.decorators.numericField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Lowest BOM level',
        isReadOnly: true,
        width: 'small',
        isHidden() {
            return !this.explodeBillOfMaterial.value;
        },
    })
    lowLevelCode: ui.fields.Numeric;

    @ui.decorators.dateField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Calculation date',
        isReadOnly: true,
        width: 'small',
    })
    calculationDate: ui.fields.Date;

    @ui.decorators.referenceField<MrpCalculation, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Calculation user',
        bind: 'user',
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select user',
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First Name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'lastName' }),
            ui.nestedFields.text({ bind: 'email', title: 'Email' }),
        ],
        isReadOnly: true,
        width: 'small',
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.labelField<MrpCalculation>({
        title: 'Status',
        optionType: '@sage/xtrem-mrp-data/CalculationStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('CalculationStatus', this.calculationStatus.value);
        },
    })
    calculationStatus: ui.fields.Label<CalculationStatus>;

    @ui.decorators.separatorField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.textField<MrpCalculation>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Error message',
        width: 'large',
        isReadOnly: true,
        isHidden() {
            return this.calculationStatus.value !== 'error';
        },
    })
    errorMessage: ui.fields.Text;

    @ui.decorators.tableField<MrpCalculation, MrpResultLineBinding>({
        parent() {
            return this.linesSection;
        },
        title: 'Details',
        node: '@sage/xtrem-mrp-data/MrpResultLine',
        canSelect: false,
        isHelperTextHidden: true,
        bind: 'resultLines',
        canAddNewLine: true,
        isPhantomRowDisabled: true,
        canExport: true,
        orderBy: {
            startDate: +1,
        },
        columns: [
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding>({ bind: '_sortValue' }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding>({ bind: 'site' }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding>({ bind: 'company' }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding>({ bind: 'item' }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Company name',
                bind: { company: { name: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Company ID',
                bind: { company: { id: true } },
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Site name',
                bind: { site: { name: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Site ID',
                bind: { site: { id: true } },
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.link<MrpCalculation, MrpResultLineBinding>({
                isTransient: true,
                title: 'Item',
                bind: { item: { name: true } },
                canFilter: false,
                async onClick(_id: string, rowData) {
                    const workLine = extractEdges(
                        await this.$.graph
                            .node('@sage/xtrem-mrp-data/MrpWorkLine')
                            .query(
                                ui.queryUtils.edgesSelector(
                                    {
                                        item: true,
                                        site: true,
                                        safetyStock: true,
                                        batchQuantity: true,
                                        economicOrderQuantity: true,
                                        leadTime: true,
                                    },
                                    {
                                        filter: {
                                            _and: [
                                                { mrpCalculation: this.$.recordId },
                                                { item: { id: rowData.referenceItem?.id } },
                                                { site: { id: rowData.referenceSite?.id } },
                                            ],
                                        },
                                    },
                                ),
                            )
                            .execute(),
                    )[0] as ExtractEdges<MrpWorkLine>;

                    await this.$.dialog.page(
                        '@sage/xtrem-stock/MrpProjectedStockDialog',
                        {
                            item: JSON.stringify(rowData.referenceItem),
                            site: JSON.stringify(rowData.referenceSite),
                            startDate: JSON.stringify(this.startDate.value),
                            numberOfWeeks: this.numberOfWeeks.value as number,
                            safetyStock: +workLine.safetyStock,
                            decimalDigits: rowData.stockUnit?.decimalDigits as number,
                            stockUnit: rowData.stockUnit?.symbol as string,
                            mrpCalculationId: JSON.stringify(this.$.recordId),
                            preferredProcess: rowData.preferredProcess as string,
                            batchQuantity: +workLine.batchQuantity,
                            economicOrderQuantity: +workLine.economicOrderQuantity,
                            leadTime: workLine.leadTime,
                        },
                        {
                            resolveOnCancel: true,
                            rightAligned: false,
                            size: 'extra-large',
                        },
                    );
                },
            }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Item ID',
                bind: { referenceItem: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text<MrpCalculation, MrpResultLineBinding>({
                title: 'Item description',
                bind: { referenceItem: { description: true } },
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.date<MrpCalculation, MrpResultLineBinding>({
                title: 'First suggestion date',
                bind: { mrpCalculation: { startDate: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.numeric<MrpCalculation, MrpResultLineBinding>({
                title: 'Quantity',
                bind: 'quantity',
                scale(_val, rowData) {
                    return rowData?.stockUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData?.stockUnit?.symbol;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding, Item>({
                node: '@sage/xtrem-master-data/Item',
                bind: 'referenceItem',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                    ui.nestedFields.technical({ bind: 'isBought' }),
                    ui.nestedFields.technical({ bind: { stockUnit: { name: true } } }),
                    ui.nestedFields.technical({ bind: { purchaseUnit: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'isPhantom' }),
                ],
            }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding, Site>({
                node: '@sage/xtrem-system/Site',
                bind: 'referenceSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({
                                bind: 'currency',
                                node: '@sage/xtrem-master-data/Currency',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<MrpCalculation, MrpResultLineBinding, ItemSite>({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'referenceItemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { item: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'purchaseLeadTime' }),
                ],
            }),
            ui.nestedFields.date<MrpCalculation, MrpResultLineBinding>({
                title: 'Start date',
                bind: 'startDate',
                isReadOnly: true,
            }),
            ui.nestedFields.date<MrpCalculation, MrpResultLineBinding>({
                title: 'End date',
                bind: 'endDate',
                isReadOnly: true,
            }),
            ui.nestedFields.select<MrpCalculation, MrpResultLineBinding>({
                title: 'Replenishment method',
                bind: 'preferredProcess',
                isReadOnly: true,
                optionType: '@sage/xtrem-master-data/PreferredProcess',
            }),
            ui.nestedFields.label<MrpCalculation, MrpResultLineBinding>({
                title: 'Suggestion status',
                bind: 'suggestionStatus',
                optionType: '@sage/xtrem-mrp-data/SuggestionStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('SuggestionStatus', rowData.suggestionStatus),
            }),
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Create purchase order',
                isDisabled(_rowId, rowItem) {
                    return rowItem.suggestionStatus === 'ordered' || this.$.isDirty;
                },
                isHidden(_rowId, rowItem) {
                    return !(rowItem.item && rowItem.referenceItem?.isBought);
                },
                async onClick(rowId: string, rowData: ui.PartialNodeWithId<MrpResultLineBinding>) {
                    // TODO: POs do let you have an order date later than today. Re-instate if that changes.
                    // let startDate = date.parse(rowData.day).addDays(-rowData.itemSite.purchaseLeadTime);
                    // if (startDate.daysDiff(date.today()) < 0) {
                    //     startDate = date.today();
                    // }
                    const newPurchaseOrder = await this.$.dialog.page(
                        '@sage/xtrem-purchasing/ReorderPurchaseOrderPanel',
                        {
                            recommendation: JSON.stringify({
                                item: rowData.referenceItem,
                                site: rowData.referenceSite,
                                quantity: rowData.quantity,
                                startDate: date.today(),
                                endDate: rowData.endDate,
                            }),
                        },
                        {
                            rightAligned: true,
                            size: 'large',
                            resolveOnCancel: true,
                        },
                    );
                    // When clicking X button it returns an empty object
                    if (newPurchaseOrder && JSON.stringify(newPurchaseOrder) !== '{}') {
                        await this.updateResultLineSuggestionStatus(rowId);
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock/pages__mrp_calculation__purchase_order_creation__success',
                                'Purchase order {{num}} created.',
                                { num: newPurchaseOrder.number },
                            ),
                            { timeout: 0, type: 'success' },
                        );
                    }
                },
            },
            {
                icon: 'add',
                title: 'Create work order',
                isDisabled(_rowId, rowItem) {
                    return rowItem.suggestionStatus === 'ordered' || this.$.isDirty;
                },
                isHidden(_rowId, rowItem) {
                    return !(rowItem.item && rowItem.referenceItem?.isManufactured);
                },
                async onClick(rowId: string, rowData: ui.PartialNodeWithId<MrpResultLineBinding>) {
                    let startDate = date
                        .parse(rowData.endDate ?? '')
                        .addDays(-(rowData?.referenceItemSite?.prodLeadTime ?? 0));
                    if (startDate.daysDiff(date.today()) < 0) {
                        startDate = date.today();
                    }
                    const newWorkOrder = await this.$.dialog.page(
                        '@sage/xtrem-manufacturing/WorkOrderPanel',
                        {
                            recommendation: JSON.stringify({
                                item: {
                                    name: rowData.referenceItem?.name,
                                    id: rowData.referenceItem!.id,
                                    item: rowData.referenceItem,
                                },
                                site: rowData.referenceSite,
                                quantity: rowData.quantity,
                                startDate: startDate.toString(),
                                endDate: rowData.endDate,
                            } as unknown as WorkOrderPanelParameters),
                        },
                        {
                            rightAligned: true,
                            size: 'large',
                            resolveOnCancel: true,
                        },
                    );
                    // When clicking X button it returns an empty object
                    if (newWorkOrder && JSON.stringify(newWorkOrder) !== '{}') {
                        await this.updateResultLineSuggestionStatus(rowId);
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-stock/pages__mrp_calculation__work_order_creation__success',
                                'Work order {{num}} created.',
                                { num: newWorkOrder.number },
                            ),
                            { timeout: 0, type: 'success' },
                        );
                    }
                },
            },
        ],
    })
    resultLines: ui.fields.Table<MrpResultLineBinding>;

    async updateResultLineSuggestionStatus(rowId: string) {
        await this.$.graph
            .node('@sage/xtrem-mrp-data/MrpCalculation')
            .mutations.updateResultLineSuggestionStatus(true, {
                calculation: this.$.recordId ?? '',
                resultLineId: rowId,
                suggestionStatus: 'ordered',
            })
            .execute();
        await this.resultLines.refreshRecord(rowId);
    }
}
