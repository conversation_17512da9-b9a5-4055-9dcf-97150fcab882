import { DateValue } from '@sage/xtrem-date-time';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { Currency, Item, Location, ReasonCode, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stock } from '@sage/xtrem-master-data/build/lib/menu-items/stock';
import type {
    GraphApi,
    StockIssueDisplayStatus,
    StockIssueLine,
    StockIssueLineBinding,
    StockIssue as StockIssueNode,
} from '@sage/xtrem-stock-api';
import type { Lot, StockDetailStatus, StockDocumentTransactionStatus, StockStatus } from '@sage/xtrem-stock-data-api';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { getSite } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-stock-receipt';
import type * as StockInterfaces from '../client-functions/interfaces';
import * as actionFunctions from '../client-functions/stock-issue-action-functions';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockIssue, StockIssueNode>({
    isTransient: false,
    menuItem: stock,
    priority: 200,
    title: 'Stock issue',
    objectTypeSingular: 'Stock issue',
    objectTypePlural: 'Stock issues',
    idField() {
        return this.number;
    },
    hasAttachmentsSection: true,
    mode: 'tabs',
    node: '@sage/xtrem-stock/StockIssue',
    headerLabel() {
        return this.displayStatus;
    },
    headerSection() {
        return this.mainSection;
    },
    navigationPanel: {
        orderBy: { effectiveDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({ bind: 'number', title: 'Number', isMandatory: true }),
            line2: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date', isMandatory: true }),
            reasonCode: ui.nestedFields.reference({
                bind: 'reasonCode',
                valueField: 'name',
                node: '@sage/xtrem-master-data/ReasonCode',
                title: 'Reason',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.label({
                title: 'Status',
                optionType: '@sage/xtrem-stock/StockIssueDisplayStatus',
                bind: 'displayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            description: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            isSetDimensionsMainListHidden: ui.nestedFields.technical({ bind: 'isSetDimensionsMainListHidden' }),
        },
        dropdownActions: [
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockIssueNode>) {
                    const site = await getSite({
                        stockPage: this,
                        siteId: rowItem.stockSite?._id ?? '',
                    });
                    await actionFunctions.setDimensions({
                        stockIssuePage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        site,
                    });
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<StockIssueNode>) {
                    return rowItem.isSetDimensionsMainListHidden || false;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<StockIssueNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-stock/StockIssue',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockIssueNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { stockTransactionStatus: rowItem.stockTransactionStatus },
                        recordId,
                    });
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _ne: 'issued' } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Details required', graphQLFilter: { displayStatus: { _eq: 'detailsRequired' } } },
            { title: 'Details entered', graphQLFilter: { displayStatus: { _eq: 'detailsEntered' } } },
            { title: 'Stock posting in progress', graphQLFilter: { displayStatus: { _eq: 'stockPostingInProgress' } } },
            { title: 'Stock posting error', graphQLFilter: { displayStatus: { _eq: 'stockPostingError' } } },
            { title: 'Issued', graphQLFilter: { displayStatus: { _eq: 'issued' } } },
        ],
    },
    onLoad() {
        if (this._id.value) {
            this.issueStepSequence.statuses = actionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockIssueDisplayStatus,
            );
        }
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message || '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        return this.init();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this._id.value) {
            this.issueStepSequence.statuses = actionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockIssueDisplayStatus,
            );
        }
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveStockIssue,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.defaultDimension,
            ],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.repost, this.post, this.saveStockIssue];
    },
})
export class StockIssue
    extends ui.Page<GraphApi, StockIssueNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.section<StockIssue>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockIssue>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<StockIssue>({
        title: 'Lines',
    })
    linesSection: ui.containers.Section;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockIssue>({
        title: 'Posting',
        isHidden() {
            return !(this.stockSite.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockIssue>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.textField<StockIssue>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.stepSequenceField<StockIssue>({
        parent() {
            return this.mainBlock;
        },
        options: actionFunctions.issueStepSequenceOptions,
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    issueStepSequence: ui.fields.StepSequence;

    @ui.decorators.textField<StockIssue>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly: true,
        maxLength: 24,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockIssue, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.reference<StockIssue, Site, Currency>({
                bind: 'financialCurrency',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({
                        bind: 'symbol',
                    }),
                    ui.nestedFields.numeric({
                        bind: 'decimalDigits',
                    }),
                    ui.nestedFields.checkbox({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.reference<StockIssue, Site, Company>({
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                isHidden: true,
                columns: [
                    ui.nestedFields.checkbox({
                        bind: 'doStockPosting',
                    }),
                ],
            }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        placeholder: 'Select site',
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            if (this.stockSite.value?.isLocationManaged) {
                this.lines.showColumn('location');
            } else {
                this.lines.hideColumn('location');
            }
            await this.lines.redraw();
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.stockSite.value,
            });
        },
    })
    stockSite: ui.fields.Reference;

    @ui.decorators.dateField<StockIssue>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<StockIssue, ReasonCode>({
        parent() {
            return this.mainBlock;
        },
        title: 'Reason',
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        fetchesDefaults: true,
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    reasonCode: ui.fields.Reference<ReasonCode>;

    @ui.decorators.textField<StockIssue>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    description: ui.fields.Text;

    @ui.decorators.labelField<StockIssue>({
        title: 'Display status',
        optionType: '@sage/xtrem-stock/StockIssueDisplayStatus',
        style() {
            return StockPillColor.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<StockIssueDisplayStatus>;

    @ui.decorators.labelField<StockIssue>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        style() {
            return StockPillColor.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<StockIssue, StockInterfaces.StockIssueLinePageBinding>({
        title: 'Lines',
        bind: 'lines',
        canSelect: false,
        node: '@sage/xtrem-stock/StockIssueLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.linesSection;
        },
        columns: [
            ui.nestedFields.technical<StockIssue, StockInterfaces.StockIssueLinePageBinding>({
                bind: 'stockTransactionStatus',
            }),
            ui.nestedFields.label<StockIssue, StockInterfaces.StockIssueLinePageBinding>({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-stock-data/StockIssueDisplayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.reference<StockIssue, StockInterfaces.StockIssueLinePageBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                valueField: 'name',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                width: 'medium',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical<StockIssue, Item, UnitOfMeasure>({ bind: { stockUnit: { _id: true } } }),
                    ui.nestedFields.technical<StockIssue, Item, UnitOfMeasure>({
                        bind: { stockUnit: { name: true } },
                    }),
                    ui.nestedFields.technical<StockIssue, Item, UnitOfMeasure>({
                        bind: { stockUnit: { symbol: true } },
                    }),
                    ui.nestedFields.technical<StockIssue, Item, UnitOfMeasure>({ bind: { stockUnit: { id: true } } }),
                    ui.nestedFields.technical<StockIssue, Item, UnitOfMeasure>({
                        bind: { stockUnit: { decimalDigits: true } },
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
                ],
                isDisabled(rowId: any, rowData: ui.PartialCollectionValue<StockIssueLineBinding> | undefined) {
                    return !rowData || Number(rowData?._id) > 0 || Number(rowData?.jsonStockDetails?.length) > 0;
                },
                filter() {
                    return {
                        itemSites: { _atLeast: 1, site: { _id: this.stockSite.value?._id } },
                    };
                },
                orderBy: {
                    name: +1,
                    id: +1,
                },
                async onChange(_id: number, rowData: ui.PartialNodeWithId<StockIssueLine>) {
                    if (rowData.item?._id && this.stockSite.value) {
                        const lineQuantity = rowData.quantityInStockUnit ? +rowData.quantityInStockUnit : 0;
                        const [{ valuationMethod }] = await StockDataUtils.getItemSiteValuationMethod(this, {
                            item: rowData.item._id,
                            site: this.stockSite.value._id,
                        });

                        if (valuationMethod !== 'fifoCost' || (valuationMethod === 'fifoCost' && lineQuantity > 0)) {
                            this.lines.setRecordValue({
                                _id: rowData._id,
                                ...(await StockDataUtils.getLineCost(this, {
                                    item: rowData.item?._id,
                                    site: this.stockSite.value?._id,
                                    dateOfValuation: this.effectiveDate.value,
                                    quantity: lineQuantity,
                                })),
                            });
                        }

                        const { storedAttributes, storedDimensions } =
                            await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                page: this,
                                _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                dimensionDefinitionLevel: 'stockDirect',
                                site: this.stockSite.value,
                                item: rowData.item,
                            });

                        this.lines.setRecordValue({
                            _id: rowData._id,
                            storedAttributes,
                            storedDimensions,
                        });
                    }
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                isReadOnly(_rowId, rowData) {
                    return StockIssue.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                scale(_val, rowData) {
                    return rowData?.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
                isMandatory: true,
                min: 0,
                async onChange(_id: number, rowData: ui.PartialNodeWithId<StockIssueLine>) {
                    if (rowData.stockDetailStatus === 'entered') {
                        rowData.stockDetailStatus = 'required';
                    }
                    if (rowData.item?._id && this.stockSite.value) {
                        const [{ valuationMethod }] = await StockDataUtils.getItemSiteValuationMethod(this, {
                            item: rowData.item._id,
                            site: this.stockSite.value._id,
                        });

                        if (valuationMethod !== 'fifoCost') {
                            this.lines.addOrUpdateRecordValue(rowData);
                        } else if (rowData.quantityInStockUnit && +rowData.quantityInStockUnit > 0) {
                            this.lines.setRecordValue({
                                _id: rowData._id,
                                stockDetailStatus: rowData.stockDetailStatus,
                                ...(await StockDataUtils.getLineCost(this, {
                                    item: rowData.item?._id,
                                    site: this.stockSite.value?._id,
                                    dateOfValuation: this.effectiveDate.value,
                                    quantity: +rowData.quantityInStockUnit,
                                })),
                            });
                        } else {
                            rowData.valuedCost = '0';
                            rowData.orderCost = '0';
                            this.lines.addOrUpdateRecordValue(rowData);
                        }
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Expected cost',
                bind: 'valuedCost',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
                scale() {
                    return this.costScale();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost',
                bind: 'orderCost',
                isReadOnly: true,
                prefix() {
                    return this.costSymbol();
                },
                scale() {
                    return this.costScale();
                },
                min: 0,
            }),
            ui.nestedFields.technical<StockIssue, StockInterfaces.StockIssueLinePageBinding>({
                bind: 'stockDetailStatus',
            }),
            ui.nestedFields.reference<StockIssue, StockInterfaces.StockIssueLinePageBinding, Location>({
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                bind: 'location',
                minLookupCharacters: 1,
                lookupDialogTitle: 'Select location',
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                    ui.nestedFields.reference<StockIssue, Location, Location['locationZone']>({
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: '_id',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.reference<
                                StockIssue,
                                Location['locationZone'],
                                Location['locationZone']['site']
                            >({
                                bind: 'site',
                                node: '@sage/xtrem-system/Site',
                                valueField: '_id',
                            }),
                        ],
                    }),
                ],
                filter() {
                    return {
                        locationZone: { site: { _id: { _eq: this.stockSite.value?._id } } },
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isReadOnly(_rowId, rowData) {
                    return StockIssue.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
            }),
            ui.nestedFields.reference<StockIssue, StockInterfaces.StockIssueLinePageBinding, StockStatus>({
                title: 'Quality control',
                bind: 'stockStatus',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select quality value',
                isReadOnly(_rowId, rowData) {
                    return StockIssue.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
            }),
            ui.nestedFields.reference<StockIssue, StockInterfaces.StockIssueLinePageBinding, Lot>({
                title: 'Lot',
                valueField: 'id',
                bind: 'lot',
                node: '@sage/xtrem-stock-data/Lot',
                placeholder: 'Select lot',
                width: 'medium',
                isReadOnly(rowId: any, rowData: any) {
                    return rowData.item.lotManagement === 'notManaged';
                },
                columns: [
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                    ui.nestedFields.reference<StockIssue, Lot, Item>({
                        title: 'Item',
                        valueField: 'name',
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        lookupDialogTitle: 'Select item',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                ],
                filter(rowData) {
                    return { item: { _id: { _eq: rowData.item._id } } };
                },
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: '_sortValue',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'computedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            ui.nestedFields.technical<StockIssue, StockInterfaces.StockIssueLinePageBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Stock details`,
                isHidden(rowId, rowItem) {
                    return !rowItem || !(rowItem.item && rowItem.quantityInStockUnit);
                },
                async onClick(
                    rowId: string,
                    rowItem: ui.PartialCollectionValue<StockInterfaces.StockIssueLinePageBinding>,
                ) {
                    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        StockDetailHelper.editStockDetails(this, line, {
                            movementType: 'issue',
                            data: {
                                isEditable: !StockIssue.lineStatusReadonly(line.stockTransactionStatus),
                                effectiveDate: this.effectiveDate.value || undefined,
                                documentLineSortValue: line._sortValue,
                                documentLine: rowId,
                                jsonStockDetails: line.jsonStockDetails,
                                item: line.item?._id,
                                stockSite: this.stockSite.value?._id,
                                quantity: +line.quantityInStockUnit,
                                number: this.number.value || undefined,
                                unit: line.item.stockUnit?._id,
                                stockStatus: line.stockStatus?._id,
                                location: line.location?._id,
                                lot: line.lot?._id,
                                orderCost: line.orderCost,
                                valuedCost: line.valuedCost,
                                searchCriteria: {
                                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                                    item: line.item?._id,
                                    site: this.stockSite.value?._id,
                                    stockUnit: line.item.stockUnit?._id,
                                    statusList: [],
                                    ignoreExpirationDate: true,
                                },
                            },
                        }) as Promise<StockInterfaces.StockIssueLinePageBinding>,
                    );
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(rowId: any, rowItem: ui.PartialCollectionValue<StockIssueLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                // this parameter as to be customized to you need.
                                editable:
                                    !StockIssue.lineStatusReadonly((rowData as any).stockTransactionStatus) ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowID, rowItem) {
                    return StockIssue.lineStatusReadonly(rowItem?.stockTransactionStatus);
                },
                onClick(rowID: any) {
                    this.lines.removeRecord(rowID);
                    this.disableHeaderFields();
                },
            },
        ],
        fieldActions() {
            return [this.addIssueLine];
        },
    })
    lines: ui.fields.Table<StockIssueLine>;

    @ui.decorators.tableField<StockIssue, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockIssue>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockIssue>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'miscellaneousStockIssue',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.checkboxField<StockIssue>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    isSetDimensionsMainListHidden: ui.fields.Checkbox;

    @ui.decorators.pageAction<StockIssue>({
        icon: 'none',
        title: 'Set dimensions',
        isHidden() {
            return this.isSetDimensionsMainListHidden.value === true;
        },
        isDisabled() {
            return !this.stockSite.value;
        },
        async onClick() {
            const filter = (line: ui.PartialNodeWithId<StockIssueLine>) =>
                !StockIssue.lineStatusReadonly(line.stockTransactionStatus);
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockIssue>({
        title: 'Save',
        buttonType: 'primary',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validation = await this.validate(true);

            if (!validation.length) {
                if (this.lines.value.length) {
                    StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(
                        this.lines.value,
                        this.lines,
                        'issue',
                    );
                }
                await this.$standardSaveAction.execute(true);
                await this.$.refreshNavigationPanel();
            }
        },
    })
    saveStockIssue: ui.PageAction;

    @ui.decorators.pageAction<StockIssue>({
        title() {
            if (this.displayStatus.value === 'stockPostingError') {
                return ui.localize('@sage/xtrem-stock/pages__stock_issue__stock_posting_error', 'Stock posting error');
            }
            return ui.localize('@sage/xtrem-stock/pages__stock_issue__stock_journal_inquiry', 'Stock journal inquiry');
        },
        isHidden() {
            return !['issued', 'stockPostingError'].includes(this.displayStatus.value || '');
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.lines,
                },
            );
        },
    })
    stockJournal: ui.PageAction;

    private async init() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockIssue,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
        if (!this.$.recordId) {
            this.effectiveDate.value = DateValue.today().toString();
        }
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.stockSite.value,
        });
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        await this.$.refreshNavigationPanel();
        this.$.setPageClean();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    setDeleteAction() {
        if (this.stockTransactionStatus.value) {
            this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
                parameters: { stockTransactionStatus: this.stockTransactionStatus.value },
                recordId: this.$.recordId,
            });
        }
    }

    @ui.decorators.pageAction<StockIssue>({
        icon: 'add',
        title: 'Add line',
        isDisabled() {
            return (
                !this.stockSite.value ||
                (this.stockTransactionStatus.value && this.stockTransactionStatus.value !== 'draft') ||
                false
            );
        },
        onClick() {
            const lineData = {
                ...dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                    { _sortValue: (this.lines.value[this.lines.value.length - 1]?._sortValue || 0) + 10 } as any,
                    this._defaultDimensionsAttributes,
                ),
                stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
                stockDetailStatus: 'required' as StockDetailStatus,
                displayStatus: 'detailsRequired' as StockIssueDisplayStatus,
            };
            this.lines.addRecord(lineData);
            this.disableHeaderFields();
        },
    })
    addIssueLine: ui.PageAction;

    @ui.decorators.pageAction<StockIssue>({
        title: 'Post stock',
        buttonType: 'primary',
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value || '') ||
                !this._id.value ||
                this.$.isDirty
            );
        },
        async onClick() {
            if (this.lines.value.some(line => line.stockDetailStatus === 'required')) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__stock_issue_post__stock_details_required',
                        'You need to enter stock details for all lines before you can post.',
                    ),
                    { type: 'error' },
                );
                return;
            }
            this.post.isDisabled = true;
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-stock/StockIssue')
                    .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                    .execute(),
                this,
            );
            if (this._id.value) {
                await actionFunctions.checkForUpdate({
                    stockIssuePage: this,
                    recordId: this._id.value,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockIssue>({
        title: 'Repost',
        isHidden() {
            return !(this.fromNotificationHistory && (this.stockSite?.value?.legalCompany?.doStockPosting || false));
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError((await this.issueRepost()).message, this);
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    async issueRepost() {
        const documentLines = this.lines.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        this.$.loader.isHidden = false;

        const postResult = await this.$.graph
            .node('@sage/xtrem-stock/StockIssue')
            .mutations.repost(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { stockIssue: this._id.value || '', documentLines },
            )
            .execute();

        this.$.loader.isHidden = true;
        if (!postResult.wasSuccessful) {
            this.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-stock/pages__stock_issue__repost_errors',
                    'Errors while reposting:',
                )}**\n${postResult.message}`,
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    /**
     * Validates the page (including mandatory stock fields)
     * @returns string array (promise of) with all the error messages
     */
    validate(notify = false): Promise<string[]> | string[] {
        // check if the serial numbers on all new (or updated) lines are unique
        if (this.$.isServiceOptionEnabled('serialNumberOption')) {
            const validationMessages = StockValuationLib.validateSerialNumbers(this);
            if (validationMessages && !!validationMessages.length) {
                if (notify) {
                    this.$.showToast(validationMessages.join('\n\n'), { type: 'error' });
                }
                return validationMessages;
            }
        }
        return MasterDataUtils.runCustomValidation(
            this,
            async check => {
                // TODO: remove quantity, status and location check when isMandatory on tables is working
                await this.$.page.validate();
                this.lines.value.forEach((issueTableLine: any) => {
                    if (!issueTableLine.stockDetails?.length) {
                        check(
                            issueTableLine.quantityInStockUnit,
                            ui.localize(
                                '@sage/xtrem-stock/pages__stock_issue__notification__The_quantity_is_mandatory',
                                'The quantity is mandatory.',
                            ),
                        );

                        // Disabling this for now as we don't need it.
                        // check(
                        //     issueTableLine.stockStatus,
                        //     ui.localize(
                        //         '@sage/xtrem-stock/pages__stock_issue__notification__The_stock_status_is_mandatory',
                        //         'The stock status is mandatory.',
                        //     ),
                        // );
                        //
                        // if (this.stockSite.value.isLocationManaged) {
                        //     check(
                        //         issueTableLine.location,
                        //         ui.localize(
                        //             '@sage/xtrem-stock/pages__stock_issue__notification__The_location_is_mandatory',
                        //             'The location is mandatory.',
                        //         ),
                        //     );
                        // }
                        //
                        // if (issueTableLine.item.lotManagement !== 'notManaged') {
                        //     check(
                        //         issueTableLine.lot,
                        //         ui.localize(
                        //             '@sage/xtrem-stock/pages__stock_issue__notification__The_lot_is_mandatory',
                        //             'The lot is mandatory.',
                        //         ),
                        //     );
                        //     if (issueTableLine.item.lotManagement === 'lotSublotManagement') {
                        //         check(
                        //             issueTableLine.sublot,
                        //             ui.localize(
                        //                 '@sage/xtrem-stock/pages__stock_issue__notification__The_sublot_is_mandatory',
                        //                 'The sublot is mandatory.',
                        //             ),
                        //         );
                        //     }
                        // }
                    }
                });
            },
            notify,
        );
    }

    private static lineStatusReadonly(stockTransactionStatus: StockDocumentTransactionStatus) {
        return ['inProgress', 'completed'].includes(stockTransactionStatus);
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    disableHeaderFields() {
        // header fields (including stockSite) are disabled for an existing loaded record
        // so here just manage case where we are in creation of a new record
        if (!this.$.recordId) {
            this.stockSite.isDisabled = this.lines.value.length > 0;
        }
    }

    costScale() {
        return this.stockSite.value?.financialCurrency.decimalDigits ?? 2;
    }

    costSymbol() {
        return this.stockSite.value?.financialCurrency.symbol || '';
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
