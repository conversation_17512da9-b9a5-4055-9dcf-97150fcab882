import type { BatchContext } from '@sage/xtrem-communication';
import type { Context, NodePayloadData } from '@sage/xtrem-core';
import { Test, TextStream, datetime } from '@sage/xtrem-core';
import * as xtremImportExport from '@sage/xtrem-import-export';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as fs from 'fs';
import * as _ from 'lodash';
import type { Readable } from 'stream';
import * as xtremStock from '../../../lib';

type KeyOf<T extends object> = Extract<keyof T, string>;
function omitProperties<T extends object, K extends Extract<keyof T, string>>(
    obj: T,
    excludedKeys: Array<KeyOf<T>>,
): Omit<T, K> {
    if (excludedKeys.length === 0) return obj;
    const pickedProperties = Object.keys(obj).filter(key => !excludedKeys?.includes(key as KeyOf<T>));
    return _.pick(obj, pickedProperties) as Omit<T, K>;
}

async function importCsv(
    context: Context,
    batchContext: BatchContext,
    stream: Readable,
    importResult: xtremImportExport.nodes.ImportResult,
): Promise<xtremImportExport.nodes.ImportResult> {
    const { stringifyCsv } = xtremImportExport.functions;
    const importResultId = importResult._id;
    const { headers, failedRows, rowsProcessed, status, generalError, dryRun } =
        await xtremImportExport.functions.parseCsvStreamTransform(stream, context, batchContext, importResult);
    return context
        .runInWritableContext(async writableContext => {
            const importResultEnd = (await writableContext.tryRead(
                xtremImportExport.nodes.ImportResult,
                {
                    _id: importResultId,
                },
                { forUpdate: true },
            )) as xtremImportExport.nodes.ImportResult;

            if (importResultEnd) {
                const endStamp = datetime.now(true);
                const failedRowsArray = failedRows.map(row => Object.values(row) as string[]);
                await importResultEnd.$.set({
                    numberOfRowsInError: failedRows.filter(row => row._error !== '').length,
                    rowsProcessed,
                    endStamp,
                    rowsInError: new TextStream(stringifyCsv([headers, ...failedRowsArray], ';'), 'text/csv'),
                    status,
                    generalError,
                    dryRun,
                });
                await importResultEnd.$.save();
                return importResultEnd;
            }
            return null;
        })
        .then(res => res || importResult);
}

async function getLotJson(
    lot: xtremStockData.nodes.Lot | null,
): Promise<NodePayloadData<xtremStockData.nodes.Lot> | null> {
    if (!lot) {
        return null;
    }

    return {
        id: await lot.id,
        sublot: await lot.sublot,
    };
}

async function getItemJson(item: xtremMasterData.nodes.Item): Promise<NodePayloadData<xtremMasterData.nodes.Item>> {
    return {
        id: await item.id,
    };
}

async function getStockStatusJson(
    stockStatus: xtremStockData.nodes.StockStatus | null,
): Promise<NodePayloadData<xtremStockData.nodes.StockStatus> | null> {
    if (!stockStatus) return null;

    return {
        id: await stockStatus?.id,
    };
}

async function getReasonCodeJson(
    reasonCode: xtremMasterData.nodes.ReasonCode,
): Promise<NodePayloadData<xtremMasterData.nodes.ReasonCode>> {
    return {
        id: await reasonCode.id,
    };
}

async function getLocationJson(
    location: xtremMasterData.nodes.Location | null,
): Promise<NodePayloadData<xtremMasterData.nodes.Location> | null> {
    if (!location) {
        return null;
    }
    return { id: await location.id };
}

async function getSiteJson(site: xtremSystem.nodes.Site): Promise<NodePayloadData<xtremSystem.nodes.Site>> {
    return { id: await site.id };
}

async function getStockDetailLotJson(
    stockDetailLot: xtremStockData.nodes.StockDetailLot | null,
): Promise<NodePayloadData<xtremStockData.nodes.StockDetailLot> | null> {
    if (!stockDetailLot) {
        return null;
    }

    return {
        lot: await getLotJson(await stockDetailLot.lot),
        lotNumber: await stockDetailLot.lotNumber,
        sublot: await stockDetailLot.sublot,
        expirationDate: await stockDetailLot.expirationDate,
    };
}

async function getStockAdjustmentDetailJson(
    stockDetail: xtremStockData.nodes.StockAdjustmentDetail | null,
): Promise<NodePayloadData<xtremStockData.nodes.StockAdjustmentDetail> | null> {
    if (!stockDetail) {
        return null;
    }

    return {
        status: (await getStockStatusJson(await stockDetail.status)) ?? undefined,
        quantityInStockUnit: await stockDetail.quantityInStockUnit,
        stockDetailLot: await getStockDetailLotJson(await stockDetail.stockDetailLot),
        reasonCode: await getReasonCodeJson(await stockDetail.reasonCode),
    };
}

function getSerialNumbers(
    stockCountLine: xtremStock.nodes.StockCountLine,
): Promise<NodePayloadData<xtremStock.nodes.StockCountLineSerialNumber>[]> {
    return stockCountLine.stockCountLineSerialNumbers
        .map(async sn => {
            return {
                serialNumber: { id: await (await sn.serialNumber).id },
                isCounted: await sn.isCounted,
            };
        })
        .toArray();
}

async function getStockCountLineJson(
    stockCountLine: xtremStock.nodes.StockCountLine,
): Promise<NodePayloadData<xtremStock.nodes.StockCountLine>> {
    return {
        _sortValue: await stockCountLine._sortValue,
        stockTransactionStatus: await stockCountLine.stockTransactionStatus,
        status: await stockCountLine.status,
        item: await getItemJson(await stockCountLine.item),
        location: await getLocationJson(await stockCountLine.location),
        lot: await getLotJson(await stockCountLine.lot),
        stockStatus: await getStockStatusJson(await stockCountLine.stockStatus),
        owner: await stockCountLine.owner,
        countedQuantityInStockUnit: await stockCountLine.countedQuantityInStockUnit,
        quantityInStockUnit: await stockCountLine.quantityInStockUnit,
        quantityVariance: await stockCountLine.quantityVariance,
        isAddedDuringCount: await stockCountLine.isAddedDuringCount,
        stockDetail: await getStockAdjustmentDetailJson(await stockCountLine.stockDetail),
        ...((await stockCountLine.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption))
            ? { stockCountLineSerialNumbers: await getSerialNumbers(stockCountLine) }
            : {}),
    };
}

async function getStockCountJson(
    stockCount: xtremStock.nodes.StockCount,
    excludedProperties?: KeyOf<NodePayloadData<xtremStock.nodes.StockCount>>[],
): Promise<NodePayloadData<xtremStock.nodes.StockCount>> {
    const fromItem = await stockCount.fromItem;
    const toItem = await stockCount.toItem;
    const result: NodePayloadData<xtremStock.nodes.StockCount> = {
        number: await stockCount.number,
        counter: await stockCount.counter,
        description: await stockCount.description,
        stockSite: await getSiteJson(await stockCount.stockSite),
        status: await stockCount.status,
        effectiveDate: await stockCount.effectiveDate,
        financeIntegrationStatus: await stockCount.financeIntegrationStatus,
        lastCountDate: await stockCount.lastCountDate,
        fromItem: fromItem ? await getItemJson(fromItem) : null,
        toItem: toItem ? await getItemJson(toItem) : null,
        lines: await stockCount.lines.map(line => getStockCountLineJson(line)).toArray(),
    };

    return omitProperties(result, excludedProperties ?? []);
}

function getStringValue(value: any) {
    if (value === null) return null;
    const result = value.toString();
    if (result === '[object Object]') return JSON.stringify(value);

    return result;
}

function assertObjects(actualObj: any, expectedObj: any, path: string) {
    const actualKeys = Object.keys(actualObj);
    const expectedKeys = Object.keys(expectedObj);
    assert.deepEqual(
        actualKeys.length,
        expectedKeys.length,
        `${path}: actual properties=${actualKeys}, expected properties=${expectedKeys}`,
    );
    expectedKeys.forEach(key => {
        if (Array.isArray(expectedObj[key]) && Array.isArray(actualObj[key])) {
            assert.deepEqual(actualObj[key].length, expectedObj[key].length, `${path}.${key}.length`);
            (expectedObj[key] as any).forEach((expectedItem: any, index: integer) => {
                assertObjects(actualObj[key][index], expectedItem, `${path}.${key}[${index}]`);
            });
        } else if (
            typeof expectedObj[key] === 'object' &&
            typeof actualObj[key] === 'object' &&
            !!expectedObj[key] &&
            !!actualObj[key]
        ) {
            assertObjects(actualObj[key], expectedObj[key], `${path}.${key}`);
        } else {
            // To manage the decimals correctly -> stringify the values
            // if the value is an object, stringify it
            const expectedValue = getStringValue(expectedObj[key]);
            const actualValue = getStringValue(actualObj[key]);

            assert.deepEqual(actualValue, expectedValue, `${path}.${key}`);
        }
    });
}

async function uploadAndImport(
    context: Context,
    csvPath: string,
    options: {
        doInsert?: boolean;
        doUpdate?: boolean;
        maxErrorCount?: number;
        importExportTemplate: xtremImportExport.nodes.ImportExportTemplate;
    },
): Promise<xtremImportExport.nodes.ImportResult> {
    const user = await context.user;
    const stream = fs.createReadStream(csvPath, 'utf8');
    const uploadedFile = await xtremImportExport.uploadTestFile(csvPath, user?.email);
    const importResult = await xtremImportExport.nodes.ImportExportTemplate.createResult(
        context,
        options.importExportTemplate,
        true,
        !!options.doUpdate,
        false,
        options.maxErrorCount || 1,
        'inProgress',
        String(uploadedFile.uploadedFileId),
    );
    return importCsv(context, context.batch, stream, importResult);
}

function parseCsv(csv: string): string[][] {
    return csv.split('\n').map(line => line.split(';').map(cell => cell.replaceAll('"', '').trim()));
}

async function testErrorResult(importResult: xtremImportExport.nodes.ImportResult, expectedErrorMessage: string) {
    assert.equal(await importResult.numberOfRowsInError, 1);
    const results = parseCsv((await importResult.rowsInError).value);

    assert.deepEqual(results[0].at(-1), '_error');
    assert.deepEqual(results[1].at(-1), expectedErrorMessage);
}

/**
 *  Test the import of a stock count:
 * - check data before import if a file xxx-check-start.json exists
 * - import the file xxx.csv
 * - check data after import with a file named xxx.json
 * @param context
 * @param args
 * @param args.fileName the name of the csv file to import
 * @param args.templateId the id of the import template to use
 * @param args.stockCountNumber the number of the stock count to import the data into
 * @param args.error the expected error message if any
 * @returns the stock count if no error is expected, null otherwise
 */
async function testImport(
    context: Context,
    args: {
        fileName: string;
        templateId: string;
        stockCountNumber: string;
        numberOfNewStockCount?: integer;
        checkCsv?: (rows: string[][]) => void;
    },
): Promise<xtremStock.nodes.StockCount[]>;
async function testImport(
    context: Context,
    args: {
        fileName: string;
        templateId: string;
        stockCountNumber: string;
        error: string;
        checkCsv?: (rows: string[][]) => void;
    },
): Promise<null>;
async function testImport(
    context: Context,
    args: {
        fileName: string;
        templateId: string;
        stockCountNumber: string;
        numberOfNewStockCount?: integer;
        error?: string;
        checkCsv?: (rows: string[][]) => void;
    },
): Promise<xtremStock.nodes.StockCount[] | null> {
    const csvPath = `./test/fixtures/csv/${args.fileName}.csv`;
    const resultPath = `./test/fixtures/csv/${args.fileName}.json`;
    const checkStartPath = `./test/fixtures/csv/${args.fileName}-check-start.json`;

    if (args.checkCsv) {
        const rows = parseCsv(fs.readFileSync(csvPath, 'utf8'));
        args.checkCsv(rows);
    }
    if (fs.existsSync(checkStartPath)) {
        const stockCount = await context.read(xtremStock.nodes.StockCount, { _id: `#${args.stockCountNumber}` });
        const expected = JSON.parse(fs.readFileSync(checkStartPath, 'utf8'));
        assertObjects(await getStockCountJson(stockCount), expected, 'stockCount');
    }

    const existingStockCountNumbers = (
        await context.select(xtremStock.nodes.StockCount, { number: true }, { filter: {} })
    ).map(stockCount => stockCount.number);

    const importExportTemplate = await context.read(xtremImportExport.nodes.ImportExportTemplate, {
        id: args.templateId,
    });
    const imported = await uploadAndImport(context, csvPath, { importExportTemplate, doInsert: true, doUpdate: true });
    if (args.error) {
        await testErrorResult(imported, args.error);
        return null;
    }
    assert.equal(await imported.numberOfRowsInError, 0);

    await context.flushDeferredActions();

    const newStockCountNumbers = _.difference(
        (await context.select(xtremStock.nodes.StockCount, { number: true }, { filter: {} })).map(
            stockCount => stockCount.number,
        ),
        existingStockCountNumbers,
    );

    assert.equal(newStockCountNumbers.length, args.numberOfNewStockCount ?? 0);

    let stockCounts: xtremStock.nodes.StockCount[] = [];
    if (args.stockCountNumber) {
        stockCounts = [
            await context.read(xtremStock.nodes.StockCount, {
                _id: `#${args.stockCountNumber}`,
            }),
        ];
    } else {
        stockCounts = await context
            .query(xtremStock.nodes.StockCount, {
                filter: { number: { _in: newStockCountNumbers } },
            })
            .toArray();
    }
    if (stockCounts.length === 1) {
        const expected = JSON.parse(fs.readFileSync(resultPath, 'utf8'));

        assertObjects(
            await getStockCountJson(stockCounts[0], !args.stockCountNumber ? ['number'] : undefined),
            expected,
            'stockCount',
        );
    }

    return stockCounts;
}

describe('StockCount import success', () => {
    it('Should start and count a stock count with success', async () => {
        await Test.withUncommittedContext(async context => {
            await testImport(context, {
                fileName: 'stock-count-mini-start-and-count',
                templateId: 'miniStockCount',
                stockCountNumber: 'STOCK_COUNT04',
            });
        });
    });
    it('Should start, count and add a line in a stock count with success', async () => {
        await Test.withUncommittedContext(async context => {
            // As the status is not in the template, start the stock count before importing a new line
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { _id: `#STOCK_COUNT04` },
                { forUpdate: true },
            );
            await stockCount.$.set({ status: 'countInProgress' });
            await stockCount.$.save();
            await testImport(context, {
                fileName: 'stock-count-mini-start-count-add',
                templateId: 'miniStockCount',
                stockCountNumber: 'STOCK_COUNT04',
                // The stockDetail is not correctly set in the file
                // => The goal is to see if values of stock count line are set in the stockDetail to ensure consistency
                checkCsv: rows => {
                    const locationIndex = rows[0].indexOf('location'); // stockCountLine.location index
                    const stockDetailLocationIndex = rows[0].indexOf('location#1'); // stockDetail.location index
                    const location = rows[1][locationIndex];
                    const stockDetailLocation = rows[1][stockDetailLocationIndex];
                    assert.notEqual(location, stockDetailLocation);

                    const statusIndex = rows[0].indexOf('stockStatus'); // stockCountLine.status index
                    const stockDetailStatusIndex = rows[0].indexOf('status#3'); // stockDetail.status index
                    const stockStatus = rows[1][statusIndex];
                    const stockDetailStatus = rows[1][stockDetailStatusIndex];
                    assert.notEqual(stockStatus, stockDetailStatus);
                },
            });
        });
    });
    it('Should start, count and add 2 lines in a stock count with success', async () => {
        await Test.withUncommittedContext(async context => {
            // As the status is not in the template, start the stock count before importing a new line
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { _id: `#STOCK_COUNT04` },
                { forUpdate: true },
            );
            await stockCount.$.set({ status: 'countInProgress' });
            await stockCount.$.save();

            // the 2nd line added uses an existing lot, so the stockDetailLot.lot is set but not the expirationDate, sublot and supplierLot
            await testImport(context, {
                fileName: 'stock-count-mini-start-count-add-2',
                templateId: 'miniStockCount',
                stockCountNumber: 'STOCK_COUNT04',
            });
        });
    });
    it('Update a stock count with serial numbers', async () => {
        await Test.withUncommittedContext(
            async context => {
                const stockCount = (
                    await testImport(context, {
                        fileName: 'stock-count-mini-with-sn',
                        templateId: 'miniStockCountSN',
                        stockCountNumber: 'STOCK_COUNT07',
                    })
                )[0];

                assert.deepEqual(await (await stockCount.lines.elementAt(0)).countedSerialNumber, 0);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).countedSerialNumber, 4);
                assert.deepEqual(await (await stockCount.lines.elementAt(1)).countedSerialNumberPercentage, 80.0);
                assert.deepEqual(await (await stockCount.lines.elementAt(2)).countedSerialNumber, 6);
                assert.deepEqual(await (await stockCount.lines.elementAt(2)).countedSerialNumberPercentage, 100.0);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });

    // The file imported does not specify the stock count number
    // => the number must be determined automatically
    it('Create a stock count with 2 lines', async () => {
        await Test.withUncommittedContext(
            async context => {
                const item = await context.read(xtremMasterData.nodes.Item, { id: 'STKCOUNT1' });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US002' });
                const location1 = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC7|US002|Storage dock',
                });
                const location2 = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC2|US002|Storage dock',
                });
                const status1 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
                const status2 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });
                const stockUnit = await item.stockUnit;
                const owner = 'US002';
                const stockRecord1 = await xtremStockData.functions.stockLib.getStockRecord(context, {
                    item,
                    site,
                    location: location1,
                    lot: null,
                    status: status1,
                    stockUnit,
                    owner,
                });
                assert.isNotNull(stockRecord1);
                const stockRecord2 = await xtremStockData.functions.stockLib.getStockRecord(context, {
                    item,
                    site,
                    location: location2,
                    lot: null,
                    status: status2,
                    stockUnit,
                    owner,
                });
                assert.isNotNull(stockRecord2);

                const stockCount = (
                    await testImport(context, {
                        fileName: 'stock-count-create',
                        templateId: 'StockCount',
                        stockCountNumber: '',
                        numberOfNewStockCount: 1,
                    })
                )[0];

                // As 'stockRecord' is not imported, make sure the link with the stockRecord has been created
                assert.deepEqual((await (await stockCount.lines.elementAt(0)).stockRecord)?._id, stockRecord1._id);
                assert.deepEqual((await (await stockCount.lines.elementAt(1)).stockRecord)?._id, stockRecord2._id);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
});

describe('StockCount import fails', () => {
    it('Tries to add a serial number managed item with inconsistency', async () => {
        await Test.withUncommittedContext(
            async context => {
                // The import tries to add a serial number to a stock count line where stock characteristics are not the same
                await testImport(context, {
                    fileName: 'stock-count-mini-error-sn-01',
                    templateId: 'miniStockCountSN',
                    stockCountNumber: 'STOCK_COUNT07',
                    error: 'serialNumber: The stock count line and the serial number STK-SN-0003 do not correspond to the same stock line.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
    it('Duplicate a serial number to a new stock count line', async () => {
        await Test.withUncommittedContext(
            async context => {
                await testImport(context, {
                    // A line is added with a serial number already in the stock count (STK-SN-0003)
                    fileName: 'stock-count-mini-error-sn-02',
                    templateId: 'miniStockCountSN',
                    stockCountNumber: 'STOCK_COUNT07',
                    error: 'lines: You need to change the details on this stock count line because it duplicates existing stock information. Item: STK-SN-COUNT1, site: US001, location: LOC1, lot: null, status: A, stock unit: EACH, owner: US001.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
    it('Use a non existent serial number', async () => {
        await Test.withUncommittedContext(
            async context => {
                await testImport(context, {
                    // The last serial number of the 2nd line is modified with a non-existent serial number
                    // STK-SN-0008 is replaced by SN-DOES-NOT-EXIST
                    fileName: 'stock-count-mini-error-sn-03',
                    templateId: 'miniStockCountSN',
                    stockCountNumber: 'STOCK_COUNT07',
                    error: 'SerialNumber: record not found: {item:{id:STK-SN-COUNT1},id:SN-DOES-NOT-EXIST}',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
    it('Remove a serial number from a stock count line', async () => {
        await Test.withUncommittedContext(
            async context => {
                await testImport(context, {
                    // The 3rd serial number of the 1st line is removed from the csv file
                    fileName: 'stock-count-mini-error-sn-04',
                    templateId: 'miniStockCountSN',
                    stockCountNumber: 'STOCK_COUNT07',
                    error: ': A stock count line serial number cannot be deleted after the count has started. Item STK-SN-COUNT1, serial number STK-SN-0003.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
    const testCases = [
        {
            fileName: 'stock-count-mini-error-01-1',
            error: 'StockCountLine.item: cannot set value on frozen property',
        },
        {
            fileName: 'stock-count-mini-error-01-2',
            error: 'StockCountLine.location: cannot set value on frozen property',
        },
        {
            fileName: 'stock-count-mini-error-01-3',
            error: 'StockCountLine.stockStatus: cannot set value on frozen property',
        },
        {
            fileName: 'stock-count-mini-error-01-4',
            error: 'StockCountLine.owner: cannot set value on frozen property',
        },
        {
            fileName: 'stock-count-mini-error-01-5',
            error: 'StockDetailLot.lot: cannot set value on frozen property',
        },
        {
            fileName: 'stock-count-mini-error-01-6',
            error: 'StockDetailLot.lotNumber: cannot set value on frozen property',
        },
    ];
    testCases.forEach(testCase => {
        it(`Try to modify the stock count line ${testCase.fileName}`, () =>
            Test.withUncommittedContext(
                async context => {
                    await testImport(context, {
                        fileName: testCase.fileName,
                        templateId: 'miniStockCount',
                        stockCountNumber: 'STOCK_COUNT04',
                        error: testCase.error,
                    });
                },
                { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
            ));
    });
    it('Try to add a stock count line without stockDetailLot', async () => {
        await Test.withUncommittedContext(
            async context => {
                await testImport(context, {
                    fileName: 'stock-count-mini-error-02',
                    templateId: 'miniStockCount',
                    stockCountNumber: 'STOCK_COUNT04',
                    error: 'stockDetailLot: You need to select a lot for the item: ITEM_NEWLOT. Stock count STOCK_COUNT04, line 60.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
    it('Try to add a stock count line without stockDetail', async () => {
        await Test.withUncommittedContext(
            async context => {
                // The imports tries to add a line with an item that has lot management but the stockDetail is not set in the file
                await testImport(context, {
                    fileName: 'stock-count-mini-error-03',
                    templateId: 'miniStockCount',
                    stockCountNumber: 'STOCK_COUNT04',
                    error: 'stockDetailLot: You need to enter an expiration date.\\nstockDetailLot: You need to enter a sublot.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });

    it('Create a stock count with serial numbers', async () => {
        await Test.withUncommittedContext(
            async context => {
                // Tries to create a stock count with a line already in a stock count
                await testImport(context, {
                    fileName: 'stock-count-error-sn-01',
                    templateId: 'StockCount',
                    stockCountNumber: 'NEW_STOCK_COUNT',
                    error: 'lines: The line already exists in the stock count STOCK_COUNT07.',
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        );
    });
});
