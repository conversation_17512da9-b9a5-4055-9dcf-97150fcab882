import type { decimal, integer } from '@sage/xtrem-core';
import { Test, asyncArray, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremStock from '../../../lib';
import * as testLib from '../../fixtures/lib';

const { withSequenceNumberContext } = xtremMasterData.functions.testLib;

describe('StockCount node', () => {
    it('Read stock count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT01' });

            assert.instanceOf(stockCount, xtremStock.nodes.StockCount);
            assert.isNotNull(stockCount);
            assert.equal(await stockCount.number, 'STOCK_COUNT01');
            assert.equal(await stockCount.description, 'Closed stock count');
            assert.equal((await stockCount.effectiveDate).toString(), '2021-08-03');
            assert.equal(await stockCount.counter, 'Geraldine');
            assert.equal(await stockCount.status, 'closed');

            const stockCountLines = await stockCount.lines.toArray();
            assert.equal(stockCountLines.length, 2);
            assert.equal(await stockCountLines[0].status, 'counted');
            assert.equal(await (await stockCountLines[0].item).id, 'STKCOUNT1');
            assert.equal((await stockCountLines[0].zone)!._id, 1);
            assert.equal((await stockCountLines[0].location)!._id, 1);
            assert.isNull(await stockCountLines[0].lot);
            assert.equal(await (await stockCountLines[0].stockStatus)?.id, 'A');
            assert.equal(await stockCountLines[0].owner, 'US001');
            assert.equal(await (await stockCountLines[0].stockUnit).id, 'EACH');
            assert.equal(await stockCountLines[0].quantityInStockUnit, 205);
            assert.equal(await stockCountLines[0].countedQuantityInStockUnit, 200);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), -5);
            // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
            assert.equal((await stockCountLines[0].quantityVariancePercentage).valueOf(), -2.439024390243902439);

            assert.equal(await stockCountLines[1].status, 'counted');
            assert.equal(await (await stockCountLines[1].item).id, 'STKCOUNT1');
            assert.equal((await stockCountLines[1].zone)!._id, 1);
            assert.equal((await stockCountLines[1].location)!._id, 3);
            assert.isNull(await stockCountLines[1].lot);
            assert.equal(await (await stockCountLines[1].stockStatus)?.id, 'A');
            assert.equal(await stockCountLines[1].owner, 'US001');
            assert.equal(await (await stockCountLines[1].stockUnit).id, 'EACH');
            assert.equal(await stockCountLines[1].quantityInStockUnit, 99);
            assert.equal(await stockCountLines[1].countedQuantityInStockUnit, 100);
            assert.equal((await stockCountLines[1].quantityVariance).valueOf(), 1);
            // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
            assert.equal((await stockCountLines[1].quantityVariancePercentage).valueOf(), 1.010101010101010101);
        }));

    it('Delete stock count not started', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT02' },
                { forUpdate: true },
            );

            await stockCount.$.delete();
        }));

    it('Delete a started stock count - fails', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT01' },
                { forUpdate: true },
            );
            await assert.isRejected(stockCount.$.delete(), 'The stock count was not deleted.');
            assert.deepEqual(context.diagnoses, [
                { message: 'You cannot delete the stock count.', severity: 3, path: [] },
            ]);
        }));

    it('Remove a line of a stock count - fails', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT02' },
                { forUpdate: true },
            );
            const lineId = (await stockCount.lines.elementAt(0))._id;
            await assert.isRejected(
                stockCount.$.set({ lines: [{ _action: 'delete', _id: lineId }] }),
                'The stock count line was not deleted.',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    message:
                        'A line that was added during the start of the stock count cannot be deleted. Stock count STOCK_COUNT02, line 10.',
                    severity: 3,
                    path: ['lines', '3302'],
                },
            ]);
        }));

    async function addStockCountLine(
        stockCount: xtremStock.nodes.StockCount,
        args: { itemId: string; locationId: string; stockStatusId: string; countedQuantity: decimal },
    ): Promise<xtremStock.nodes.StockCountLine> {
        const site = await stockCount.stockSite;
        const lineIds = await stockCount.lines.map(line => line._id).toArray();
        const line = await stockCount.$.context.create(xtremStock.nodes.StockCountLine, {
            document: stockCount,
            item: `#${args.itemId}`,
            location: `#${args.locationId}|${await site.id}|Loading dock`,
            stockStatus: `#${args.stockStatusId}`,
            countedQuantityInStockUnit: args.countedQuantity,
            stockDetails: [
                {
                    item: `#${args.itemId}`,
                    site,
                    location: `#${args.locationId}|${await site.id}|Loading dock`,
                    status: `#${args.stockStatusId}`,
                    quantityInStockUnit: args.countedQuantity,
                    reasonCode: `#R1`,
                },
            ],
        });
        await line.$.save();

        const lineAdded = await stockCount.lines.find(stockCountLine => !lineIds.includes(stockCountLine._id));

        assert.isDefined(lineAdded);
        assert.deepEqual(await (await lineAdded.item).id, args.itemId);
        assert.deepEqual(await (await lineAdded.location)?.id, args.locationId);
        assert.deepEqual(await (await lineAdded.stockStatus)?.id, args.stockStatusId);
        assert.deepEqual(await lineAdded.status, args.countedQuantity ? 'counted' : 'toBeCounted');

        return lineAdded;
    }

    it('Remove a line of a stock count added after start', () =>
        Test.withContext(async context => {
            const stockCountNumber = 'STOCK_COUNT05';
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: stockCountNumber },
                { forUpdate: true },
            );

            assert.deepEqual(await (await stockCount.lines.elementAt(0)).isAddedDuringCount, false);
            assert.deepEqual(await (await stockCount.lines.elementAt(0)).canBeDeleted, false);

            let lineAdded = await addStockCountLine(stockCount, {
                itemId: 'STKCOUNT1',
                locationId: 'LOC8',
                stockStatusId: 'R1',
                countedQuantity: 0,
            });

            assert.deepEqual(await lineAdded.isAddedDuringCount, true);
            assert.deepEqual(await lineAdded.canBeDeleted, true);

            // The deletion of the new line must passed
            await stockCount.$.set({ lines: [{ _action: 'delete', _id: lineAdded._id }] });
            await stockCount.$.save();

            lineAdded = await addStockCountLine(stockCount, {
                itemId: 'STKCOUNT1',
                locationId: 'LOC8',
                stockStatusId: 'R1',
                countedQuantity: 2,
            });

            // simulate posting of the stock count line
            await lineAdded.$.set({ stockTransactionStatus: 'inProgress' });
            await lineAdded.$.save();

            assert.deepEqual(await lineAdded.canBeDeleted, false);
            await assert.isRejected(
                stockCount.$.set({ lines: [{ _action: 'delete', _id: lineAdded._id }] }),
                'The stock count line was not deleted.',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    message: `The stock count line cannot be deleted. It was already posted. Stock count ${stockCountNumber}, line ${await lineAdded._sortValue}.`,
                    severity: 3,
                    path: ['lines', '1001803'],
                },
            ]);

            await lineAdded.$.set({ forceUpdateForStock: true, stockTransactionStatus: 'completed' });
            assert.deepEqual(await lineAdded.canBeDeleted, false);
            await lineAdded.$.save();
            await assert.isRejected(
                stockCount.$.set({ lines: [{ _action: 'delete', _id: lineAdded._id }] }),
                'The stock count line was not deleted.',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    message: `The stock count line cannot be deleted. It was already posted. Stock count ${stockCountNumber}, line ${await lineAdded._sortValue}.`,
                    severity: 3,
                    path: ['lines', '1001803'],
                },
            ]);
        }));

    it('Delete a stock count line - fails', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT02' });
            const lineId = (await stockCount.lines.elementAt(0))._id;
            const stockCountLine = await context.read(
                xtremStock.nodes.StockCountLine,
                { _id: lineId },
                { forUpdate: true },
            );
            await assert.isRejected(stockCountLine.$.delete(), 'The stock count line was not deleted.');
            assert.deepEqual(context.diagnoses, [
                {
                    message:
                        'A line that was added during the start of the stock count cannot be deleted. Stock count STOCK_COUNT02, line 10.',
                    severity: 3,
                    path: [],
                },
            ]);
        }));

    it('Create stock count with missing description', () =>
        Test.withContext(async context => {
            const stockCount = await context.create(xtremStock.nodes.StockCount, {});

            await assert.isRejected(stockCount.$.save());

            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 4,
                    path: ['description'],
                    message: 'StockCount.description: property is required',
                },
            ]);
        }));

    it('Create stock count with missing site', () =>
        Test.withContext(async context => {
            const stockCount = await context.create(xtremStock.nodes.StockCount, {
                description: 'Stock count with only description',
            });

            await assert.isRejected(stockCount.$.save());

            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 4,
                    path: ['stockSite'],
                    message: 'StockCount.stockSite: property is required',
                },
            ]);
        }));

    it('Create stock count with incorrect dates', () =>
        Test.withContext(async context => {
            // FIXME: message for lastCountDate is not correct (XT-30607)
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockCount = await context.create(xtremStock.nodes.StockCount, {
                stockSite: site,
                description: 'Stock count with wrong dates',
                effectiveDate: date.make(2022, 8, 8),
                lastCountDate: date.make(2022, 9, 9),
            });

            await assert.isRejected(stockCount.$.save());

            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lastCountDate'],
                    message: 'value must not be after 2022-08-08',
                },
            ]);
        }));

    it('Create stock count with minimum properties - Check properties', () =>
        Test.withContext(
            async context => {
                const itemId = 'ITME_LOT';
                const siteId = 'US001';
                const locationId = 'LOC1';
                const zoneId = 'Loading dock';
                const stockStatusId = 'A';
                const owner = 'US001';
                const item = await context.read(xtremMasterData.nodes.Item, { id: itemId });
                const site = await context.read(xtremSystem.nodes.Site, { id: siteId });
                const location = await context.read(xtremMasterData.nodes.Location, {
                    _id: `#${locationId}|${await site.id}|${zoneId}`,
                });
                const stockStatus = await context.read(xtremStockData.nodes.StockStatus, { id: stockStatusId });
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, {
                    item,
                    site,
                    location,
                    lot: null,
                    status: stockStatus,
                    stockUnit: await item.stockUnit,
                    owner,
                });
                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: location._id,
                    lot: null,
                    status: stockStatus._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockCount = await context.create(xtremStock.nodes.StockCount, {
                    description: 'Stock count with only mandatory values',
                    stockSite: site,
                    lines: [
                        {
                            item,
                            location,
                            stockStatus,
                            stockRecord,
                            stockDetails: [
                                {
                                    ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                        stockSearchData,
                                        {
                                            quantityInStockUnit: 1000,
                                            movementType: 'adjustment',
                                        },
                                    ),
                                    reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))
                                        ._id,
                                },
                            ],
                        },
                    ],
                });

                await stockCount.$.set({ status: 'toBeCounted' });

                await stockCount.$.save({ flushDeferredActions: true });

                assert.deepEqual(stockCount.$.context.diagnoses, []);
                assert.isNotEmpty(await stockCount.number);
                assert.equal(await (await stockCount.stockSite).id, await site.id);
                assert.equal(await stockCount.description, 'Stock count with only mandatory values');
                assert.equal((await stockCount.effectiveDate).toString(), '2022-08-02');
                assert.equal(await stockCount.status, 'toBeCounted');
                assert.isEmpty(await stockCount.counter);
                assert.isNull(await stockCount.lastCountDate);
                assert.isNull(await stockCount.fromItem);
                assert.isNull(await stockCount.toItem);
                assert.equal((await stockCount.zones).length, 0);
                assert.equal((await stockCount.locations).length, 0);
                const stockCountLines = await stockCount.lines.toArray();
                assert.equal(stockCountLines.length, 1);
                assert.equal(await stockCountLines[0].status, 'toBeCounted');
                assert.equal(await (await stockCountLines[0].item).id, itemId);
                const stockCountLineZone = await stockCountLines[0].zone;
                assert.isNotNull(stockCountLineZone);
                assert.equal(await stockCountLineZone.id, zoneId);
                const stockCountLineLocation = await stockCountLines[0].location;
                assert.isNotNull(stockCountLineLocation);
                assert.isNotNull(await stockCountLines[0].location);
                assert.equal(await stockCountLineLocation.id, locationId);
                assert.isNull(await stockCountLines[0].lot);
                assert.equal(await (await stockCountLines[0].stockStatus)?.id, stockStatusId);
                assert.equal(await stockCountLines[0].owner, owner);
                assert.equal(await (await stockCountLines[0].stockUnit).id, await (await item.stockUnit).id);
                assert.equal(await stockCountLines[0].quantityInStockUnit, 0);
                assert.equal(await stockCountLines[0].countedQuantityInStockUnit, 0);
                assert.equal((await stockCountLines[0].quantityVariance).valueOf(), 0);
                assert.equal((await stockCountLines[0].quantityVariancePercentage).valueOf(), 0);

                // Test default attributes and dimensions
                const expectedAttributes = { project: 'AttPROJ', task: 'Task1' };
                const expectedDimensions = {
                    dimensionType02: 'CHANNELVALUE1',
                };
                const attributesLine1 = await stockCountLines[0].storedAttributes;
                const dimensionsLine1 = await stockCountLines[0].storedDimensions;
                assert.equal(JSON.stringify(attributesLine1), JSON.stringify(expectedAttributes));
                assert.equal(JSON.stringify(dimensionsLine1), JSON.stringify(expectedDimensions));
            },
            { today: '2022-08-02' },
        ));

    it('Create stock count all properties - Check properties', () =>
        Test.withContext(async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ChairBack' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const stockCount = await context.create(xtremStock.nodes.StockCount, {
                number: 'TEST01',
                description: 'Stock count with all values',
                stockSite: site,
                effectiveDate: date.make(2022, 8, 12),
                counter: 'Myself',
                lastCountDate: date.make(2022, 7, 12),
                fromItem: '#ChairLeg',
                toItem: '#ChairBack',
                zones: [1, 3],
                locations: [1, 3, 8],
                status: 'toBeCounted',
                lines: [
                    {
                        item: '#ChairBack',
                        location: 8,
                        stockStatus: '#A',
                        stockDetails: [
                            {
                                ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1000,
                                    movementType: 'adjustment',
                                }),
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                            },
                        ],
                        storedAttributes: { project: 'AttPROJ', employee: '', task: '' },
                        storedDimensions: {
                            dimensionType01: '300',
                            dimensionType02: 'CHANNELVALUE1',
                            dimensionType03: 'DIMTYPE1VALUE1',
                            dimensionType04: 'DIMTYPE2VALUE2',
                            dimensionType05: 'COMMERCIAL',
                        },
                    },
                ],
            });

            await stockCount.$.save();

            assert.deepEqual(stockCount.$.context.diagnoses, []);
            assert.equal(await stockCount.number, 'TEST01');
            assert.equal(await stockCount.description, 'Stock count with all values');
            assert.equal(await (await stockCount.stockSite).id, await site.id);
            assert.equal((await stockCount.effectiveDate).toString(), '2022-08-12');
            assert.equal(await stockCount.status, 'toBeCounted');
            assert.equal(await stockCount.counter, 'Myself');
            assert.equal((await stockCount.lastCountDate)!.toString(), '2022-07-12');
            assert.equal(await (await stockCount.fromItem)!.id, 'ChairLeg');
            assert.equal(await (await stockCount.toItem)!.id, 'ChairBack');
            assert.equal((await stockCount.zones)!.length, 2);
            assert.equal((await stockCount.zones)![0]._id, 1);
            assert.equal((await stockCount.zones)![1]._id, 3);
            assert.equal((await stockCount.locations)!.length, 3);
            assert.equal((await stockCount.locations)![0]._id, 1);
            assert.equal((await stockCount.locations)![1]._id, 3);
            assert.equal((await stockCount.locations)![2]._id, 8);

            const stockCountLines = await stockCount.lines.toArray();
            const expectedAttributes = { project: 'AttPROJ', employee: '', task: '' };
            const expectedDimensions = {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
                dimensionType03: 'DIMTYPE1VALUE1',
                dimensionType04: 'DIMTYPE2VALUE2',
                dimensionType05: 'COMMERCIAL',
            };
            const attributesLine1 = await stockCountLines[0].storedAttributes;
            const dimensionsLine1 = await stockCountLines[0].storedDimensions;
            assert.equal(JSON.stringify(attributesLine1), JSON.stringify(expectedAttributes));
            assert.equal(JSON.stringify(dimensionsLine1), JSON.stringify(expectedDimensions));
        }));

    it('Update a stock count line', () =>
        Test.withContext(async context => {
            // Update a line toBeCounted with stock quantity = 200 & counted quantity = 0 => counted quantity = 203
            // It resets status to counted
            let stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT02' },
                { forUpdate: true },
            );

            let stockCountLines = await stockCount.lines.toArray();
            assert.equal(await stockCountLines[0].status, 'toBeCounted');
            assert.equal((await stockCountLines[0].quantityInStockUnit).valueOf(), 200);
            assert.equal((await stockCountLines[0].countedQuantityInStockUnit).valueOf(), 0);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), 0);

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await stockCount.lines.elementAt(0))._id,
                        countedQuantityInStockUnit: 203,
                    },
                ],
            });
            await stockCount.$.save();

            assert.equal(await stockCountLines[0].status, 'counted');
            assert.equal((await stockCountLines[0].countedQuantityInStockUnit).valueOf(), 203);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), 3);

            // Update a line counted with stock quantity = 200 & counted quantity = 203 => excluded
            // It resets counted quantity to 0
            await stockCount.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await stockCount.lines.elementAt(0))._id,
                        status: 'excluded',
                    },
                ],
            });
            await stockCount.$.save();

            assert.equal(await stockCountLines[0].status, 'excluded');
            assert.equal((await stockCountLines[0].countedQuantityInStockUnit).valueOf(), 0);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), 0);

            // Create a receipt of 10 for the line excluded, stock quantity turns from 200 to 210
            const site = await stockCount.stockSite;
            const item = await stockCountLines[0].item;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await stockCountLines[0].location)?._id || null,
                lot: (await stockCountLines[0].lot)?._id || null,
                status: (await stockCountLines[0].stockStatus)?._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const oldStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();
            assert.equal(oldStockQuantity, 200);

            let receipt = await context.create(xtremStock.nodes.StockReceipt, {
                effectiveDate: date.today(),
                stockSite: site,
                lines: [
                    {
                        item: stockSearchData.item,
                        stockStatus: stockSearchData.status,
                        quantityInStockUnit: 10,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 10,
                            }),
                        ],
                    },
                ],
            });

            await receipt.$.save();

            await testLib.functions.TestHelpers.processStockUpdate(
                context,
                receipt,
                'receive',
                { movementType: 'receipt' },
                xtremStock.nodes.StockReceipt.onStockReply,
                [{ stockUpdateResultStatus: 'increased', stockTransactionStatus: 'succeeded' }],
            );

            assert.instanceOf(receipt, xtremStock.nodes.StockReceipt);

            receipt = await context.read(xtremStock.nodes.StockReceipt, { _id: receipt._id });
            assert.equal(await receipt.status, 'closed');

            const newStockQuantity = (
                await (
                    await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData)
                )?.quantityInStockUnit
            )?.valueOf();
            assert.equal(newStockQuantity, 210);

            // Update a line excluded (with counted quantity = 0) => counted with counted quantity = 220
            // It refreshes stock quantity from 200 to 210
            stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT02' },
                { forUpdate: true },
            );

            stockCountLines = await stockCount.lines.toArray();

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await stockCount.lines.elementAt(0))._id,
                        status: 'counted',
                        countedQuantityInStockUnit: 220,
                    },
                ],
            });
            await stockCount.$.save();
            assert.equal(await stockCountLines[0].status, 'counted');
            assert.equal((await stockCountLines[0].countedQuantityInStockUnit).valueOf(), 220);
            assert.equal((await stockCountLines[0].quantityInStockUnit).valueOf(), 210);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), 10);
        }));

    it('Update stock quantity of a stock count line - Failed', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            await assert.isRejected(
                stockCount.$.set({
                    lines: [
                        {
                            _action: 'update',
                            _sortValue: 10,
                            quantityInStockUnit: 50,
                        },
                    ],
                }),
                'StockCountLine.quantityInStockUnit: cannot set value on frozen property',
            );
        }));

    it('Update counted stock quantity of a stock count line with Serial number - Failed', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 3315 },
                    { forUpdate: true },
                );

                await stockCountLine.$.set({
                    countedQuantityInStockUnit: 50,
                });

                await assert.isRejected(stockCountLine.$.save());

                assert.deepEqual(stockCountLine.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['countedQuantityInStockUnit'],
                        message: 'value must not be greater than 3',
                    },
                ]);

                await assert.isRejected(stockCountLine.$.save(), 'The stock count line was not updated.');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('get counted serial numbers', () =>
        Test.withContext(
            async context => {
                const stockCount = await context.read(xtremStock.nodes.StockCount, { _id: 7 });

                assert.deepEqual(
                    await stockCount.lines
                        .map(async line => ({
                            _id: line._id,
                            countedSerialNumber: await line.countedSerialNumber,
                            countedSerialNumberPercentage: Number(await line.countedSerialNumberPercentage),
                        }))
                        .toArray(),
                    [
                        {
                            _id: 3315,
                            countedSerialNumber: 0,
                            countedSerialNumberPercentage: 0,
                        },
                        {
                            _id: 3316,
                            countedSerialNumber: 1,
                            countedSerialNumberPercentage: 20,
                        },
                        {
                            _id: 3317,
                            countedSerialNumber: 3,
                            countedSerialNumberPercentage: 50,
                        },
                    ],
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Verify chronological control between two stock changes on creation', () =>
        withSequenceNumberContext('StockCount', { isChronological: true }, async context => {
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'ChairBack' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            const newStockCount = await context.create(xtremStock.nodes.StockCount, {
                effectiveDate: date.make(2020, 7, 12),
                description: 'First stock count',
                stockSite: '#US001',
                lines: [
                    {
                        item: '#ChairBack',
                        location: 8,
                        stockStatus: '#A',
                        stockDetails: [
                            {
                                ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                    quantityInStockUnit: 1000,
                                    movementType: 'adjustment',
                                }),
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                            },
                        ],
                    },
                ],
            });
            await newStockCount.$.save({ flushDeferredActions: true });
            assert.deepEqual(newStockCount.$.context.diagnoses, []);

            const stockCountError = await context.create(xtremStock.nodes.StockCount, {
                effectiveDate: date.make(2020, 7, 11),
                description: 'Second stock count',
                stockSite: '#US001',
                lines: [{ item: '#STKCOUNT1', location: 8, stockStatus: '#R' }],
            });
            await assert.isRejected(
                stockCountError.$.save(),
                'The document date 2020-07-11 is earlier than the previous document date 2020-07-12.',
            );
        }));

    it('Cancel row if quantity in stock = 0 when starting the count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT03' },
                { forUpdate: true },
            );

            // The status of the lines is 'toBeCounted' before starting the count
            assert.deepEqual(await (await stockCount.lines.elementAt(0)).status, 'toBeCounted');
            assert.deepEqual(await (await stockCount.lines.elementAt(1)).status, 'toBeCounted');
            assert.deepEqual(await (await stockCount.lines.elementAt(2)).status, 'toBeCounted');

            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);
            assert.deepEqual(await (await stockCount.lines.elementAt(0)).status, 'excluded');
            assert.deepEqual(await (await stockCount.lines.elementAt(1)).status, 'toBeCounted');
            assert.deepEqual(await (await stockCount.lines.elementAt(2)).status, 'excluded');
        }));

    it('Lock item-site when starting the count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT04' },
                { forUpdate: true },
            );
            assert.equal(await stockCount.status, 'toBeCounted');

            const siteID = (await stockCount.stockSite)._id;

            const itemStatuses = [
                { _id: '#STKCOUNT1', countingInProgressOld: false, countingInProgressNew: false }, // STKCOUNT1
                { _id: '#17890', countingInProgressOld: false, countingInProgressNew: true }, // 17890
                { _id: '#Milk', countingInProgressOld: false, countingInProgressNew: false }, // Milk
                { _id: '#Muesli', countingInProgressOld: false, countingInProgressNew: true }, // Muesli
            ];

            await asyncArray(itemStatuses).forEach(async itemstatus => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    site: siteID,
                    item: itemstatus._id,
                });
                assert.deepEqual(
                    await itemSite.countingInProgress,
                    itemstatus.countingInProgressOld,
                    `Control countingInProgress status for item _id = ${itemstatus._id} before count start`,
                );
            });

            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);
            assert.equal(await stockCount.status, 'countInProgress');

            // Reset context cache as the bulkUpdate doesn't update the cache
            await Test.rollbackCache(context);

            await asyncArray(itemStatuses).forEach(async itemStatus => {
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, {
                    site: siteID,
                    item: itemStatus._id,
                });
                assert.deepEqual(
                    await itemSite.countingInProgress,
                    itemStatus.countingInProgressNew,
                    `Control countingInProgress status for item _id = ${itemStatus._id} after count start`,
                );
            });
        }));

    it('Check stock count line status with serial numbers', () =>
        Test.withContext(
            async context => {
                const stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: 'STOCK_COUNT07' },
                    { forUpdate: true },
                );

                const buildRange = (
                    serialQuantity: integer,
                ): Array<xtremStockData.interfaces.SerialNumberIdNumericRange> => {
                    const ranges: Array<xtremStockData.interfaces.SerialNumberIdNumericRange> = [];

                    if (!serialQuantity) return ranges;

                    ranges.push({
                        startingSerialNumberId: 'STK-SN-0009',
                        endingSerialNumberId: 'STK-SN-0009',
                        numericStart: 9,
                        numericEnd: 9,
                        quantity: 1,
                        originalStartId: 'STK-SN-0009',
                    });

                    if (serialQuantity > 1) {
                        const lastNumber = 13 + serialQuantity - 2;
                        ranges.push({
                            startingSerialNumberId: 'STK-SN-0013',
                            endingSerialNumberId: `STK-SN-00${lastNumber}`,
                            numericStart: 13,
                            numericEnd: lastNumber,
                            quantity: serialQuantity - 2,
                            originalStartId: 'STK-SN-0013',
                        });
                    }
                    return ranges;
                };

                const testData: Array<{
                    countedQuantity: integer;
                    serialQuantity: integer;
                    statusExpected: xtremStock.enums.StockCountLineStatus;
                }> = [
                    // Af the status was already 'countInProgress', by setting counted quantity to 0 and serial quantity to 0,
                    // the status is not modified and remains 'countInProgress'
                    { countedQuantity: 0, serialQuantity: 0, statusExpected: 'countInProgress' },
                    { countedQuantity: 3, serialQuantity: 0, statusExpected: 'toBeCounted' },
                    { countedQuantity: 3, serialQuantity: 1, statusExpected: 'countInProgress' },
                    { countedQuantity: 3, serialQuantity: 2, statusExpected: 'countInProgress' },
                    { countedQuantity: 1, serialQuantity: 1, statusExpected: 'counted' },
                    { countedQuantity: 3, serialQuantity: 3, statusExpected: 'counted' },
                ];

                await asyncArray(testData).forEach(async test => {
                    await stockCount.$.set({
                        lines: [
                            {
                                _action: 'update',
                                _id: 3317,
                                countedQuantityInStockUnit: test.countedQuantity,
                                jsonSerialNumbers: {
                                    ranges: buildRange(test.serialQuantity),
                                },
                            },
                        ],
                    });
                    await stockCount.$.save();

                    const stockCountLine = await context.read(xtremStock.nodes.StockCountLine, { _id: 3317 });

                    assert.deepEqual(
                        await stockCountLine.status,
                        test.statusExpected,
                        `countedQuantity=${test.countedQuantity} serialQuantity=${test.serialQuantity}`,
                    );
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Check set of jsonSerialNumbers - Fails', () =>
        Test.withContext(
            async context => {
                const stockCount = await context.read(
                    xtremStock.nodes.StockCount,
                    { number: 'STOCK_COUNT07' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    stockCount.$.set({
                        lines: [
                            {
                                _action: 'update',
                                _id: 3317,
                                countedQuantityInStockUnit: 6,
                                jsonSerialNumbers: {
                                    ranges: [
                                        {
                                            startingSerialNumberId: 'STK-SN-0014',
                                            endingSerialNumberId: 'STK-SN-0014',
                                            numericStart: 14,
                                            numericEnd: 14,
                                            quantity: 1,
                                            originalStartId: 'STK-SN-0014',
                                        },
                                        {
                                            startingSerialNumberId: 'STK-SN-0013',
                                            endingSerialNumberId: 'STK-SN-0016',
                                            numericStart: 13,
                                            numericEnd: 16,
                                            quantity: 4,
                                            originalStartId: 'STK-SN-0013',
                                        },
                                    ],
                                },
                            },
                        ],
                    }),
                    'There are duplicate serial numbers. Make sure that each line has a unique set of serial numbers.',
                );
                await assert.isRejected(
                    stockCount.$.set({
                        lines: [
                            {
                                _action: 'update',
                                _id: 3317,
                                countedQuantityInStockUnit: 2,
                                jsonSerialNumbers: {
                                    ranges: [
                                        {
                                            startingSerialNumberId: 'STK-SN-0013',
                                            endingSerialNumberId: 'STK-SN-0016',
                                            numericStart: 13,
                                            numericEnd: 16,
                                            quantity: 4,
                                            originalStartId: 'STK-SN-0013',
                                        },
                                    ],
                                },
                            },
                        ],
                    }),
                    'The number of selected serial numbers (4) cannot exceed the counted quantity (2).',
                );
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Check set of jsonSerialNumbers - Success', () =>
        Test.withContext(
            async context => {
                const stockCountLine = await context.read(
                    xtremStock.nodes.StockCountLine,
                    { _id: 3317 },
                    { forUpdate: true },
                );

                assert.deepEqual(await stockCountLine.jsonSerialNumbers, {
                    ranges: [
                        {
                            startingSerialNumberId: 'STK-SN-0013',
                            endingSerialNumberId: 'STK-SN-0015',
                            numericStart: 13,
                            numericEnd: 15,
                            quantity: 3,
                            originalStartId: 'STK-SN-0013',
                        },
                    ],
                });

                // add a serial number outside of existing range and add a serial number extending the existing range
                await stockCountLine.$.set({
                    jsonSerialNumbers: {
                        ranges: [
                            {
                                startingSerialNumberId: 'STK-SN-0009',
                                endingSerialNumberId: 'STK-SN-0009',
                                numericStart: 9,
                                numericEnd: 9,
                                quantity: 1,
                                originalStartId: 'STK-SN-0009',
                            },
                            {
                                startingSerialNumberId: 'STK-SN-0016',
                                endingSerialNumberId: 'STK-SN-0016',
                                numericStart: 16,
                                numericEnd: 16,
                                quantity: 1,
                                originalStartId: 'STK-SN-0016',
                            },
                            {
                                startingSerialNumberId: 'STK-SN-0013',
                                endingSerialNumberId: 'STK-SN-0015',
                                numericStart: 13,
                                numericEnd: 15,
                                quantity: 4,
                                originalStartId: 'STK-SN-0013',
                            },
                        ],
                    },
                });

                assert.deepEqual(await stockCountLine.jsonSerialNumbers, {
                    ranges: [
                        {
                            startingSerialNumberId: 'STK-SN-0009',
                            endingSerialNumberId: 'STK-SN-0009',
                            numericStart: 9,
                            numericEnd: 9,
                            quantity: 1,
                            originalStartId: 'STK-SN-0009',
                        },
                        {
                            startingSerialNumberId: 'STK-SN-0013',
                            endingSerialNumberId: 'STK-SN-0016',
                            numericStart: 13,
                            numericEnd: 16,
                            quantity: 4,
                            originalStartId: 'STK-SN-0013',
                        },
                    ],
                });
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Cancel row if stock count record is deleted linked to stock count line', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT02' },
                { forUpdate: true },
            );

            assert.deepEqual(await (await stockCount.lines.elementAt(0)).status, 'toBeCounted');

            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);

            const updateStockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT02' });

            assert.deepEqual(await (await updateStockCount.lines.elementAt(0)).status, 'excluded');
        }));

    it('Cancel row if non stock item has now a stock record after stock count was created.', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT10' },
                { forUpdate: true },
            );

            assert.deepEqual(await (await stockCount.lines.elementAt(0)).status, 'toBeCounted');

            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);

            const updateStockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT10' });

            assert.deepEqual(await (await updateStockCount.lines.elementAt(0)).status, 'excluded');
        }));
});

describe('Stock count: adding line', () => {
    it('Add a line already in the count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            const stockCountLines = await stockCount.lines.toArray();
            assert.equal(stockCountLines.length, 1);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOAVC' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await stockCount.lines.elementAt(0))._id,
                        countedQuantityInStockUnit: 12,
                        newLineOrderCost: 222,
                    },
                    {
                        _action: 'create',
                        status: 'counted',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 10,
                        newLineOrderCost: 111,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 10,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#R1',
                        location: '3',
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'R1' }))._id,
                            }),
                        ],
                    },
                ],
            });
            await stockCount.$.save();

            assert.equal(await stockCountLines[0].countedQuantityInStockUnit, 12);
            assert.isNull(await stockCountLines[0].newLineOrderCost);

            assert.equal(await stockCountLines[1].status, 'counted');
            assert.equal(await stockCountLines[1].quantityInStockUnit, 0);
            assert.equal(await stockCountLines[1].countedQuantityInStockUnit, 10);
            assert.equal((await stockCountLines[1].quantityVariance).valueOf(), 10);
            assert.equal((await stockCountLines[1].newLineOrderCost)?.valueOf(), 111);

            assert.equal(await stockCountLines[2].status, 'toBeCounted');
            assert.equal(await stockCountLines[2].quantityInStockUnit, 0);
            assert.equal(await stockCountLines[2].countedQuantityInStockUnit, 0);
            assert.equal((await stockCountLines[2].quantityVariance).valueOf(), 0);
            assert.equal((await stockCountLines[2].newLineOrderCost)?.valueOf(), 120.225);
        }));

    it('Add a line matching with a stock record - Failed', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Chair' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: 1,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };
            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item: await context.read(xtremMasterData.nodes.Item, { id: 'Chair' }),
                        stockStatus: '#A',
                        location: '1',
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                            }),
                        ],
                    },
                ],
            });
            await assert.isRejected(stockCount.$.save(), 'The stock count was not updated.');
            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000001'],
                    message:
                        'You need to change the details on this stock count line because it duplicates existing stock information. Item: Chair, site: US001, location: LOC1, lot: null, status: A, stock unit: EACH, owner: US001.',
                },
            ]);
        }));

    it('Add a line to a counted stock count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT06' },
                { forUpdate: true },
            );
            assert.equal(await stockCount.status, 'counted');
            assert.equal(await stockCount.lines.length, 3);
            // The first line is linked to a stock line => the price is not entered
            assert.isNotNull(await (await stockCount.lines.elementAt(0)).stockRecord);
            assert.isFalse(await (await stockCount.lines.elementAt(0)).shouldEnterOrderCost);
            // The second line is not linked to a stock line => the price is entered
            assert.isNull(await (await stockCount.lines.elementAt(1)).stockRecord);
            assert.notEqual((await (await stockCount.lines.elementAt(1)).newLineOrderCost) ?? 0, 0.0);
            assert.isFalse(await (await stockCount.lines.elementAt(1)).shouldEnterOrderCost);
            // The third line is not linked to a stock line and the price is not entered => the price is should be entered
            assert.isNull(await (await stockCount.lines.elementAt(2)).stockRecord);
            assert.equal((await (await stockCount.lines.elementAt(2)).newLineOrderCost) ?? 0, 0.0);
            assert.isTrue(await (await stockCount.lines.elementAt(2)).shouldEnterOrderCost);

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'STOAVC' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'R1' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item: await context.read(xtremMasterData.nodes.Item, { id: 'STOAVC' }),
                        stockStatus: '#R1',
                        location: '3',
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'R1' }))._id,
                            }),
                        ],
                    },
                ],
            });
            await stockCount.$.save();

            const stockCountLines = await stockCount.lines.toArray();
            const line = (await stockCount.lines.toArray()).length - 1;
            assert.equal(await stockCountLines[line].status, 'toBeCounted');
            assert.equal(await stockCountLines[line].countedQuantityInStockUnit, 0);
            assert.isTrue(await stockCountLines[line].isAddedDuringCount);
            assert.deepEqual(await stockCountLines[line].newLineOrderCost, 120.225); // 12383.17 / 103
            assert.equal(await stockCount.status, 'countInProgress');

            // check of cost warning
            await stockCount.$.set({
                lines: [{ _action: 'update', _id: stockCountLines[line]._id, newLineOrderCost: 0 }],
            });
            await stockCount.$.save();
            assert.isTrue(await stockCountLines[line].canEnterOrderCost);
            assert.isTrue(await stockCountLines[line].shouldEnterOrderCost);
        }));

    it('Duplicate a line by single save - ok', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 1000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 1000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'UBL001',
                                    supplierLot: 'UBSLOT001',
                                    sublot: 'UBSUBLOT1',
                                    expirationDate: date.make(2021, 9, 30),
                                },
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 2000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 2000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT002',
                                    sublot: 'SUBLOT1',
                                    expirationDate: date.make(2021, 9, 30),
                                },
                            }),
                        ],
                    },
                ],
            });
            assert.isOk(await stockCount.$.control());
        }));

    it('Duplicate a line by single save - Failed', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );
            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: (await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' }))._id,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 1000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 1000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT002',
                                    sublot: 'SUBLOT1',
                                    expirationDate: date.make(2021, 9, 30),
                                },
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 1000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 1000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'LOT0011',
                                    supplierLot: 'SLOT0011',
                                    sublot: 'SUBLOT011',
                                    expirationDate: date.make(2021, 9, 30),
                                },
                            }),
                        ],
                    },
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '3',
                        countedQuantityInStockUnit: 2000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 2000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'LOT001',
                                    supplierLot: 'SLOT002',
                                    sublot: 'SUBLOT1',
                                    expirationDate: date.make(2021, 9, 30),
                                },
                            }),
                        ],
                    },
                ],
            });
            await assert.isRejected(stockCount.$.save());

            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000001'],
                    message: 'Change or remove duplicate lines.',
                },
            ]);
        }));

    it('Duplicate a line by successive save - Failed', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: 1,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '1',
                        lot: '#Muesli|PURREC01|',
                        countedQuantityInStockUnit: 1000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 1000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lot: '29',
                                },
                            }),
                        ],
                    },
                ],
            });
            await stockCount.$.save();

            const stockCountLines = await stockCount.lines.toArray();
            const line = (await stockCount.lines.toArray()).length - 1;
            assert.equal(await stockCountLines[line].status, 'counted');
            assert.equal(await stockCountLines[line].quantityInStockUnit, 0);
            assert.equal(await stockCountLines[line].countedQuantityInStockUnit, 1000);

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '1',
                        countedQuantityInStockUnit: 2000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 2000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lot: '29',
                                },
                            }),
                        ],
                    },
                ],
            });

            await assert.isRejected(stockCount.$.save(), 'The stock count was not updated.');
            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000008'],
                    message: 'The line already exists in the stock count STOCK_COUNT05.',
                },
            ]);
        }));

    it('Duplicate a line with lot creation by successive save - Failed', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            const item = await context.read(xtremMasterData.nodes.Item, { id: 'Muesli' });
            const site = await stockCount.stockSite;
            const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                item: item._id,
                location: 1,
                lot: null,
                status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                stockUnit: (await item.stockUnit)._id,
                site: site._id,
                owner: await site.id,
            };
            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '1',
                        countedQuantityInStockUnit: 1000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 1000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'STOCK_COUNT05LOT1',
                                },
                            }),
                        ],
                    },
                ],
            });
            await stockCount.$.save();

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '1',
                        countedQuantityInStockUnit: 2000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 2000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'STOCK_COUNT05LOT2',
                                },
                            }),
                        ],
                    },
                ],
            });
            await stockCount.$.save();

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'create',
                        item,
                        stockStatus: '#A',
                        location: '1',
                        countedQuantityInStockUnit: 3000,
                        stockDetails: [
                            testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(stockSearchData, {
                                quantityInStockUnit: 3000,
                                movementType: 'adjustment',
                                reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))._id,
                                stockDetailLot: {
                                    lotNumber: 'STOCK_COUNT05LOT1',
                                },
                            }),
                        ],
                    },
                ],
            });

            await assert.isRejected(stockCount.$.save(), 'The stock count was not updated.');
            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000015'],
                    message: 'The line already exists in the stock count STOCK_COUNT05.',
                },
            ]);
        }));
});

describe('Stock count create function', () => {
    it('Read stock count', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT01' });

            assert.instanceOf(stockCount, xtremStock.nodes.StockCount);
            assert.isNotNull(stockCount);
            assert.equal(await stockCount.number, 'STOCK_COUNT01');
            assert.equal(await stockCount.description, 'Closed stock count');
            assert.equal((await stockCount.effectiveDate).toString(), '2021-08-03');
            assert.equal(await stockCount.counter, 'Geraldine');
            assert.equal(await stockCount.status, 'closed');

            const stockCountLines = await stockCount.lines.toArray();
            assert.equal(stockCountLines.length, 2);
            assert.equal(await stockCountLines[0].status, 'counted');
            assert.equal(await (await stockCountLines[0].item).id, 'STKCOUNT1');
            assert.equal(await (await stockCountLines[0].zone)!.id, 'Loading dock');
            assert.equal(await (await stockCountLines[0].location)!.id, 'LOC1');
            assert.isNull(await stockCountLines[0].lot);
            assert.equal(await (await stockCountLines[0].stockStatus)?.id, 'A');
            assert.equal(await stockCountLines[0].owner, 'US001');
            assert.equal(await (await stockCountLines[0].stockUnit).id, 'EACH');
            assert.equal(await stockCountLines[0].quantityInStockUnit, 205);
            assert.equal(await stockCountLines[0].countedQuantityInStockUnit, 200);
            assert.equal((await stockCountLines[0].quantityVariance).valueOf(), -5);
            // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
            assert.equal((await stockCountLines[0].quantityVariancePercentage).valueOf(), -2.439024390243902439);

            assert.equal(await stockCountLines[1].status, 'counted');
            assert.equal(await (await stockCountLines[1].item).id, 'STKCOUNT1');
            assert.equal(await (await stockCountLines[1].zone)!.id, 'Loading dock');
            assert.equal(await (await stockCountLines[1].location)!.id, 'LOC3');
            assert.isNull(await stockCountLines[1].lot);
            assert.equal(await (await stockCountLines[1].stockStatus)?.id, 'A');
            assert.equal(await stockCountLines[1].owner, 'US001');
            assert.equal(await (await stockCountLines[1].stockUnit).id, 'EACH');
            assert.equal(await stockCountLines[1].quantityInStockUnit, 99);
            assert.equal(await stockCountLines[1].countedQuantityInStockUnit, 100);
            assert.equal((await stockCountLines[1].quantityVariance).valueOf(), 1);
            // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
            assert.equal((await stockCountLines[1].quantityVariancePercentage).valueOf(), 1.010101010101010101);
        }));

    it('Update a stock count line attribute type restricted to', () =>
        Test.withContext(async context => {
            // Update a line toBeCounted with stock quantity = 200 & counted quantity = 0 => counted quantity = 203
            // It resets status to counted
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT05' },
                { forUpdate: true },
            );

            await stockCount.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: (await stockCount.lines.elementAt(0))._id,
                        storedAttributes: { project: '', employee: '', task: 'TASK1' },
                    },
                ],
            });
            await assert.isRejected(stockCount.$.save());
            assert.deepEqual(stockCount.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '3311'],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Create stock count default attributes', () =>
        Test.withContext(
            async context => {
                const companyDefaultAttributeProject = await context.create(
                    xtremFinanceData.nodes.CompanyDefaultAttribute,
                    {
                        company: '#RX001',
                        attributeType: '#project',
                        dimensionDefinitionLevel: 'stockDirect',
                        masterDataDefault: 'item',
                    },
                );
                await companyDefaultAttributeProject.$.save({ flushDeferredActions: true });

                const companyDefaultAttributeTask = await context.create(
                    xtremFinanceData.nodes.CompanyDefaultAttribute,
                    {
                        company: '#RX001',
                        attributeType: '#task',
                        dimensionDefinitionLevel: 'stockDirect',
                        masterDataDefault: 'item',
                    },
                );
                await companyDefaultAttributeTask.$.save({ flushDeferredActions: true });

                const item = await context.read(xtremMasterData.nodes.Item, { id: 'HC001' }, { forUpdate: true });
                const site = await context.read(xtremSystem.nodes.Site, { id: 'RX001' });

                await item.$.set({
                    storedAttributes: { project: 'AttPROJ', employee: '', task: 'Task1' },
                });
                await item.$.save();

                const stockReceipt = await context.create(xtremStock.nodes.StockReceipt, {
                    number: 'Test_receipt_HC001',
                    effectiveDate: date.make(2023, 3, 17),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 3,
                            stockStatus: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                        },
                    ],
                });

                assert.deepEqual(
                    JSON.stringify(await (await stockReceipt.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );

                const stockSearchData: xtremStockData.interfaces.StockSearchData = {
                    item: item._id,
                    location: null,
                    lot: null,
                    status: (await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }))._id,
                    stockUnit: (await item.stockUnit)._id,
                    site: site._id,
                    owner: await site.id,
                };

                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                const newStockIssue = await context.create(xtremStock.nodes.StockIssue, {
                    effectiveDate: date.make(2023, 3, 17),
                    stockSite: site,
                    lines: [
                        {
                            item,
                            quantityInStockUnit: 1,
                            stockStatus: stockSearchData.status,
                            stockDetails: [
                                {
                                    stockUnit: stockSearchData.stockUnit,
                                    quantityInStockUnit: -1,
                                    stockRecord,
                                },
                            ],
                        },
                    ],
                });
                await newStockIssue.$.save({ flushDeferredActions: true });
                assert.deepEqual(
                    JSON.stringify(await (await newStockIssue.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );

                const stockCount = await context.create(xtremStock.nodes.StockCount, {
                    description: 'Stock count default dimensions',
                    stockSite: site,
                    lines: [
                        {
                            item,
                            location: null,
                            stockStatus: '#A',
                            stockRecord,
                            stockDetails: [
                                {
                                    ...testLib.functions.TestHelpers.stockSearchDataToDetailCreateData(
                                        stockSearchData,
                                        {
                                            quantityInStockUnit: 4,
                                            movementType: 'adjustment',
                                        },
                                    ),
                                    reasonCode: (await context.read(xtremMasterData.nodes.ReasonCode, { id: 'R1' }))
                                        ._id,
                                },
                            ],
                        },
                    ],
                });
                await stockCount.$.save({ flushDeferredActions: true });

                assert.deepEqual(
                    JSON.stringify(await (await stockCount.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );
            },
            { today: '2022-08-02' },
        ));

    it('Confirm zero quantities - fails', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremStock.nodes.StockCount.confirmZeroQuantity(context, 'STOCK_COUNT01'),
                'You cannot update the quantities on a stock count that was closed: stock count STOCK_COUNT01.',
            );

            await assert.isRejected(
                xtremStock.nodes.StockCount.confirmZeroQuantity(context, 'STOCK_COUNT04'),
                'The stock count has not started yet: STOCK_COUNT04. Start the count to update the quantities.',
            );
        }));

    it('Confirm zero quantities - success', () =>
        Test.withContext(async context => {
            let stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT04' },
                { forUpdate: true },
            );
            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);
            assert.equal(await stockCount.status, 'countInProgress');

            await stockCount.$.set({ lines: [{ _sortValue: 20, _action: 'update', countedQuantityInStockUnit: 123 }] });
            await stockCount.$.save();
            assert.deepEqual(
                await stockCount.lines
                    .map(async line => ({
                        _sortValue: await line._sortValue,
                        status: await line.status,
                        countedQuantity: await line.countedQuantityInStockUnit,
                    }))
                    .toArray(),
                [
                    { _sortValue: 10, status: 'excluded', countedQuantity: 0.0 },
                    { _sortValue: 20, status: 'counted', countedQuantity: 123.0 },
                    { _sortValue: 30, status: 'excluded', countedQuantity: 0.0 },
                    { _sortValue: 40, status: 'toBeCounted', countedQuantity: 0.0 },
                    { _sortValue: 50, status: 'toBeCounted', countedQuantity: 0.0 },
                ],
            );
            const recordsUpdated = await xtremStock.nodes.StockCount.confirmZeroQuantity(context, 'STOCK_COUNT04');

            stockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT04' });
            assert.deepEqual(recordsUpdated, 2);
            assert.deepEqual(await stockCount.status, 'counted');
            assert.deepEqual(
                await stockCount.lines
                    .map(async line => ({
                        _sortValue: await line._sortValue,
                        status: await line.status,
                        countedQuantity: await line.countedQuantityInStockUnit,
                    }))
                    .toArray(),
                [
                    { _sortValue: 10, status: 'excluded', countedQuantity: 0.0 },
                    { _sortValue: 20, status: 'counted', countedQuantity: 123.0 },
                    { _sortValue: 30, status: 'excluded', countedQuantity: 0.0 },
                    { _sortValue: 40, status: 'counted', countedQuantity: 0.0 },
                    { _sortValue: 50, status: 'counted', countedQuantity: 0.0 },
                ],
            );
        }));

    it('Confirm zero quantities on line without stock - success', () =>
        Test.withContext(async context => {
            const stockCountNumber = 'STOCK_COUNT11';
            let stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: stockCountNumber },
                { forUpdate: true },
            );
            const started = await xtremStock.nodes.StockCount.start(context, stockCount);
            assert.isTrue(started);
            assert.equal(await stockCount.status, 'countInProgress');

            let stockCountLine = await stockCount.lines.elementAt(0);

            assert.isTrue(await stockCountLine.isNonStockItem);
            const recordsUpdated = await xtremStock.nodes.StockCount.confirmZeroQuantity(context, stockCountNumber);

            stockCount = await context.read(xtremStock.nodes.StockCount, { number: stockCountNumber });
            assert.deepEqual(recordsUpdated, 1);
            assert.deepEqual(await stockCount.status, 'counted');
            stockCountLine = await stockCount.lines.elementAt(0);
            assert.equal(await stockCountLine.status, 'counted');
        }));
});

describe('Stock count- itemSite filter', () => {
    it('Create stock count using different search input', () =>
        Test.withContext(async context => {
            const stockCount = await context.create(xtremStock.nodes.StockCount, {
                number: 'TEST01',
                description: 'Stock count with all values',
                stockSite: '#US001',
                effectiveDate: date.today(),
                counter: 'Myself',
            });

            await stockCount.$.save();

            assert.deepEqual(stockCount.$.context.diagnoses, []);
            assert.equal(await stockCount.number, 'TEST01');
            assert.equal(await stockCount.itemSites.length, 40);

            let stockCountUpdate = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'TEST01' },
                { forUpdate: true },
            );

            // from item input
            await stockCountUpdate.$.set({
                fromItem: '#ChairLeg',
            });
            await stockCountUpdate.$.save();

            stockCountUpdate = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'TEST01' },
                { forUpdate: true },
            );

            assert.equal(await stockCountUpdate.itemSites.length, 33);

            // to item input
            await stockCountUpdate.$.set({
                toItem: '#ChairLeg',
            });

            await stockCountUpdate.$.save();

            stockCountUpdate = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'TEST01' },
                { forUpdate: true },
            );

            assert.equal(await stockCountUpdate.itemSites.length, 1);

            const itemCategory = await context.read(xtremMasterData.nodes.ItemCategory, { _id: '#OTHER' });

            await stockCountUpdate.$.set({ toItem: null, fromItem: null, categories: [itemCategory._id] });
            await stockCountUpdate.$.save();

            stockCountUpdate = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'TEST01' },
                { forUpdate: true },
            );

            assert.equal(await stockCountUpdate.itemSites.length, 38);

            // location zone input

            const locationZone = await context.read(xtremMasterData.nodes.LocationZone, {
                _id: '#US001|Loading dock',
            });

            await stockCountUpdate.$.set({ categories: [], hasStockRecords: true, zones: [locationZone._id] });
            await stockCountUpdate.$.save();

            assert.equal(await stockCountUpdate.itemSites.length, 38);

            // location  input

            const location = await context.read(xtremMasterData.nodes.Location, { id: 'LOC3' });

            await stockCountUpdate.$.set({ zones: [], locations: [location._id] });
            await stockCountUpdate.$.save();

            stockCountUpdate = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'TEST01' },
                { forUpdate: true },
            );
            assert.equal(await stockCountUpdate.itemSites.length, 12);
        }));
});

describe('Stock count- Create stock count from filter', () => {
    const commonHeader = { number: 'TEST01', stockSite: '#DEP1-S01', effectiveDate: date.today(), counter: 'Myself' };

    const expectedZeroStockItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: '3542', quantity: 0.0 },
        { itemId: 'CCZ_APPLE', quantity: 0.0 },
        { itemId: 'CCZ_APPLE_PIE', quantity: 0.0 },

        { itemId: 'CCZ_BUTTER', quantity: 0.0 },
        { itemId: 'CCZ_EGG', quantity: 0.0 },
        { itemId: 'CCZ_FLOUR', quantity: 0.0 },
        { itemId: 'CCZ_PIE_CRUST', quantity: 0.0 },
        { itemId: 'CCZ_SALT', quantity: 0.0 },
        { itemId: 'CCZ_SUGAR', quantity: 0.0 },
        { itemId: 'RM_ITEM_001', quantity: 0.0 },
        { itemId: 'RM_ITEM_002', quantity: 0.0 },
        { itemId: 'RM_ITEM_003', quantity: 0.0 },
        { itemId: 'RM_ITEM_004', quantity: 0.0 },
        { itemId: 'RM_ITEM_005', quantity: 0.0 },
    ];
    const expectedWithStockItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: 'AI_ITEM_001', quantity: 300.0 },
        { itemId: 'STOAVC', quantity: 92.0 },
        { itemId: 'STOFIFO', quantity: 142.0 },
    ];

    const expectedWithStockSerialNumberItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: 'STK-SN-TO-COUNT', quantity: 3.0 },
        { itemId: 'STK-SN-TO-COUNT', quantity: 5.0 },
        { itemId: 'STK-SN-TO-COUNT', quantity: 7.0 },
    ];

    const expectedNonStockItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: 'CARROT', quantity: 0.0 },
        { itemId: 'CARROT-BOX', quantity: 0.0 },
        { itemId: 'CARROT-BOX-STD', quantity: 0.0 },
    ];

    const expectedLocationStockItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: 'Chair', quantity: 1.0 },
        { itemId: 'Chair', quantity: 3.0 },
    ];

    const expectedFilteredLocationStockItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] = [
        { itemId: 'Chair', quantity: 3.0 },
    ];

    const expectedAllItems: testLib.functions.StockCountHelpers.StockCountFilterResult[] =
        testLib.functions.StockCountHelpers.sortResult(expectedZeroStockItems.concat(expectedWithStockItems));

    it('Create stock count for all stock records only', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for only stock records',
                    hasStockRecords: true,
                },
                confirmOptions: { allSelected: true, stockRecords: [], itemSites: [] },
                expectedResults: {
                    beforeConfirmation: expectedWithStockItems,
                    afterConfirmation: expectedWithStockItems,
                },
            });
        }));

    it('Create stock count for all stock records and items site without stock records', () =>
        Test.withContext(async context => {
            const stockCount = await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for only stock records',
                    hasStockRecords: false,
                },
                confirmOptions: { allSelected: true, stockRecords: [], itemSites: [] },
                expectedResults: {
                    beforeConfirmation: expectedAllItems,
                    afterConfirmation: expectedAllItems,
                },
            });

            await stockCount.lines.forEach(async (line, index) => {
                const stockRecord = await line.stockRecord;
                const message = `index=${index} Item: ${await (await line.item).id}, stockRecord: ${stockRecord?._id}`;
                assert.isFalse(await line.isAddedDuringCount, `isAddedDuringCount=true for ${message}`);
                assert.equal(await line.isNonStockItem, !stockRecord, message);
                assert.equal(await line.isCreatedFromStockRecord, !!stockRecord, message);
            });
        }));

    it('Create stock count for selected stock records', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for only stock records',
                    hasStockRecords: true,
                },
                confirmOptions: {
                    allSelected: false,
                    // Select the 1st stockRecord of the STOAVC item
                    stockRecords: async (stockCount: xtremStock.nodes.StockCount) => {
                        const selectedItemSites = stockCount.itemSites.filter(
                            async itemSite => (await (await itemSite.item).id) === 'STOAVC',
                        );
                        assert.equal(await selectedItemSites.length, 1);
                        return [(await (await selectedItemSites.elementAt(0)).stockRecords.elementAt(0))._id];
                    },
                    itemSites: [],
                },
                expectedResults: {
                    beforeConfirmation: expectedWithStockItems,
                    afterConfirmation: [{ itemId: 'STOAVC', quantity: 92.0 }],
                },
            });
        }));

    it('Create stock count for selected items without stock records', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for only stock records',
                    hasStockRecords: false,
                },
                confirmOptions: {
                    allSelected: false,
                    stockRecords: [],
                    // Select only STOAVC item
                    itemSites: async (stockCount: xtremStock.nodes.StockCount) => {
                        const selectedItemSites = stockCount.itemSites.filter(
                            async itemSite => (await (await itemSite.item).id) === 'STOAVC',
                        );
                        assert.equal(await selectedItemSites.length, 1);
                        return [(await selectedItemSites.elementAt(0))._id];
                    },
                },
                expectedResults: {
                    beforeConfirmation: expectedAllItems,
                    afterConfirmation: [{ itemId: 'STOAVC', quantity: 92.0 }],
                },
            });
        }));

    it('Create stock count for SN items with stock', () =>
        Test.withContext(
            async context => {
                // Select a range of items managed with serial numbers
                // The items without stock records are not selected
                const stockCount = await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                    stockCountCreateData: {
                        ...commonHeader,
                        description: 'Stock count for SN items with stock records',
                        stockSite: '#US001',
                        hasStockRecords: false,
                        fromItem: '#STK-SN-COUNT1',
                        toItem: '#STK-SN-TO-COUNT',
                    },
                    confirmOptions: {
                        allSelected: true,
                        stockRecords: [],
                        itemSites: [],
                    },
                    expectedResults: {
                        beforeConfirmation: expectedWithStockSerialNumberItems,
                        afterConfirmation: expectedWithStockSerialNumberItems,
                    },
                });

                assert.deepEqual(
                    await (await stockCount.lines.elementAt(0)).stockCountLineSerialNumbers
                        .map(async stockCountLineSerialNumber => (await stockCountLineSerialNumber.serialNumber).id)
                        .toArray(),
                    ['STK-SN-0001', 'STK-SN-0002', 'STK-SN-0003'],
                );
                assert.deepEqual(
                    await (await stockCount.lines.elementAt(1)).stockCountLineSerialNumbers
                        .map(async stockCountLineSerialNumber => (await stockCountLineSerialNumber.serialNumber).id)
                        .toArray(),
                    ['STK-SN-0004', 'STK-SN-0005', 'STK-SN-0006', 'STK-SN-0007', 'STK-SN-0008'],
                );
                assert.deepEqual(
                    await (await stockCount.lines.elementAt(2)).stockCountLineSerialNumbers
                        .map(async stockCountLineSerialNumber => (await stockCountLineSerialNumber.serialNumber).id)
                        .toArray(),
                    [
                        'STK-SN-0009',
                        'STK-SN-0010',
                        'STK-SN-0011',
                        'STK-SN-0013',
                        'STK-SN-0014',
                        'STK-SN-0015',
                        'STK-SN-0016',
                    ],
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    // Here, the serial number service option is not activated
    // => there is no filter on serial number items
    it('Create stock count for SN items without stock', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for SN items with stock records',
                    stockSite: '#US001',
                    hasStockRecords: false,
                    fromItem: '#STK-SN-COUNT1',
                    toItem: '#STK-SN-TO-COUNT',
                },
                confirmOptions: {
                    allSelected: false,
                    stockRecords: [],
                    itemSites: (stockCount: xtremStock.nodes.StockCount) => {
                        // select 1 item with stock records - STK-SN-COUNT1 item in progress on STOCK_COUNT07
                        return stockCount.itemSites
                            .filter(async itemSite => ['STK-SN-TO-COUNT'].includes(await (await itemSite.item).id))
                            .map(itemSite => itemSite._id)
                            .toArray();
                    },
                },
                expectedResults: {
                    beforeConfirmation: expectedWithStockSerialNumberItems,
                    afterConfirmation: expectedWithStockSerialNumberItems,
                },
            });
        }));

    it('Create stock count for none stock item toBeCounted - ensure that if another stock count creation is done that they are excluded from selection.', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    ...commonHeader,
                    description: 'Stock count for non stock item',
                    stockSite: '#US001',
                    hasStockRecords: false,
                    fromItem: '#CARROT',
                    toItem: '#CARROT-BOX-STD',
                },
                confirmOptions: {
                    allSelected: false,
                    stockRecords: [],
                    itemSites: (stockCount: xtremStock.nodes.StockCount) => {
                        // select 3 non stock items returned by the search criteria
                        return stockCount.itemSites
                            .filter(async itemSite =>
                                ['CARROT', 'CARROT-BOX', 'CARROT-BOX-STD'].includes(await (await itemSite.item).id),
                            )
                            .map(itemSite => itemSite._id)
                            .toArray();
                    },
                },
                expectedResults: {
                    beforeConfirmation: expectedNonStockItems,
                    afterConfirmation: expectedNonStockItems,
                },
            });

            // create another stock count for the same search criteria - no items will be returned as they are already in progress
            const stockCount = await context.create(xtremStock.nodes.StockCount, {
                number: 'TEST03',
                description: 'Stock count for non stock item already in another stock count - tobeCounted',
                stockSite: '#US001',
                effectiveDate: date.today(),
                counter: 'Myself',
                hasStockRecords: false,
                fromItem: '#CARROT',
                toItem: '#CARROT-BOX-STD',
            });

            await stockCount.$.save();

            assert.deepEqual(stockCount.$.context.diagnoses, []);
            assert.equal(await stockCount.number, 'TEST03');
            assert.equal(await stockCount.itemSites.length, 0); // no items returned to be selected
        }));

    it('Create stock count and filter by single location for site with multiple locations on stock records', () =>
        Test.withContext(async context => {
            await testLib.functions.StockCountHelpers.testStockCountFilter(context, {
                stockCountCreateData: {
                    number: 'TEST01',
                    effectiveDate: date.today(),
                    counter: 'Myself',
                    description: 'Stock count for non stock item',
                    stockSite: '#US001',
                    hasStockRecords: true,
                    locations: [1],
                    fromItem: '#Chair',
                    toItem: '#Chair',
                },
                confirmOptions: {
                    allSelected: false,
                    stockRecords: async (stockCount: xtremStock.nodes.StockCount) => {
                        const selectedItemSites = stockCount.itemSites.filter(
                            async itemSite => (await (await itemSite.item).id) === 'Chair',
                        );
                        assert.equal(await selectedItemSites.length, 1);
                        return [(await (await selectedItemSites.elementAt(0)).stockRecords.elementAt(0))._id];
                    },
                    itemSites: [],
                },
                expectedResults: {
                    beforeConfirmation: expectedLocationStockItems, // location filter on page, server returns all selected locations
                    afterConfirmation: expectedFilteredLocationStockItems, // location filter applied on server same as on page
                },
            });
        }));

    it('Delete stock count if no lines when status to be counted', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(
                xtremStock.nodes.StockCount,
                { number: 'STOCK_COUNT12' },
                { forUpdate: true },
            );

            assert.equal(await stockCount.lines.length, 0);

            await stockCount.$.delete();
            assert.isFalse(await context.exists(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT12' }));
        }));
});

describe('Stock record linked to stock count line', () => {
    it('Delete stock record and ensure stock record linked to stock count line set to null', () =>
        Test.withContext(async context => {
            const stockCount = await context.read(xtremStock.nodes.StockCount, { number: 'STOCK_COUNT09' });

            const stockCountLines = await stockCount.lines.toArray();
            const stockCountLine = stockCountLines[0]._id;
            assert.isNotNull(await stockCountLines[0].stockRecord);

            const deleteStockRecord = await context.read(
                xtremStockData.nodes.Stock,
                {
                    _id: (await stockCountLines[0].stockRecord)?._id,
                },
                { forUpdate: true },
            );

            await deleteStockRecord.$.delete();

            const stockRecordStockCountLine = await context.select(
                xtremStock.nodes.StockCountLine,
                { stockRecord: true },
                {
                    filter: { _id: stockCountLine },
                },
            );

            assert.isNull(stockRecordStockCountLine[0].stockRecord);
        }));

    it('Check writeable context to lock stock transaction to progress updates', () =>
        Test.withContext(async context => {
            const number = 'STOCK_COUNT06';
            await xtremStockData.testHelpers.testPostToStockSetInProgress(context, {
                documentNode: xtremStock.nodes.StockCount,
                number,
                assert,
                postToStock: xtremStock.nodes.StockCount.postToStock,
            });

            await Test.rollbackCache(context);

            const stockCount = await context.read(xtremStock.nodes.StockCount, { number });
            // after the call to postToStock, the document must be 'inProgress' status'
            assert.equal(await stockCount.status, 'counted');
        }));
});
