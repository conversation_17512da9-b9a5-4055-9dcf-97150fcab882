import type { Dict, ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as accountingInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/accounting-integration';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { MutationResult } from '@sage/xtrem-finance-data/build/lib/shared-functions/interfaces/common';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Item,
    Location,
    Supplier,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { confirmDialogToBoolean, formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type {
    GraphApi,
    PurchaseDocumentStatus,
    PurchaseDocumentTax,
    PurchaseDocumentTaxBinding,
    PurchaseInvoice,
    PurchaseInvoiceLine,
    PurchaseOrderLine,
    PurchaseOrderLineToPurchaseReceiptLine,
    PurchaseOrderLineToPurchaseReceiptLineBinding,
    PurchaseReceiptInvoiceStatus,
    PurchaseReceiptLine,
    PurchaseReceiptLineBinding,
    PurchaseReceiptLineToPurchaseInvoiceLine,
    PurchaseReceiptLineToPurchaseReturnLine,
    PurchaseReceipt as PurchaseReceiptNode,
    PurchaseReceiptReturnStatus,
    PurchaseReturn,
    PurchaseReturnLine,
} from '@sage/xtrem-purchasing-api';
import type { Lot, StockDocumentTransactionStatus, StockStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { linkToPage } from '@sage/xtrem-system/build/lib/shared-functions/utils';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { refreshTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/display-taxes';
import * as ui from '@sage/xtrem-ui';
import {
    confirmDialogWithAcceptButtonText,
    getSiteAndSupplier,
    isUiPartialCollectionValuePurchaseReceiptLineBinding,
    printPurchaseReceipt,
    setOverwriteNote,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-purchase-receipt';
import type { PurchaseReceiptStepSequenceStatus } from '../client-functions/interfaces/step-sequence';
import * as LandedCost from '../client-functions/landed-cost';
import * as PillColorPurchase from '../client-functions/pill-color';
import { calculatePrices } from '../client-functions/purchase-price';
import * as actionFunctions from '../client-functions/purchase-receipt-actions-functions';
import { createPurchaseInvoice } from '../client-functions/purchase-receipt-lib';
import { addLineFromOrder } from '../client-functions/receipt-from-order';
import { onChangeLineQuantity, setPriceOrigin } from '../client-functions/receipt-line';
import { displayTaxes } from '../client-functions/shared/display-taxes';
import { isExchangeRateHidden, laterThanToday } from '../client-functions/shared/page-functions';
import {
    isPurchaseReceiptLineActionDisabled,
    isPurchaseReceiptLinePropertyDisabled,
} from '../shared-functions/edit-rules';

@ui.decorators.page<PurchaseReceipt, PurchaseReceiptNode>({
    title: 'Purchase receipt',
    objectTypeSingular: 'Purchase receipt',
    objectTypePlural: 'Purchase receipts',
    idField() {
        return this.number;
    },
    node: '@sage/xtrem-purchasing/PurchaseReceipt',
    menuItem: purchasing,
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 500,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [this.post, this.repost, this.createPurchaseReturn],
        });
    },

    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({ quickActions: [this.print] });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this._manageDisplayApplicativePageActions(isDirty);
        this.receiptStepSequence.statuses = this.getDisplayStatusStepSequence();
    },
    async onLoad() {
        await this.initPage();
        if (this.$.recordId) {
            await this.initPosting();
        }
        this.receiptStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(false);
        this.isRepost = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.fromLandedCostPanel = this.$.queryParameters.fromLandedCostPanel?.toString() === 'true' || false;
        if (this.fromLandedCostPanel) {
            this.isRepost = true;
        }
        this.sourceDocumentData = JSON.parse(this.$.queryParameters.sourceDocumentData?.toString() ?? '{}');
        this.$.setPageClean();
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
    },
    navigationPanel: {
        orderBy: { receiptDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-purchasing/PurchaseReceipt',
                queryParameters: (_value, rowData) => ({ _id: rowData?._id ?? '' }),
            }),
            line2: ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptNode, Supplier>({
                bind: 'supplier',
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            id: ui.nestedFields.text({
                bind: { supplier: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({
                bind: 'receiptDate',
                title: 'Receipt date',
            }),
            line_4: ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptNode, Site>({
                title: 'Receiving site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                tunnelPage: undefined,
            }),
            line_5: ui.nestedFields.numeric({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                isMandatory: true,
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line6: ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding' }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                ],
            }),
            line7: ui.nestedFields.numeric<PurchaseReceipt, PurchaseReceiptNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line8: ui.nestedFields.numeric<PurchaseReceipt, PurchaseReceiptNode>({
                title: 'Tax',
                bind: 'totalTaxAmount',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line10: ui.nestedFields.text<PurchaseReceipt, PurchaseReceiptNode>({
                title: 'Supplier packing slip',
                bind: 'supplierDocumentNumber',
                isHiddenOnMainField: true,
            }),
            line11: ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptNode>({
                title: 'Carrier',
                bind: 'carrier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                isHiddenOnMainField: true,
            }),
            line12: ui.nestedFields.technical({ bind: 'status' }),
            line13: ui.nestedFields.technical({ bind: 'isStockDetailRequired' }),
            line14: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            line15: ui.nestedFields.technical({ bind: 'taxEngine' }),
            line16: ui.nestedFields.technical({ bind: 'taxCalculationStatus' }),
            line17: ui.nestedFields.technical({ bind: 'returnStatus' }),
            totalLinesCount: ui.nestedFields.aggregate<PurchaseReceipt>({
                aggregateOn: '_id',
                aggregationMethod: 'distinctCount',
                bind: 'lines',
                isHidden: true,
            }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'invoiced', 'returned'] } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
            { title: 'Partially invoiced', graphQLFilter: { displayStatus: { _eq: 'partiallyInvoiced' } } },
            { title: 'Invoiced', graphQLFilter: { displayStatus: { _eq: 'invoiced' } } },
            { title: 'Partially returned', graphQLFilter: { displayStatus: { _eq: 'partiallyReturned' } } },
            { title: 'Returned', graphQLFilter: { displayStatus: { _eq: 'returned' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
        ],
        dropdownActions: [
            {
                icon: 'none',
                title: 'Post stock',
                refreshesMainList: 'list',
                async onClick(rowId, rowItem) {
                    await actionFunctions.post({
                        purchaseReceiptPage: this,
                        recordNumber: rowItem?.number ?? '',
                        recordId: rowId,
                        taxCalculationStatus: rowItem.taxCalculationStatus,
                    });
                },

                isHidden(rowId, rowItem) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                                taxEngine: rowItem.taxEngine,
                                taxCalculationStatus: rowItem.taxCalculationStatus,
                                isStockDetailRequired: rowItem.isStockDetailRequired,
                                isCalledFromRecordPage: false,
                            },
                            recordId: rowId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Create return',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number) {
                        await actionFunctions.createPurchaseReturnAction({
                            isCalledFromRecordPage: false,
                            purchaseReceiptPage: this,
                            recordNumber: rowItem.number,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return (
                        displayButtons.isHiddenButtonCreatePurchaseReturnAction({
                            parameters: {
                                status: rowItem.status,
                                returnStatus: rowItem.returnStatus,
                                lines: Number(rowItem.lines),
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                                isRepost: this.isRepost,
                            },
                            recordId,
                            isDirty: false,
                        }) ?? false
                    );
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                icon: 'print',
                refreshesMainList: 'record',
                async onClick(recordId, rowItem) {
                    if (recordId && rowItem.number) {
                        await printPurchaseReceipt({
                            page: this,
                            number: rowItem.number,
                            _id: recordId,
                            status: rowItem.taxCalculationStatus,
                        });
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem) {
                    const { site, supplier } = await getSiteAndSupplier({
                        page: this,
                        siteId: rowItem.site?._id ?? '',
                        supplierId: rowItem.supplier?._id ?? '',
                    });
                    await actionFunctions.setDimensions({
                        purchaseReceiptPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        isRepost: this.isRepost,
                        site,
                        supplier,
                    });
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId, rowItem) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-purchasing/PurchaseReceipt',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<PurchaseReceiptNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class PurchaseReceipt
    extends ui.Page<GraphApi, PurchaseReceiptNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    displayTaxesClicked = false;

    currentSelectedLineId: string;

    isRepost: boolean;

    fromLandedCostPanel: boolean;

    sourceDocumentData: { financeTransactionSysId: string; numberOfSourceDocuments: number };

    financeIntegrationCheckResult: MutationResult;

    private readonly receiptStepSequenceCreate = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_creation',
        'Create',
    );

    private readonly receiptStepSequenceInvoice = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_invoicing',
        'Invoice',
    );

    private readonly receiptStepSequenceReturn = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_return',
        'Return',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        let currentInvoiceStatus: ui.StepSequenceStatus = 'incomplete';

        if (this.invoiceStatus.value === 'invoiced') {
            currentInvoiceStatus = 'complete';
        } else if (this.invoiceStatus.value === 'partiallyInvoiced') {
            currentInvoiceStatus = 'current';
        }

        if (this.returnStatus.value === 'returned') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                invoice: currentInvoiceStatus,
                return: 'complete',
            });
        }
        if (this.returnStatus.value === 'partiallyReturned') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                invoice: currentInvoiceStatus,
                return: 'current',
            });
        }
        if (this.returnStatus.value === 'notReturned') {
            return this._setStepSequenceStatusObject({
                create: this.stockTransactionStatus.value === 'completed' ? 'complete' : 'current',
                invoice: currentInvoiceStatus,
                return: 'incomplete',
            });
        }
        return this._setStepSequenceStatusObject({
            create: this.stockTransactionStatus.value === 'completed' ? 'complete' : 'current',
            invoice: currentInvoiceStatus,
            return: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [this.$standardOpenCustomizationPageWizardAction, this.post, this.createPurchaseReturn],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.customSave.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value, isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
        this.customSave.isHidden = displayButtons.isHiddenButtonSaveAction({
            isRepost: this.isRepost,
            isDirty,
        });

        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value, isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isHidden = displayButtons.isHiddenButtonCancelAction({
            isRepost: this.isRepost,
            isDirty,
        });

        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        this.manageDisplayButtonCreatePurchaseReturnAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonSelectFromPurchaseOrderLinesAction();
        this.manageDisplayLinePhantomRow();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: {
                status: this.status.value,
                stockTransactionStatus: this.stockTransactionStatus.value,
                taxEngine: this.taxEngine.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                lines: this.lines.value,
                isCalledFromRecordPage: true,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCreatePurchaseReturnAction(isDirty: boolean) {
        this.createPurchaseReturn.isHidden = displayButtons.isDisabledButtonCreatePurchaseReturnAction({
            parameters: {
                status: this.status.value,
                returnStatus: this.returnStatus.value,
                lines: this.lines.value?.length || 0,
                stockTransactionStatus: this.stockTransactionStatus.value,
                isRepost: this.isRepost,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: { status: this.status.value, site: this.site.value, supplier: this.businessRelation.value },
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: { site: this.site.value, supplier: this.businessRelation.value },
        });
    }

    private manageDisplayButtonSelectFromPurchaseOrderLinesAction() {
        this.selectFromPurchaseOrderLookup.isDisabled =
            displayButtons.isDisabledButtonSelectFromPurchaseOrderLinesAction({
                parameters: { site: this.site.value, supplier: this.businessRelation.value, status: this.status.value },
            });

        this.selectFromPurchaseOrderLookup.isHidden = displayButtons.isHiddenButtonSelectFromPurchaseOrderLinesAction({
            parameters: { site: this.site.value, supplier: this.businessRelation.value, status: this.status.value },
        });
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.status || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    getSerializedValues() {
        if (this.lines.value.length) {
            StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(this.lines.value, this.lines, 'receipt');
        }
        const { $detailPanel, ...values } = this.$.values;
        delete values.totalAmountExcludingTax;
        delete values.totalAmountExcludingTaxInCompanyCurrency;
        // TODO: Fix problem with addresses as not all are being saved
        delete values.returnAddress;
        this.getLineValues(values.lines);
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines: any) {
        if (lines) {
            lines.forEach((receiptTableLine: any) => {
                delete receiptTableLine.currency;
                delete receiptTableLine.totalMinusVatAmount;
                delete receiptTableLine.lineAmountExcludingTax;
                delete receiptTableLine.lineAmountExcludingTaxInCompanyCurrency;
                delete receiptTableLine.taxAmountAdjusted;
                delete receiptTableLine.location;
                delete receiptTableLine.quantityInStockUnit;
                if (+receiptTableLine._id > 0) {
                    delete receiptTableLine.unitToStockUnitConversionFactor;
                }
                if (receiptTableLine.purchaseOrderLine && !receiptTableLine.purchaseOrderLine.purchaseOrderLine) {
                    delete receiptTableLine.purchaseOrderLine;
                }
            });
        }
    }

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Save',
        isHidden: true,
        access: { bind: '$create' },
        async onClick() {
            if (!this._id.value) {
                this.isOverwriteNote.value = await setOverwriteNote(this, {
                    linesFromSingleDocument: this.linesFromSingleOrder(),
                    headerNotesChanged: this.headerNotesChanged(),
                    lineNotesChanged: this.lineNotesChanged(),
                    isTransferHeaderNote: this.orderIsTransferHeaderNote(),
                    isTransferLineNote: this.ordersIsTransferLineNote(),
                });
            }
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            if (this.$.recordId && this.status.value === 'draft') {
                this.financeIntegrationCheckResult = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseReceipt')
                    .mutations.financeIntegrationCheck(
                        {
                            wasSuccessful: true,
                            message: true,
                            validationMessages: { message: true, lineNumber: true, sourceDocumentNumber: true },
                        },
                        { purchaseReceipt: this.$.recordId, receiptNumber: this.number.value || '' },
                    )
                    .execute();

                if (!this.financeIntegrationCheckResult.wasSuccessful) {
                    this.$.showToast(
                        `**${ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_receipt__save_warnings',
                            'Warnings while saving:',
                        )}**\n${this.financeIntegrationCheckResult.message}`,
                        { type: 'warning', timeout: 10000 },
                    );
                }
            }
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReceipt>({
        icon: 'search',
        title: 'Add lines from orders',
        isHidden: true,
        async onClick() {
            await addLineFromOrder(this);
        },
    })
    selectFromPurchaseOrderLookup: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Print',
        icon: 'print',
        async onClick() {
            await printPurchaseReceipt({
                page: this,
                number: this.number.value ?? '',
                _id: this.$.recordId ?? '',
                status: this.taxCalculationStatus.value ?? '',
            });
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Save',
        buttonType: 'primary',
        onError(error) {
            this.$.loader.isHidden = true;
            return formatError(this, error);
        },
        async onClick() {
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            if (this.$.recordId && this.status.value === 'draft') {
                this.financeIntegrationCheckResult = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseReceipt')
                    .mutations.financeIntegrationCheck(
                        {
                            wasSuccessful: true,
                            message: true,
                            validationMessages: { message: true, lineNumber: true, sourceDocumentNumber: true },
                        },
                        { purchaseReceipt: this.$.recordId },
                    )
                    .execute();

                if (!this.financeIntegrationCheckResult.wasSuccessful) {
                    this.$.showToast(
                        `**${ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_receipt__save_warnings',
                            'Warnings while saving:',
                        )}**\n${this.financeIntegrationCheckResult.message}`,
                        { type: 'warning', timeout: 10000 },
                    );
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Post stock',
        isHidden: true,
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_receipt_post__tax_calculation_failed',
                        'You need to correct lines that failed the tax calculation before you can post.',
                    ),
                );
            }
            if (this.lines.value.some(line => line.stockDetailStatus === 'required')) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_receipt_post__stock_details_required',
                        'You need to enter stock details for all lines before you can post.',
                    ),
                );
            }
            let postReceipt = true;

            if (this.lines.value.some(line => Number(line.netPrice) === 0)) {
                if (
                    !(await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_title',
                            'Confirm posting of lines with values equal to zero',
                        ),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_content',
                            'You are about to post this purchase receipt with lines that have values equal to zero.',
                        ),
                        ui.localize('@sage/xtrem-purchasing/pages-confirm-post', 'Post'),
                    ))
                ) {
                    postReceipt = false;
                }
            }

            this.$.loader.isHidden = false;

            if (postReceipt) {
                // post mutation of the node called to trigger the accounting interface
                await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseReceipt')
                    .mutations.post(true, { receipt: this._id.value ?? '' })
                    .execute();

                await this.checkForUpdate();
            }
            this.$.loader.isHidden = true;
        },
    })
    post: ui.PageAction;

    async checkForUpdate() {
        let refreshCounter = 0;
        this.post.isHidden = true;
        const checkForUpdate = async () => {
            try {
                await this.displayStatus.refresh();
                if (['received', 'error'].includes(this.displayStatus.value ?? '')) {
                    this.$.setPageClean();
                    await this.$.router.refresh(true); // true overrides the dirty checks

                    this.post.isHidden = this.displayStatus.value === 'received';

                    await this.$.refreshNavigationPanel();
                    this.$.loader.isHidden = true;
                    return;
                }
                refreshCounter += 1;
                if (refreshCounter < 5) {
                    // eslint-disable-next-line
                    setTimeout(checkForUpdate, 1000);
                } else {
                    await this.$.router.refresh(true); // true overrides the dirty checks
                    this.post.isHidden = true;
                    await this.$.refreshNavigationPanel();
                    this.$.loader.isHidden = true;
                }
            } catch {
                this.$.loader.isHidden = true;
            }
        };
        await checkForUpdate();
    }

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            let saveOnly = this.fromLandedCostPanel;

            if (this.sourceDocumentData.numberOfSourceDocuments > 1) {
                saveOnly = !(await confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_receipt__repost', 'Repost'),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_receipt__just_save',
                            'Do you want to repost now or after you edited dimensions on other source documents?',
                        ),
                        {
                            acceptButton: {
                                text: ui.localize('@sage/xtrem-purchasing/pages__purchase_receipt__repost', 'Repost'),
                            },
                            cancelButton: {
                                text: ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_receipt__save_only',
                                    'Save without posting',
                                ),
                            },
                        },
                    ),
                ));
            }

            const receiptLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReceipt')
                .mutations.repost(
                    { wasSuccessful: true, message: true },
                    {
                        purchaseReceipt: this._id.value || '',
                        receiptLines,
                        saveOnly,
                        landedCostControl: this.fromLandedCostPanel,
                        ...(this.sourceDocumentData.financeTransactionSysId
                            ? { financeTransaction: this.sourceDocumentData.financeTransactionSysId }
                            : {}),
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `** ${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_receipt__repost_errors',
                        'Errors occurred while reposting:',
                    )} **\n ${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.textField<PurchaseReceipt>({ isHidden: true })
    _id: ui.fields.Text;

    @ui.decorators.section<PurchaseReceipt>({ title: 'General', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Information', isTitleHidden: true })
    informationSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Information',
        isTitleHidden: true,
        width: 'large',
        parent() {
            return this.informationSection;
        },
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Receiving', isTitleHidden: true })
    receivingSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Financial', isTitleHidden: true })
    financialSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Totals', isTitleHidden: true })
    totalsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Landed costs', isTitleHidden: true })
    landedCostsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Returns', isTitleHidden: true })
    returnsSection: ui.containers.Section;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<PurchaseReceipt>({
        title: 'Posting',
        isHidden() {
            return !(this.site.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReceipt>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseReceipt, accountingInterfaces.FinancePostingStatusData>({
        title: 'Results',
        canSelect: false,
        isTransient: true,
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'documentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'documentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.documentSysId ?? '',
                        number: rowData?.documentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.status),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationApp' }),
            ui.nestedFields.technical({ bind: 'hasFinanceIntegrationApp' }),
            ui.nestedFields.technical({ bind: 'externalLink' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationAppUrl' }),
            ui.nestedFields.technical({ bind: 'documentSysId' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<accountingInterfaces.FinancePostingStatusData>;

    @ui.decorators.textAreaField<PurchaseReceipt>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<PurchaseReceipt>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'purchaseReceipt',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.section<PurchaseReceipt>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    notesBlock: ui.containers.Block;

    @ui.decorators.richTextField<PurchaseReceipt>({
        parent() {
            return this.notesBlock;
        },
        width: 'large',
        title: 'Internal notes',
        capabilities: validCapabilities,
        isFullWidth: true,
        helperText: 'Notes display on internal documents.',
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseReceipt>({
        parent() {
            return this.notesBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseReceipt>({
        parent() {
            return this.notesBlock;
        },
        title: 'Repeat the document notes on new documents.',
        width: 'large',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseReceipt>({
        parent() {
            return this.notesBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        width: 'large',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Financial',
        isTitleHidden: true,
        parent() {
            return this.financialSection;
        },
    })
    financialBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Returns',
        isTitleHidden: true,
        parent() {
            return this.returnsSection;
        },
        width: 'large',
    })
    returnsBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseReceipt>({
        width: 'large',
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<PurchaseReceipt>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.podField<PurchaseReceipt, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Supplier address',
        width: 'small',
        bind: 'supplierAddress',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Supplier address',
                isTitleHidden: true,
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
    })
    supplierAddress: ui.fields.Pod<Address>;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Carriage',
        parent() {
            return this.receivingSection;
        },
        width: 'large',
    })
    carriageBlock: ui.containers.Block;

    @ui.decorators.podField<PurchaseReceipt, Address>({
        parent() {
            return this.receivingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Receiving address',
        width: 'small',
        bind: 'receivingAddress',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Receiving address',
                isTitleHidden: true,
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
    })
    receivingAddress: ui.fields.Pod<Address>;

    @ui.decorators.podField<PurchaseReceipt, Address>({
        parent() {
            return this.returnsSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Return to address',
        width: 'small',
        bind: 'returnAddress',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Return-to address',
                isTitleHidden: true,
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
    })
    returnAddress: ui.fields.Pod<Address>;

    @ui.decorators.stepSequenceField<PurchaseReceipt>({
        width: 'small',
        isTransient: true,
        isFullWidth: true,
        parent() {
            return this.headerBlock;
        },
        options() {
            return [this.receiptStepSequenceCreate, this.receiptStepSequenceInvoice, this.receiptStepSequenceReturn];
        },
    })
    receiptStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<PurchaseReceipt, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receiving site',
        lookupDialogTitle: 'Select receiving site',
        minLookupCharacters: 0,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.reference({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        valueField: 'name',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseReceipt, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseReceipt, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.reference<PurchaseReceipt, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        tunnelPage: '@sage/xtrem-master-data/Currency',
                        title: 'Currency',
                        bind: 'currency',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ bind: 'decimalDigits', title: 'Decimal places' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                    ui.nestedFields.technical({ bind: 'doStockPosting' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
            ui.nestedFields.reference<PurchaseReceipt, Site, BusinessEntityAddress>({
                title: 'Primary address',
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: '_id',
                columns: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.technical<PurchaseReceipt, Site, StockStatus>({
                bind: 'defaultStockStatus',
                node: '@sage/xtrem-stock-data/StockStatus',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<PurchaseReceipt, Site, Location>({
                bind: 'defaultLocation',
                node: '@sage/xtrem-master-data/Location',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        isReadOnly() {
            return !!this.$.recordId || !!this.lines.value.find(line => line.origin === 'purchaseOrder');
        },
        async onChange() {
            await this.$.fetchDefaults(['receivingAddress']);
            this.manageDisplayButtonSelectFromPurchaseOrderLinesAction();
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonDefaultDimensionAction();
            this.showHideColumns();
            if (this.site.value?.legalCompany) {
                this.taxEngine.value =
                    this.site.value.legalCompany.legislation?.id === 'US'
                        ? 'genericTaxCalculation'
                        : String(this.site.value.legalCompany.taxEngine);
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value;
                if (this.businessRelation.value) {
                    this._defaultDimensionsAttributes = await initDefaultDimensions({
                        page: this,
                        dimensionDefinitionLevel: 'purchasingDirect',
                        site: this.site.value,
                        supplier: this.businessRelation.value,
                    });
                }
            }
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
            });
            this.businessRelation.focus();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseReceipt, Supplier>({
        parent() {
            return this.headerBlock;
        },
        title: 'Supplier',
        lookupDialogTitle: 'Select supplier',
        minLookupCharacters: 3,
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseReceipt, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseReceipt, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
        ],
        async onChange() {
            if (this.businessRelation.value?.businessEntity?.currency) {
                this.currency.value = this.businessRelation.value.businessEntity.currency;
            }
            await this.$.fetchDefaults(['supplierAddress', 'returnAddress']);
            this.manageDisplayButtonSelectFromPurchaseOrderLinesAction();
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonDefaultDimensionAction();
            this.showHideColumns();
            if (this.site.value && this.businessRelation.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'purchasingDirect',
                    site: this.site.value,
                    supplier: this.businessRelation.value,
                });
            }
        },
        isReadOnly() {
            return !!this.$.recordId || !!this.lines.value.find(line => line.origin === 'purchaseOrder');
        },
    })
    businessRelation: ui.fields.Reference<Supplier>;

    @ui.decorators.textField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.labelField<PurchaseReceipt>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.referenceField<PurchaseReceipt, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency (transaction)',
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCurrency();
            this.manageDisplayButtonSelectFromPurchaseOrderLinesAction();
            this.showHideColumns();
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.referenceField<PurchaseReceipt, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Currency (company)',
        lookupDialogTitle: 'Select currency (company)',
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length || 0) !== 0;
        },
        isHidden: true,
    })
    companyCurrency: ui.fields.Reference<Currency>;

    private applyCompanyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
        this.totalAmountIncludingTaxInCompanyCurrency.unit = this.siteCurrency;
        this.totalAmountExcludingTaxInCompanyCurrency.unit = this.siteCurrency;
    }

    @ui.decorators.dateField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden: true,
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseReceipt>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.businessRelation.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.dateField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receipt date',
        fetchesDefaults: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        validation(val) {
            return laterThanToday(
                val,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_receipt__receipt_date_cannot_be_future',
                    'The receipt date cannot be later than today.',
                ),
            );
        },
    })
    receiptDate: ui.fields.Date;

    @ui.decorators.textField<PurchaseReceipt>({
        parent() {
            return this.informationBlock;
        },
        title: 'Supplier packing slip',
        isReadOnly() {
            return !!this.$.recordId && this.status.value !== 'draft';
        },
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.labelField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Status',
        bind: 'status',
        isHidden: true,
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label<PurchaseDocumentStatus>;

    @ui.decorators.labelField<PurchaseReceipt>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            } else if (this.stockTransactionStatus.value === 'error') {
                await StockDataUtils.onStockTransactionStatusClick(
                    this,
                    this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                    { origin: 'header', tableField: this.lines },
                );
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.dropdownListField<PurchaseReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.labelField<PurchaseReceipt>({
        parent() {
            return this.returnsBlock;
        },
        title: 'Return status',
        optionType: '@sage/xtrem-purchasing/PurchaseReceiptReturnStatus',
        style() {
            return PillColorPurchase.getLabelColorByStatus('PurchaseReceiptReturnStatus', this.returnStatus.value);
        },
    })
    returnStatus: ui.fields.Label<PurchaseReceiptReturnStatus>;

    @ui.decorators.labelField<PurchaseReceipt>({
        parent() {
            return this.financialBlock;
        },
        title: 'Invoice status',
        optionType: '@sage/xtrem-purchasing/PurchaseReceiptInvoiceStatus',
        style() {
            return PillColorPurchase.getLabelColorByStatus('PurchaseReceiptInvoiceStatus', this.invoiceStatus.value);
        },
    })
    invoiceStatus: ui.fields.Label<PurchaseReceiptInvoiceStatus>;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Totals',
        isTitleHidden: true,
        parent() {
            return this.totalsSection;
        },
        width: 'large',
    })
    totalsBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.totalsBlock;
        },
        title: 'Excluding tax',
        isReadOnly: true,
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        title: 'Tax',
        parent() {
            return this.totalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        title: 'Total tax adjusted',
        parent() {
            return this.totalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.totalsBlock;
        },
        title: 'Including tax',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseReceipt, PurchaseDocumentTaxBinding>({
        title: 'Summary by tax',
        width: 'large',
        canSelect: false,
        isReadOnly: true,
        pageSize: 10,
        node: '@sage/xtrem-purchasing/PurchaseDocumentTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory' }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax' }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate' }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.checkbox({ title: 'Reverse charge', bind: 'isReverseCharge', size: 'small' }),
        ],
    })
    taxes: ui.fields.Table<PurchaseDocumentTax>;

    @ui.decorators.block<PurchaseReceipt>({
        title: 'Amounts company currency',
        parent() {
            return this.totalsSection;
        },
        width: 'large',
    })
    amountsInCompanyCurrencyBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.amountsInCompanyCurrencyBlock;
        },
        title: 'Excluding tax',
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.amountsInCompanyCurrencyBlock;
        },
        title: 'Including tax',
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.block<PurchaseReceipt>({
        parent() {
            return this.landedCostsSection;
        },
        title: 'Total in company currency',
        width: 'large',
    })
    landedCostsSectionBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseReceipt>({
        title: 'Actual landed costs',
        isTransient: true,
        parent() {
            return this.landedCostsSectionBlock;
        },
        scale: null,
        unit() {
            return this.companyCurrency.value;
        },
        isReadOnly: true,
    })
    totalActualLandedCostsInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseReceipt>({ isHidden: true, bind: 'jsonAggregateLandedCostTypes' })
    jsonAggregateLandedCostTypes: ui.fields.Text;

    @ui.decorators.tableField<PurchaseReceipt, LandedCostInterfaces.LandedCostTypeSummaryLineBinding>({
        parent() {
            return this.landedCostsSection;
        },
        canSelect: false,
        title: 'Summary by landed cost type',
        isFullWidth: true,
        isTransient: true,
        isReadOnly: true,
        pageSize: 10,
        orderBy: { landedCostType: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Type',
                bind: 'landedCostType',
                optionType: '@sage/xtrem-landed-cost/LandedCostType',
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost amount in company currency',
                bind: 'actualCostAmountInCompanyCurrency',
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return this.companyCurrency.value?.decimalDigits ?? 2;
                },
            }),
        ],
    })
    landedCosts: ui.fields.Table<LandedCostInterfaces.LandedCostTypeSummaryLineBinding>;

    /**
     * General section continuation
     */
    @ui.decorators.labelField<PurchaseReceipt>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.headerBlock;
        },
        isHidden: true,
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.countField<PurchaseReceipt>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    purchaseReceiptLineCount: ui.fields.Count;

    @ui.decorators.aggregateField<PurchaseReceipt>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'lineAmountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    totalExcludingTaxValue: ui.fields.Aggregate;

    @ui.decorators.aggregateField<PurchaseReceipt>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'lineAmountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    totalIncludingTaxValue: ui.fields.Aggregate;

    @ui.decorators.tableField<PurchaseReceipt, PurchaseReceiptLineBinding>({
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        onRowAdded() {
            this.disableHeaderFields();
        },
        columns: [
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Site>({
                minLookupCharacters: 0,
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseReceipt, Site, Company>({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Site>({
                minLookupCharacters: 0,
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseReceipt, Site, Company>({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: { description: true } })],
                    }),
                    ui.nestedFields.technical({ bind: 'isPurchase' }),
                    ui.nestedFields.technical({ bind: 'isInventory' }),
                    ui.nestedFields.technical<PurchaseReceipt, Site, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
                    }),
                ],
                filter(rowData) {
                    return rowData
                        ? { legalCompany: { _id: { _eq: rowData.site.legalCompany._id } } }
                        : { _id: { _ne: '' } };
                },
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' ||
                        this.taxEngine.value !== 'genericTaxCalculation' ||
                        this.isRepost
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLine>) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.label<PurchaseReceipt, PurchaseReceiptLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value ?? '');
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentLineOrigin',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                fetchesDefaults: true,
                isDisabled(_value, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding> | undefined) {
                    if (rowData && Number(rowData._id) < 0) {
                        return false;
                    }
                    return (
                        rowData?.origin === 'purchaseOrder' ||
                        Number(rowData?._id) > 0 ||
                        Number(rowData?.jsonStockDetails?.length) > 0 ||
                        this._isLineDisabled(rowData ?? {})
                    );
                },
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'isExpiryManaged' }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical<PurchaseReceipt, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                ],
                orderBy: { name: -1, stockUnit: { id: +1 } },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    if (rowData.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        rowData.unit = await getPurchaseUnit(
                            this.$.graph,
                            rowData.item._id ?? '',
                            this.businessRelation.value?._id ?? '',
                        );
                        const quantityToConvert = rowData.quantity ? rowData.quantity : 1;

                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit?._id ?? '',
                            rowData.item.stockUnit?._id ?? '',
                            +quantityToConvert,
                            rowData.item._id,
                            this.businessRelation.value?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
                        }
                        rowData.stockDetailStatus = rowData.item.isStockManaged ? 'required' : 'notRequired';
                    }

                    if (this.site.value && this.businessRelation.value && rowData.item) {
                        const { storedAttributes, storedDimensions } =
                            await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                page: this,
                                _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                dimensionDefinitionLevel: 'purchasingDirect',
                                site: this.site.value,
                                supplier: this.businessRelation.value,
                                item: rowData.item,
                            });
                        rowData.storedAttributes = storedAttributes;
                        rowData.storedDimensions = storedDimensions;
                    }

                    await setPriceOrigin(this, rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                helperTextField: 'name',
                isMandatory: true,
                fetchesDefaults: true,
                isHiddenOnMainField: true,
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct';
                },
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    const quantityToConvert = rowData.quantity ? rowData.quantity : 1;

                    const conversion = await convertFromTo(
                        this.$.graph,
                        rowData.unit?._id ?? '',
                        rowData.item?.stockUnit?._id ?? '',
                        +quantityToConvert,
                        rowData.item?._id ?? '',
                        this.businessRelation.value?._id ?? '',
                        '',
                        'purchase',
                        false,
                    );
                    rowData.stockUnit = rowData.item?.stockUnit ?? {};
                    rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
                    if (rowData.quantity) {
                        rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                    await setPriceOrigin(this, rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol || '',
                isReadOnly: (_rowId, rowData) => rowData?.status !== 'draft',
                onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    return onChangeLineQuantity(this, rowData);
                },
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol || '',
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),

            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                isHiddenOnMainField: true,
                title: 'Stock unit conversion factor',
                isReadOnly: true,
                width: 'large',
                scale(_rowId, rowData) {
                    let scale = 2;
                    if ((rowData?.unitToStockUnitConversionFactor ?? '').toString().split('.').length > 1) {
                        scale = rowData?.unitToStockUnitConversionFactor.toString().split('.')[1].length ?? 2;
                    }
                    return scale;
                },
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.link({
                bind: 'poLink' as any,
                title: 'Purchase order link',
                isTitleHidden: true,
                isTransient: true,
                isExcludedFromMainField: true,
                map(_id, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    let url = '';
                    if (rowData.purchaseOrderLine && rowData.purchaseOrderLine.purchaseOrderLine) {
                        url = rowData.purchaseOrderLine.purchaseOrderLine.document?.number ?? '';
                    } else if (rowData.purchaseOrderLine?.purchaseOrderLine?.document?.number) {
                        url = rowData.purchaseOrderLine.purchaseOrderLine.document.number;
                    }
                    return `${url}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseOrder',
                queryParameters(_value, rowData) {
                    let poId = '';
                    if (
                        (rowData?.purchaseOrderLine && rowData?.purchaseOrderLine.purchaseOrderLine) ||
                        rowData?.purchaseOrderLine?.purchaseOrderLine?.document?._id
                    ) {
                        poId = rowData?.purchaseOrderLine.purchaseOrderLine.document._id;
                    }
                    return { _id: poId };
                },
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Location>({
                title: 'Location',
                lookupDialogTitle: 'Select location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                bind: 'location' as any,
                isTransient: true,
                minLookupCharacters: 3,
                isExcludedFromMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        if (+(rowData._id ?? '') < 0 && rowData.origin === 'purchaseOrder') {
                            return false;
                        }
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                filter() {
                    return { site: this.site.value?._id };
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseReceipt, Location, Location['site']>({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, StockStatus>({
                title: 'Status',
                lookupDialogTitle: 'Select status',
                node: '@sage/xtrem-stock-data/StockStatus',
                tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
                bind: 'stockStatus' as any,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        if (+(rowData._id ?? '') < 0 && rowData.origin === 'purchaseOrder') {
                            return false;
                        }
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                isTransient: true,
                minLookupCharacters: 1,
                isExcludedFromMainField: true,
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Lot>({
                title: 'Lot',
                lookupDialogTitle: 'Select lot',
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'id',
                isTransient: true,
                bind: 'lot' as any,
                isExcludedFromMainField: true,
                isHidden(_rowId, row) {
                    if (row && isUiPartialCollectionValuePurchaseReceiptLineBinding(row)) {
                        return row.item?.lotManagement === 'notManaged';
                    }
                    return false;
                },
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        if (+(rowData._id ?? '') < 0 && rowData.origin === 'purchaseOrder') {
                            return false;
                        }
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                filter(rowValue) {
                    return { item: rowValue.item?._id };
                },

                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference({
                        bind: 'item',
                        title: 'Item name',
                        valueField: 'name',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                    }),
                    ui.nestedFields.text({ bind: 'expirationDate', title: 'Expiration date' }),
                    ui.nestedFields.technical({ bind: 'sublot' }),
                    ui.nestedFields.technical({ bind: 'supplierLot' }),
                ],
                width: 'small',
                // TODO: figure out how to work with strong typing and transient properties...
                onChange: (_rowId, rowData) => {
                    rowData.expirationDate = rowData.item?.isExpiryManaged ? rowData.lot?.expirationDate : undefined;
                },
            }),
            ui.nestedFields.date({
                title: 'Expiration date',
                bind: 'expirationDate' as any,
                isTransient: true,
                isExcludedFromMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        if (+(rowData._id ?? '') < 0 && rowData.origin === 'purchaseOrder') {
                            return false;
                        }
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                isHidden(_rowId, row) {
                    if (row && isUiPartialCollectionValuePurchaseReceiptLineBinding(row)) {
                        return !row.item?.isExpiryManaged;
                    }
                    return false;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                scale() {
                    return getCompanyPriceScale(this.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    if (rowData) {
                        rowData.priceOrigin = rowData.grossPrice ? 'manual' : undefined;
                        this.lines.addOrUpdateRecordValue(await calculatePrices(this, rowData));
                    }
                },
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct' || this._isLineDisabled(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return (
                            rowData.origin !== 'direct' ||
                            isPurchaseReceiptLinePropertyDisabled(this.status.value ?? '', rowData.status ?? '')
                        );
                    }
                    return false;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    this.lines.addOrUpdateRecordValue(await calculatePrices(this, rowData));
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return (
                            rowData.origin !== 'direct' ||
                            isPurchaseReceiptLinePropertyDisabled(this.status.value ?? '', rowData.status ?? '')
                        );
                    }
                    return false;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    this.lines.addOrUpdateRecordValue(await calculatePrices(this, rowData));
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                scale() {
                    return getCompanyPriceScale(this.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label<PurchaseReceipt, PurchaseReceiptLineBinding>({
                title: 'Price origin',
                bind: 'priceOrigin',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                isReadOnly: true,
                bind: 'lineAmountExcludingTax',
                scale: null,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                isReadOnly: true,
                isExcludedFromMainField: true,
                bind: 'lineAmountExcludingTaxInCompanyCurrency',
                unit() {
                    return this.siteCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                isReadOnly: true,
                scale: null,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'lineAmountIncludingTax',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'lineAmountIncludingTaxInCompanyCurrency',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Actual landed cost in company currency',
                bind: 'actualLandedCostInCompanyCurrency',
                width: 'small',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isHidden() {
                    return this.status.value === 'draft';
                },
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),

            ui.nestedFields.label({
                title: 'Return status',
                bind: 'lineReturnStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptReturnStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseReceiptReturnStatus', rowData?.lineReturnStatus),
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'lineInvoiceStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptInvoiceStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseReceiptInvoiceStatus', rowData?.lineInvoiceStatus),
            }),
            ui.nestedFields.label<PurchaseReceipt, PurchaseReceiptLineBinding>({
                title: 'Stock detail status',
                bind: 'stockDetailStatus',
                optionType: '@sage/xtrem-stock-data/StockDetailStatus',
                isHidden() {
                    return ['inProgress', 'completed'].includes(this.stockTransactionStatus.value ?? '');
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockDetailStatus', rowData?.stockDetailStatus),
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseReceiptLineBinding, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isExcludedFromMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return this._isLineDisabled(rowData);
                    }
                    return false;
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ISO 4217 code', bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
                filter: { isActive: true },
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { text: { value: true } } }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<
                PurchaseReceipt,
                PurchaseReceiptLineBinding,
                PurchaseOrderLineToPurchaseReceiptLine
            >({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
                nestedFields: [
                    ui.nestedFields.reference({
                        bind: 'purchaseOrderLine',
                        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                        valueField: { document: { number: true } },
                        columns: [
                            ui.nestedFields.reference({
                                bind: 'document',
                                node: '@sage/xtrem-purchasing/PurchaseOrder',
                                valueField: 'number',
                                columns: [
                                    ui.nestedFields.text({ title: 'Number', bind: 'number' }),
                                    ui.nestedFields.dropdownList({ bind: 'status' }),
                                    ui.nestedFields.reference({
                                        bind: 'supplierAddress',
                                        node: '@sage/xtrem-master-data/Address',
                                        valueField: 'name',
                                        columns: [
                                            ui.nestedFields.technical({ bind: '_id' }),
                                            ui.nestedFields.technical({ bind: 'name' }),
                                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                                            ui.nestedFields.technical({ bind: 'city' }),
                                            ui.nestedFields.technical({ bind: 'region' }),
                                            ui.nestedFields.technical({ bind: 'postcode' }),
                                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                                            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                                            ui.nestedFields.technical({ bind: 'concatenatedAddressWithoutName' }),
                                        ],
                                    }),
                                ],
                            }),
                            ui.nestedFields.numeric({ bind: 'quantityToReceive' }),
                            ui.nestedFields.numeric({ bind: 'quantity' }),
                            ui.nestedFields.numeric({ bind: 'receivedQuantity' }),
                            ui.nestedFields.label({
                                bind: 'status',
                                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                            }),
                            ui.nestedFields.reference({
                                bind: 'stockSiteAddress',
                                node: '@sage/xtrem-master-data/Address',
                                valueField: 'name',
                                columns: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                                    ui.nestedFields.technical({ bind: 'city' }),
                                    ui.nestedFields.technical({ bind: 'region' }),
                                    ui.nestedFields.technical({ bind: 'postcode' }),
                                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                                    ui.nestedFields.technical({ bind: 'concatenatedAddressWithoutName' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        title: 'Purchase receipt line',
                        bind: 'purchaseReceiptLine',
                        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                        valueField: { document: { number: true } },
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric({
                        title: 'Received quantity',
                        bind: { purchaseReceiptLine: { quantity: true } },
                        canFilter: false,
                    }),
                    ui.nestedFields.technical({
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<
                PurchaseReceipt,
                PurchaseReceiptLineBinding,
                PurchaseOrderLineToPurchaseReceiptLine
            >({
                title: 'Received quantity',
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
                valueField: { purchaseReceiptLine: { quantity: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'All open statuses', graphQLFilter: { status: { _ne: 'closed' } } },
            { title: 'Stock details required', graphQLFilter: { stockDetailStatus: { _eq: 'required' } } },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: `Stock details`,
                isHidden(_rowId, line) {
                    return (
                        (line &&
                            line.item &&
                            (line.item.type === 'service' ||
                                (line.item.type === 'good' && line.item.isStockManaged === false))) ??
                        false
                    );
                },
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    await this.editStockDetails(rowItem);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable:
                                    !isPurchaseReceiptLineActionDisabled(
                                        this.status.value ?? '',
                                        rowItem.status ?? '',
                                        'dimensions',
                                    ) || this.isRepost,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                isHidden(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    return !rowItem.uiTaxes || this.isRepost;
                },
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            {
                icon: 'none',
                title: 'Landed costs',
                isHidden(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    return (
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !rowItem.actualLandedCostInCompanyCurrency ||
                        this.status.value === 'draft'
                    );
                },
                async onClick(rowId) {
                    await this.displayLandedCosts(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled(_value, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    return this._isLineDisabled(rowData);
                },
                async onClick(rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_content',
                                'You are about to delete this purchase receipt line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        this.manageDisplayButtonSelectFromPurchaseOrderLinesAction();
                        await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                        await TotalTaxCalculator.getInstance().updateTaxDetails(
                            this.taxes,
                            this.totalTaxAmountAdjusted,
                            this.totalTaxAmount,
                        );
                        this._computeTotalAmounts();
                        refreshTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                        this.disableHeaderFields();
                    }
                },
            },
        ],
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromPurchaseOrderLookup],
            });
        },

        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({
                bind: 'itemDescription',
                title: 'Description',
                canFilter: false,
            }),
            line2Right: ui.nestedFields.numeric({
                bind: 'lineAmountIncludingTax',
                title: 'Amount',
                canFilter: false,
                scale: null,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? '') < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: `Stock details`,
                    isHidden(_rowId, line) {
                        return (
                            (line &&
                                line.item &&
                                (line.item.type === 'service' ||
                                    (line.item.type === 'good' && line.item.isStockManaged === false))) ??
                            false
                        );
                    },
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        await this.editStockDetails(rowItem);
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable:
                                        !isPurchaseReceiptLineActionDisabled(
                                            this.status.value ?? '',
                                            rowItem.status ?? '',
                                            'dimensions',
                                        ) || this.isRepost,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden(_rowId, recordValue: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        return !recordValue.uiTaxes || this.isRepost;
                    },
                    async onClick(_rowId, recordValue: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        if (recordValue.uiTaxes) {
                            await this.callDisplayTaxes(recordValue);
                        }
                    },
                },
                {
                    icon: 'none',
                    title: 'Landed costs',
                    isHidden(_rowId, recordValue: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        return (
                            !this.$.isServiceOptionEnabled('landedCostOption') ||
                            !recordValue.actualLandedCostInCompanyCurrency
                        );
                    },
                    async onClick(rowId) {
                        await this.displayLandedCosts(rowId);
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isDisabled(_value, rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding>) {
                        return this._isLineDisabled(rowData);
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            // TODO:this function is force to be async
            // eslint-disable-next-line require-await
            async onRecordDiscarded() {
                this.$.setPageClean();
            },

            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.currentSelectedLineId = recordValue._id;
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    if (+recordValue._id > 0) {
                        if (recordValue.purchaseOrderLine?.purchaseOrderLine) {
                            this.purchaseOrderLines.value = [];
                            this.purchaseOrderLines.addOrUpdateRecordValue(
                                (recordValue?.purchaseOrderLine as unknown as ExtractEdgesPartial<PurchaseOrderLineToPurchaseReceiptLineBinding>) ??
                                    {},
                            );
                        }
                        await this.loadRelatedPurchaseReturnLines(_id);
                        await this.loadRelatedPurchaseInvoiceLines(_id);
                    } else if (
                        this._defaultDimensionsAttributes.dimensions !== '{}' ||
                        this._defaultDimensionsAttributes.attributes !== '{}'
                    ) {
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            {
                                site: this.site.value,
                                currency: this.currency.value,
                                status: 'draft',
                                lineReturnStatus: 'notReturned',
                                lineInvoiceStatus: 'notInvoiced',
                                origin: 'direct',
                                stockSite: this.site.value,
                                stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
                            } as ui.PartialNodeWithId<PurchaseReceiptLine>,
                            this._defaultDimensionsAttributes,
                        );
                        recordValue.storedAttributes = line.storedAttributes;
                        recordValue.storedDimensions = line.storedDimensions;
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<PurchaseReceiptLineBinding>,
                        );
                    }
                    this.$.setPageClean();
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<PurchaseReceiptLineBinding>,
                    );
                }
                await TotalTaxCalculator.getInstance().updateTaxDetails(
                    this.taxes,
                    this.totalTaxAmountAdjusted,
                    this.totalTaxAmount,
                );
                this._computeTotalAmounts();
                refreshTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                this.disableHeaderFields();
            },
            layout() {
                return {
                    general: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            statusBlock: { fields: ['lineReturnStatus', 'lineInvoiceStatus', 'stockDetailStatus'] },
                            mainBlock: { fields: ['item', 'origin', 'itemDescription'] },
                            siteBlock: { fields: ['site'] },
                            purchaseBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden(_id, recordValue) {
                                    return !recordValue?.item?.isStockManaged;
                                },
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            mainBlock2: { fields: ['priceOrigin'] },
                            totals: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['lineAmountExcludingTax', 'taxAmount', 'lineAmountIncludingTax'],
                            },
                            totals2: {
                                fields: [
                                    'lineAmountExcludingTaxInCompanyCurrency',
                                    'lineAmountIncludingTaxInCompanyCurrency',
                                    'actualLandedCostInCompanyCurrency',
                                ],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.purchaseOrderLines.value.length === 0;
                        },
                        blocks: { orderBlock: { fields: [this.purchaseOrderLines] } },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            return (
                                this.purchaseReturnLines.value.length === 0 &&
                                this.purchaseInvoiceLines.value.length === 0
                            );
                        },
                        blocks: {
                            invoiceBlock: {
                                isHidden() {
                                    return this.purchaseInvoiceLines.value.length === 0;
                                },
                                fields: ['lineInvoiceStatus', this.purchaseInvoiceLines],
                            },
                            returnBlock: {
                                isHidden() {
                                    return this.purchaseReturnLines.value.length === 0;
                                },
                                fields: ['lineReturnStatus', this.purchaseReturnLines],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: { notesBlock: { fields: [this.internalNoteLine] } },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<PurchaseReceiptLine>;

    private _isLineDisabled(
        rowData: ui.PartialCollectionValue<PurchaseReceiptLineBinding> | PurchaseReceiptLineBinding,
    ) {
        return (
            ['closed', 'inProgress'].includes(this.status.value ?? '') ||
            ['closed', 'pending', 'inProgress'].includes(rowData?.status ?? '')
        );
    }

    @ui.decorators.pageAction<PurchaseReceipt>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.referenceField<PurchaseReceipt, Supplier>({
        parent() {
            return this.carriageBlock;
        },
        title: 'Carrier',
        lookupDialogTitle: 'Select carrier',
        shouldSuggestionsIncludeColumns: true,
    })
    carrier: ui.fields.Reference<Supplier>;

    @ui.decorators.richTextField<PurchaseReceipt>({
        width: 'large',
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
    })
    internalNoteLine: ui.fields.RichText;

    /**
     *  Returns Tab
     */
    @ui.decorators.tableField<PurchaseReceipt, PurchaseReceiptLineToPurchaseReturnLine>({
        title: 'Purchase receipt line to purchase return line',
        isTitleHidden: true,
        isTransient: true,
        isHidden: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseReturnLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseReturnLine, PurchaseReturnLine>({
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { document: { number: true } } }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseReturnLine, PurchaseReturn>({
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseReturn',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseReturnLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lineAmountExcludingTax' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseReturnLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id',
                title: 'Return number',
                isTransient: true,
                isFullWidth: true,
                // isHidden: true, // TODO: see why the link is not clickable when readOnly??
                map(_fieldValue, rowData: ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseReturnLine>) {
                    return `${rowData.purchaseReturnLine?.document?.number}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseReturn',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.purchaseReturnLine.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.label({
                title: 'Return status',
                isTitleHidden: true,
                bind: { purchaseReturnLine: { status: true } },
                isTransient: true,
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(value, rowData: ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseReturnLine>) {
                    switch (rowData.purchaseReturnLine?.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseReturnLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseReturnLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseReturnLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseReturnLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseReturnLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity returned',
                isFullWidth: true,
                bind: 'returnedQuantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseReturnLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseReturnLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'returnedQuantityInStockUnit', isTransient: true }),
        ],
    })
    purchaseReturnLines: ui.fields.Table<PurchaseReceiptLineToPurchaseReturnLine>;

    @ui.decorators.tableField<PurchaseReceipt, PurchaseReceiptLineToPurchaseInvoiceLine>({
        title: 'Purchase receipt line to purchase invoice line',
        isTitleHidden: true,
        isTransient: true,
        isHidden: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseInvoiceLine>({
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseInvoiceLine, PurchaseInvoice>({
                        bind: { document: { number: true } },
                    }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseInvoiceLine, PurchaseInvoice>({
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseInvoice',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseInvoiceLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lineAmountExcludingTax' }),
                    ui.nestedFields.technical<PurchaseReceipt, PurchaseInvoiceLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id',
                title: 'Invoice number',
                isTransient: true,
                isFullWidth: true,
                // isHidden: true, // TODO: see why the link is not clickable when readOnly???
                map(_fieldValue, rowData: ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseInvoiceLine>) {
                    return `${rowData.purchaseInvoiceLine?.document?.number}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseInvoice',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.purchaseInvoiceLine.document._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                isTitleHidden: true,
                bind: { purchaseInvoiceLine: { document: { status: true } } },
                isTransient: true,
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(
                    value: PurchaseDocumentStatus,
                    rowData: ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseInvoiceLine>,
                ) {
                    switch (rowData.purchaseInvoiceLine?.document?.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__draft',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__posted',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__error',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseInvoiceLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity invoiced',
                isFullWidth: true,
                bind: 'invoicedQuantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseReceipt, PurchaseReceiptLineToPurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'invoicedQuantityInStockUnit' }),
        ],
    })
    purchaseInvoiceLines: ui.fields.Table<PurchaseReceiptLineToPurchaseReturnLine>;

    /**
     * Technical property that contains all receipt in progress lines
     * (used for filtering the ones related to the current receipt line when displaying)
     */
    allInProgressOrderLineToReceiptLines: any[];

    @ui.decorators.section<PurchaseReceipt>({ isHidden: true, title: 'On order quantity change' })
    onReceiptQuantityChangeSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReceipt>({
        parent() {
            return this.onReceiptQuantityChangeSection;
        },
    })
    onReceiptQuantityChangeBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.onReceiptQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Actual quantity',
        bind: 'actualOnReceiptQuantity',
        isReadOnly: true,
    })
    actualOnReceiptQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseReceipt>({
        parent() {
            return this.onReceiptQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'New quantity',
        bind: 'newOnReceiptQuantity',
    })
    newOnReceiptQuantity: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseReceipt, PurchaseOrderLineToPurchaseReceiptLineBinding>({
        title: 'Purchase order line to purchase receipt line',
        isTitleHidden: true,
        isTransient: true,
        canFilter: false,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
        columns: [
            ui.nestedFields.reference<PurchaseReceipt, PurchaseOrderLineToPurchaseReceiptLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                valueField: '_id',
                isHidden: true,
                columns: [],
            }),
            ui.nestedFields.link({
                bind: { purchaseOrderLine: { document: { number: true } } },
                page(_, rowValue) {
                    return rowValue?._id
                        ? linkToPage('@sage/xtrem-purchasing/PurchaseOrder', rowValue.purchaseOrderLine?.document?._id)
                        : '';
                },
                queryParameters(_, rowValue) {
                    return {
                        _id: rowValue?.purchaseOrderLine?.document?._id ?? '',
                        fromPurchaseReceiptPage: !!this.financeIntegrationCheckResult.validationMessages?.some(
                            lineValidation =>
                                lineValidation.sourceDocumentNumber === rowValue?.purchaseOrderLine?.document?.number,
                        ),
                    };
                },
            }),
            ui.nestedFields.label({
                title: 'Order line status',
                bind: { purchaseOrderLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                map(value, rowData: ui.PartialCollectionValue<PurchaseOrderLineToPurchaseReceiptLine>) {
                    switch (rowData.purchaseOrderLine?.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseOrderLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseOrderLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                isFullWidth: true,
                bind: { purchaseReceiptLine: { quantity: true } },
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.label({
                title: 'Available quantity to receive',
                isFullWidth: true,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isTransient: true,
                bind: '_id',
                map(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineToPurchaseReceiptLine>) {
                    const existingQuantity =
                        +(rowData._id ?? '') < 0 ? +(rowData.purchaseReceiptLine?.quantity ?? '') : 0;
                    const quantityToReceive = +(rowData.purchaseOrderLine?.quantityToReceive ?? '') - existingQuantity;
                    return (quantityToReceive < 0 ? 0 : quantityToReceive).toString();
                },
            }),
            ui.nestedFields.reference<PurchaseReceipt, PurchaseOrderLineToPurchaseReceiptLine, PurchaseOrderLine>({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                valueField: { document: { orderDate: true } },
                isTransient: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.reference<PurchaseReceipt, PurchaseOrderLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        columns: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.numeric({ title: 'Received order', bind: 'receivedQuantity' }),
                    ui.nestedFields.numeric({ title: 'Quantity to receive', bind: 'quantityToReceive' }),
                    ui.nestedFields.label({
                        title: 'Status',
                        bind: 'status',
                        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                        style: (_id, rowData) =>
                            PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
                    }),
                    ui.nestedFields.numeric({
                        title: 'Gross price',
                        bind: 'grossPrice',
                        scale() {
                            return getCompanyPriceScale(this.site?.value?.legalCompany);
                        },
                        prefix() {
                            return this.currency?.value?.symbol ?? '';
                        },
                    }),
                    ui.nestedFields.image({ bind: { item: { image: true } }, isHidden: true }),
                    ui.nestedFields.reference({
                        bind: 'stockSiteAddress',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddressWithoutName' }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        bind: { document: { supplierAddress: true } },
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddressWithoutName' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    purchaseOrderLines: ui.fields.Table<PurchaseOrderLineToPurchaseReceiptLineBinding>;

    @ui.decorators.checkboxField<PurchaseReceipt>({
        isTransient: true,
        bind: 'isStockDetailRequired',
        isHidden: true,
    })
    isStockDetailRequired: ui.fields.Checkbox;

    async initPage() {
        const oldIsDirty = this.$.isDirty;
        if (this.currency.value) {
            this.applyCurrency();
        }

        this.landedCostsSection.isHidden = true;
        if (this.$.recordId) {
            await this.loadRelatedPurchaseReturnLines('');
            await this.loadRelatedPurchaseInvoiceLines('');
            if (this.$.isServiceOptionEnabled('landedCostOption') && this.jsonAggregateLandedCostTypes.value) {
                this.landedCostsSection.isHidden = this.status.value === 'draft';
                const landedCostSummary = LandedCost.mapLandedCostSummary(
                    JSON.parse(
                        this.jsonAggregateLandedCostTypes.value,
                    ) as LandedCostInterfaces.LandedCostTypeSummaryLine[],
                );
                this.landedCosts.value = landedCostSummary.tableLandedCostsValues;
                this.totalActualLandedCostsInCompanyCurrency.value = landedCostSummary.total;
            }
        } else {
            this.receiptDate.value = '';
            this.supplierAddress.value = {};
            this.receivingAddress.value = {};
            this.returnAddress.value = {};
        }

        if (this.site.value && this.businessRelation.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
                supplier: this.businessRelation.value,
            });
        }
        this.allInProgressOrderLineToReceiptLines = [];

        this.internalNote.isDisabled = this.status.value === 'closed';

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }

        this.showHideColumns();
    }

    async delete() {
        await this.$.graph
            .delete()
            .then(() => {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_receipt__delete_confirmation',
                        'Record deleted',
                    ),
                    { type: 'success' },
                );
                this.$.setPageClean();
                this.$.router.goTo(`@sage/xtrem-purchasing/${this.$.page.id}`);
            })
            .catch((e: any) => {
                this.$.showToast(e.message, { timeout: 0, type: 'error' });
            })
            .finally(() => {
                this.$.loader.isHidden = true;
            });
    }

    applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
    }

    async loadRelatedPurchaseReturnLines(purchaseReceiptLineId: string) {
        const oldIsDirty = this.$.isDirty;
        const filter = { _and: [{}] };
        filter._and.push({ purchaseReceiptLine: { document: { _id: { _eq: this._id.value } } } });
        if (purchaseReceiptLineId) {
            filter._and.push({ purchaseReceiptLine: { _id: purchaseReceiptLineId } });
        }
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseReceiptLine: { _id: true, document: { _id: true } },
                            purchaseReturnLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, invoiceStatus: true },
                                status: true,
                                lineInvoiceStatus: true,
                                item: { _id: true, name: true },
                                quantity: true,
                                unit: { id: true, symbol: true },
                                grossPrice: true,
                                lineAmountExcludingTax: true,
                                quantityInStockUnit: true,
                            },
                            returnedQuantityInStockUnit: true,
                            returnedQuantity: true,
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        const uniqueLines = new Array<string>();
        this.purchaseReturnLines.value.forEach(podLine => {
            this.purchaseReturnLines.removeRecord(podLine._id);
        });
        result.forEach(row => {
            if (!uniqueLines.includes(row.purchaseReturnLine._id)) {
                uniqueLines.push(row.purchaseReturnLine._id);
                this.purchaseReturnLines.addRecord(row);
            }
        });
        this.purchaseReturnLines.isHidden = !this.purchaseReturnLines.value.length;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async loadRelatedPurchaseInvoiceLines(purchaseReceiptLineId: string) {
        const oldIsDirty = this.$.isDirty;
        const filter = { _and: [{}] };
        filter._and.push({ purchaseReceiptLine: { document: { _id: { _eq: this._id.value } } } });
        if (purchaseReceiptLineId) {
            filter._and.push({ purchaseReceiptLine: { _id: purchaseReceiptLineId } });
        }

        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseReceiptLine: { _id: true, document: { _id: true } },
                            purchaseInvoiceLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, status: true },
                                item: { _id: true, name: true },
                                quantity: true,
                                unit: { id: true, symbol: true },
                                grossPrice: true,
                                lineAmountExcludingTax: true,
                            },
                            invoicedQuantity: true,
                            invoicedQuantityInStockUnit: true,
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        const uniqueLines = new Array<string>();
        this.purchaseInvoiceLines.value.forEach(podLine => {
            this.purchaseInvoiceLines.removeRecord(podLine._id);
        });
        result.forEach(row => {
            if (!uniqueLines.includes(row.purchaseInvoiceLine._id)) {
                uniqueLines.push(row.purchaseInvoiceLine._id);
                this.purchaseInvoiceLines.addRecord(row);
            }
        });
        this.purchaseInvoiceLines.isHidden = !this.purchaseInvoiceLines.value.length;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Create return',
        isHidden: true,
        async onClick() {
            if (this.number.value && this._id.value) {
                await actionFunctions.createPurchaseReturnAction({
                    isCalledFromRecordPage: true,
                    purchaseReceiptPage: this,
                    recordNumber: this.number.value,
                    recordId: this._id.value,
                });
            }
        },
    })
    createPurchaseReturn: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReceipt>({
        title: 'Create invoice',
        isHidden: true,
        async onClick() {
            await createPurchaseInvoice(this, this.number.value ?? '');
        },
    })
    createPurchaseInvoice: ui.PageAction;

    /**
     * Stock detail management.
     * Calls the StockReceiptDetailsPanel and sets the line stockDetails to the return of the StockReceiptDetailsPanel
     */
    async editStockDetails(
        lineData: ui.PartialCollectionValue<
            PurchaseReceiptLine & { location: Location; stockStatus: StockStatus; lot: Lot; expirationDate: string }
        >,
    ) {
        const line = MasterDataUtils.removeExtractEdgesPartial(lineData);
        const location = await StockDataUtils.getDefaultLocation({
            defaultLocationType: 'inboundDefaultLocation',
            page: this,
            itemId: line.item?._id ?? '',
            site: this.site.value,
        });
        const stockStatus = await StockDataUtils.getDefaultQuality({
            defaultQualityType: 'inboundDefaultQuality',
            page: this,
            itemId: line.item?._id ?? '',
            site: this.site.value,
        });
        const isEditable = ['draft', 'error'].includes(line.stockTransactionStatus);
        await MasterDataUtils.applyPanelToLineIfChanged(
            this.lines,
            StockDetailHelper.editStockDetails(this as any, line, {
                movementType: 'receipt',
                data: {
                    fieldCustomizations: {
                        ok: { isDisabled: !isEditable },
                        date: {
                            isHidden: true,
                        },
                    },
                    isEditable,
                    effectiveDate: this.receiptDate.value ?? '',
                    documentLineSortValue: line._sortValue,
                    documentLine: line._id,
                    jsonStockDetails: line.jsonStockDetails,
                    item: line.item?._id,
                    stockSite: this.site.value?._id,
                    quantity: +line.quantityInStockUnit,
                    unit: line.item.stockUnit?._id,
                    stockStatus,
                    trackCheckStock: 'track',
                    location,
                    existingLot: undefined,
                    lotCreateData: undefined,
                    stockTransactionStatus: line.stockTransactionStatus,
                    supplier: this.businessRelation.value?._id,
                },
            }) as Promise<PurchaseReceiptLine>,
        );
    }

    private async callDisplayTaxes(rowItem: ui.PartialCollectionValue<PurchaseReceiptLine>, updateTaxDetails = true) {
        const result = await displayTaxes(
            this,
            rowItem,
            {
                currency: this.currency.value ?? {},
                taxAmount: +(rowItem?.taxAmount ?? 0),
                lineAmountExcludingTax: +(rowItem?.lineAmountExcludingTax ?? 0),
                lineAmountIncludingTax: +(rowItem?.lineAmountIncludingTax ?? 0),
                documentType: DocumentTypeEnum.purchaseReceiptLine,
                taxDate: rowItem.taxDate,
                legislation: this.site.value?.legalCompany?.legislation,
                destinationCountry: this.supplierAddress.value?.country,
                originCountry: this.receivingAddress.value?.country,
                validTypes: ['purchasing', 'purchasingAndSales'],
            },
            updateTaxDetails,
        );

        if (result) {
            rowItem.lineAmountIncludingTaxInCompanyCurrency = String(
                convertAmount(
                    Number(this.lines?.getRecordValue(rowItem?._id ?? '')?.lineAmountIncludingTax),
                    this.companyFxRate.value ?? 0,
                    this.companyFxRateDivisor?.value ?? 0,
                    this.currency.value?.decimalDigits ?? 0,
                    this.siteCurrency?.decimalDigits ?? 0,
                ),
            );
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                lineAmountIncludingTaxInCompanyCurrency: rowItem.lineAmountIncludingTaxInCompanyCurrency,
            });
            if (updateTaxDetails) {
                this._computeTotalAmounts();
            }
        }
    }

    get siteCurrency() {
        return this.site.value?.legalCompany?.currency || { _id: '', decimalDigits: 2, symbol: '' };
    }

    private _computeTotalAmounts() {
        const {
            totalAmountExcludingTax,
            totalAmountExcludingTaxInCompanyCurrency,
            totalAmountIncludingTaxInCompanyCurrency,
        } = this.lines.value.reduce(
            (
                amounts: {
                    totalAmountExcludingTax: number;
                    totalAmountExcludingTaxInCompanyCurrency: number;
                    totalAmountIncludingTaxInCompanyCurrency: number;
                },
                line,
            ) => {
                amounts.totalAmountExcludingTax += Number(line.lineAmountExcludingTax ?? 0);
                amounts.totalAmountExcludingTaxInCompanyCurrency += Number(
                    line.lineAmountExcludingTaxInCompanyCurrency ?? 0,
                );
                amounts.totalAmountIncludingTaxInCompanyCurrency += Number(
                    line.lineAmountIncludingTaxInCompanyCurrency ?? 0,
                );
                return amounts;
            },
            {
                totalAmountExcludingTax: 0,
                totalAmountExcludingTaxInCompanyCurrency: 0,
                totalAmountIncludingTaxInCompanyCurrency: 0,
            },
        );

        this.totalAmountExcludingTax.value = totalAmountExcludingTax;
        this.totalAmountIncludingTax.value = this.totalAmountExcludingTax.value + (this.totalTaxAmount?.value ?? 0);
        this.totalAmountExcludingTaxInCompanyCurrency.value = totalAmountExcludingTaxInCompanyCurrency;
        this.totalAmountIncludingTaxInCompanyCurrency.value = totalAmountIncludingTaxInCompanyCurrency;
    }

    _setStepSequenceStatusObject(stepSequenceValues: PurchaseReceiptStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        return {
            [this.receiptStepSequenceCreate]: stepSequenceValues.create,
            [this.receiptStepSequenceInvoice]: stepSequenceValues.invoice,
            [this.receiptStepSequenceReturn]: stepSequenceValues.return,
        };
    }

    showHideColumns() {
        if (isExchangeRateHidden(this.currency.value, this.site.value, this.businessRelation.value)) {
            this.rateDescription.isHidden = true;
            this.lines.hideColumn('lineAmountExcludingTaxInCompanyCurrency');
            this.lines.hideColumn('lineAmountIncludingTaxInCompanyCurrency');
        } else {
            this.lines.showColumn('lineAmountExcludingTaxInCompanyCurrency');
            this.lines.showColumn('lineAmountIncludingTaxInCompanyCurrency');
            this.rateDescription.isHidden = false;
        }
    }

    orderIsTransferHeaderNote() {
        return this.lines.value.some(
            line => line.purchaseOrderLine?.purchaseOrderLine?.document?.isTransferHeaderNote === true,
        );
    }

    ordersIsTransferLineNote() {
        return this.lines.value.some(
            line => line.purchaseOrderLine?.purchaseOrderLine?.document?.isTransferLineNote === true,
        );
    }

    linesFromSingleOrder() {
        const orderLineNumbers = this.lines.value.map(
            orderLine => orderLine.purchaseOrderLine?.purchaseOrderLine?.document?.number,
        );
        return orderLineNumbers.every(number => number === orderLineNumbers[0]);
    }

    lineNotesChanged() {
        return this.lines.value.some(
            line => line.internalNote?.value || line.isExternalNote || line.externalNote?.value,
        );
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    async displayLandedCosts(documentLineId: string) {
        await this.$.dialog.page(
            '@sage/xtrem-landed-cost/LandedCostSummary',
            {
                args: JSON.stringify({
                    documentLineId,
                    companyCurrency: this.companyCurrency.value?._id ?? '',
                    hideAllocatedAmountColumn: true,
                }),
            },
            { size: 'extra-large' },
        );
    }

    disableHeaderFields() {
        if (!this.$.recordId) {
            const isDisabled: boolean = this.lines.value.length > 0;
            this.site.isDisabled = isDisabled;
            this.businessRelation.isDisabled = isDisabled;
            this.receiptDate.isDisabled = isDisabled;
        }
    }

    async initPosting() {
        this.postingDetails.value = [];
        const financeTransactions = await this.$.graph
            .node('@sage/xtrem-finance-data/FinanceTransaction')
            .queries.getPostingStatusDataByDocumentId(
                {
                    _id: true,
                    documentType: true,
                    documentNumber: true,
                    documentSysId: true,
                    status: true,
                    message: true,
                    hasFinanceIntegrationApp: true,
                    financeIntegrationApp: true,
                    financeIntegrationAppRecordId: true,
                    financeIntegrationAppUrl: true,
                    externalLink: true,
                },
                { documentType: 'purchaseReceipt', documentSysId: this.$.recordId },
            )
            .execute();

        financeTransactions.forEach(financeTransaction => {
            this.postingDetails.addOrUpdateRecordValue(financeTransaction); // add record to the postingDetails table to be shown in grid
        });
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }
}
