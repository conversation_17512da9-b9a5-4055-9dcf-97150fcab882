{"@sage/xtrem-purchasing/activity__purchase_credit_memo__name": "", "@sage/xtrem-purchasing/activity__purchase_invoice__name": "", "@sage/xtrem-purchasing/activity__purchase_order__name": "", "@sage/xtrem-purchasing/activity__purchase_receipt__name": "", "@sage/xtrem-purchasing/activity__purchase_requisition__name": "", "@sage/xtrem-purchasing/activity__purchase_return__name": "", "@sage/xtrem-purchasing/cant_approve_order_wrong_taxCalculationStatus": "", "@sage/xtrem-purchasing/cant_post_credit_memo_when_taxCalculationStatus_is_not_done": "", "@sage/xtrem-purchasing/cant_post_invoice_when_taxCalculationStatus_is_not_done": "", "@sage/xtrem-purchasing/cant_post_receipt_wrong_taxCalculationStatus": "", "@sage/xtrem-purchasing/closeOrder____title": "", "@sage/xtrem-purchasing/closeRequisition____title": "", "@sage/xtrem-purchasing/closeReturn____title": "", "@sage/xtrem-purchasing/data_types__price_origin_enum__name": "", "@sage/xtrem-purchasing/data_types__property_data_type_purchasing__name": "", "@sage/xtrem-purchasing/data_types__purchase_credit_memo_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_credit_memo_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_document_approval_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_document_line_origin_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_document_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_document_type_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_invoice_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_invoice_matching_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_invoice_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_invoice_variance_type_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_order_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_order_invoice_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_order_receipt_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_receipt_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_receipt_invoice_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_receipt_return_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_requisition_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_requisition_order_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_return_credit_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_return_display_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_return_invoice_status_enum__name": "", "@sage/xtrem-purchasing/data_types__purchase_return_shipping_status_enum__name": "", "@sage/xtrem-purchasing/data_types__supplier_document_number_data_type__name": "", "@sage/xtrem-purchasing/data_types__test_text_stream_type__name": "", "@sage/xtrem-purchasing/data_types__unbilled_account_payable_status_enum__name": "", "@sage/xtrem-purchasing/edit-create-line": "", "@sage/xtrem-purchasing/edit-update-purchase-invoice-line": "", "@sage/xtrem-purchasing/enums__price_origin__manual": "", "@sage/xtrem-purchasing/enums__price_origin__supplierPriceList": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__paid": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__partiallyPaid": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__posted": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__postingError": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__postingInProgress": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__stockError": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__taxCalculationFailed": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__error": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__inProgress": "", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__posted": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__approved": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__changeRequested": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__confirmed": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__pendingApproval": "", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__rejected": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__direct": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseCreditMemo": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseInvoice": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseOrder": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseReceipt": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseRequisition": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseReturn": "", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseSuggestion": "", "@sage/xtrem-purchasing/enums__purchase_document_status__closed": "", "@sage/xtrem-purchasing/enums__purchase_document_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_document_status__error": "", "@sage/xtrem-purchasing/enums__purchase_document_status__inProgress": "", "@sage/xtrem-purchasing/enums__purchase_document_status__pending": "", "@sage/xtrem-purchasing/enums__purchase_document_status__posted": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseCreditMemo": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseInvoice": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseOrder": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseReceipt": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseRequisition": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseReturn": "", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseSuggestion": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__credited": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__noVariance": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__paid": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__partiallyCredited": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__partiallyPaid": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__posted": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__postingError": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__postingInProgress": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__stockError": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__taxCalculationFailed": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__variance": "", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__varianceApproved": "", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__noVariance": "", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__variance": "", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__varianceApproved": "", "@sage/xtrem-purchasing/enums__purchase_invoice_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_invoice_status__error": "", "@sage/xtrem-purchasing/enums__purchase_invoice_status__inProgress": "", "@sage/xtrem-purchasing/enums__purchase_invoice_status__posted": "", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__noVariance": "", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__price": "", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__quantity": "", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__quantityAndPrice": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__approved": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__closed": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__confirmed": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__partiallyReceived": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__pendingApproval": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__received": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__rejected": "", "@sage/xtrem-purchasing/enums__purchase_order_display_status__taxCalculationFailed": "", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__invoiced": "", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__notInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__partiallyInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__notReceived": "", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__partiallyReceived": "", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__received": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__closed": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__error": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__invoiced": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__partiallyInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__partiallyReturned": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__postingInProgress": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__received": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__returned": "", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__taxCalculationFailed": "", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__invoiced": "", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__notInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__partiallyInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__notReturned": "", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__partiallyReturned": "", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__returned": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__approved": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__closed": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__confirmed": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__ordered": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__partiallyOrdered": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__pendingApproval": "", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__rejected": "", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__notOrdered": "", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__ordered": "", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__partiallyOrdered": "", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__credited": "", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__notCredited": "", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__partiallyCredited": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__approved": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__closed": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__draft": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__error": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__pendingApproval": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__postingInProgress": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__rejected": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__returned": "", "@sage/xtrem-purchasing/enums__purchase_return_display_status__taxCalculationFailed": "", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__invoiced": "", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__notInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__partiallyInvoiced": "", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__notShipped": "", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__partiallyShipped": "", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__shipped": "", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__completed": "", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__draft": "", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__error": "", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__inProgress": "", "@sage/xtrem-purchasing/functions__purchase__credit_memo__buyer_notification_subject": "", "@sage/xtrem-purchasing/functions__purchase__invoice__buyer_notification_subject": "", "@sage/xtrem-purchasing/functions__purchase__order__approval_email_subject": "", "@sage/xtrem-purchasing/functions__purchase__order__request_changes_email_subject": "", "@sage/xtrem-purchasing/functions__purchase__requisition__approval_email_subject": "", "@sage/xtrem-purchasing/functions__purchase__requisition__request_changes_email_subject": "", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_landed_costs_exist": "", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_non_posted_receipts_exist": "", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_delete_landed_costs_exist": "", "@sage/xtrem-purchasing/functions__send_mail__for_approval__approved": "", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__error_updating_status_order_not_closable": "", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__status_updated": "", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__error_updating_status_order_line_not_closable": "", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__status_updated": "", "@sage/xtrem-purchasing/functions-purchase-order-lib-purchase-order-created": "", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status": "", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status_Requisition_not_closable": "", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__status_updated": "", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__error_updating_status": "", "@sage/xtrem-purchasing/functions-purchase-Requisition-lib_purchase_requisition_line__error_updating_status_Requisition_line_not_closable": "", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__status_updated": "", "@sage/xtrem-purchasing/is_active_dimension_inactive": "", "@sage/xtrem-purchasing/menu_item__purchase-inquiries": "", "@sage/xtrem-purchasing/missing_purchase_order_reference": "", "@sage/xtrem-purchasing/missing_purchase_receipt_reference": "", "@sage/xtrem-purchasing/node__purchase_credit_memo__resend_notification_for_finance": "", "@sage/xtrem-purchasing/node__purchase_invoice__resend_notification_for_finance": "", "@sage/xtrem-purchasing/node__purchase_order_bulk_print_report_name": "", "@sage/xtrem-purchasing/node__purchase_return__resend_notification_for_finance": "", "@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_approval_workflow": "", "@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_confirmed_workflow": "", "@sage/xtrem-purchasing/node_site_extension_check_if_any_pending": "", "@sage/xtrem-purchasing/node-extensions__item_extension__property__purchaseDocuments": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__isPurchaseOrderApprovalManaged": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__isPurchaseRequisitionApprovalManaged": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseOrderDefaultApprover": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseOrderSubstituteApprover": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseRequisitionDefaultApprover": "", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseRequisitionSubstituteApprover": "", "@sage/xtrem-purchasing/node-extensions__supplier_extension__property__defaultBuyer": "", "@sage/xtrem-purchasing/node-extensions__supplier_extension__property__purchaseOrders": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__approval_status_change_forbidden": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__id_already_exists": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__item_modify": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__no__receiving_site": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__no_inventory_site": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__lines_mandatory": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__node_name": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__currency": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__externalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__internalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isExternalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isTransferHeaderNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isTransferLineNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__lines": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__paymentTerm": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__status": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__stockSite": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__supplier": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__taxes": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__reiving_site_legal_company": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__same_company_site_stock_site": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_date": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_number_already_exists": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__tax_type_validation": "", "@sage/xtrem-purchasing/nodes__base_purchase_document__update_not_allowed_status_posted": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_cannot_approve_document": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_credit_memo__date": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__approval_status_change_forbidden": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__bad_conversion_factor": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_site_record": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_record": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_site_record": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__node_name": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__charge": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__company": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__currency": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__discount": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__discountCharges": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__document": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__exemptAmount": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__externalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__grossPrice": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__internalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__isExternalNote": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__item": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__itemDescription": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__itemSupplier": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineStatus": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__netPrice": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__origin": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__priceOrigin": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__purchaseUnit": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__quantity": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__site": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__status": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockSite": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockSiteLinkedAddress": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__supplier": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__supplierName": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxableAmount": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxAmount": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxAmountAdjusted": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxCalculationStatus": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__taxes": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__text": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__unit": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_company_site_stock_site": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_site_header_and_line": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__node_name": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__property__basis": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_discount_charge__property__document": "", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_item_may_not_have_type_landed_cost": "", "@sage/xtrem-purchasing/nodes__different_bill_by_supplier": "", "@sage/xtrem-purchasing/nodes__different_currency": "", "@sage/xtrem-purchasing/nodes__different_purchase_unit": "", "@sage/xtrem-purchasing/nodes__different_receiving_site_address": "", "@sage/xtrem-purchasing/nodes__different_site": "", "@sage/xtrem-purchasing/nodes__different_supplier": "", "@sage/xtrem-purchasing/nodes__different_unit_conversion_factor": "", "@sage/xtrem-purchasing/nodes__differentProperties_item": "", "@sage/xtrem-purchasing/nodes__item-site__failed_deletion_impossible_if_documents": "", "@sage/xtrem-purchasing/nodes__item-site-cost__failed_deletion_impossible_if_documents": "", "@sage/xtrem-purchasing/nodes__landed_cost_allocation__stock_transaction_status_not_completed": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_status_is_not_draft": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_totals_not_equal": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_repost_purchasing_credit_memo_when_status_is_not_failed": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__document_was_posted": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__line__no_financial_site": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__financeIntegrationCheck": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__financeIntegrationCheck__parameter__creditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__post": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__post__parameter__creditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost__parameter__documentData": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost__parameter__purchaseCreditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__resendNotificationForFinance": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__resendNotificationForFinance__parameter__purchaseCreditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail__parameter__user": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__synchronizeDisplayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__synchronizeDisplayStatus__parameter__purchaseCreditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__posted": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__apOpenItems": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByContact": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalExemptAmount": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxableAmount": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxAmount": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxAmountAdjusted": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__creditMemoDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__dueDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__fxRateDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__isOpenItemPageOptionActive": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__matchingUser": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__openItemSysId": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__page": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__paymentTerm": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToContact": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToSupplier": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__pdfSupplierCreditMemo": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__postingDetails": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__reason": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__supplierDocumentDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__supplierDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__totalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalTaxAmount": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__update_forbidden_credit_memo_posted": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__mutation__calculateLineTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__mutation__calculateLineTaxes__parameter__data": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__new_credit_memo_line_not_allowed": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__canHaveLandedCost": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__consumptionLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__grossPrice": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__itemDescription": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__origin": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__priceOrigin": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__providerLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__purchaseInvoiceLine": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__purchaseReturnLine": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__receiptNumber": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__receiptSysId": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__recipientSite": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedLineAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedLineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentSysId": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentType": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockSite": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockTransactions": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__taxCalculationStatus": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uiTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uPurchaseUnit": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__property__basis": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_tax__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_tax__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_document__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__is_inventory_is_purchasing": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__item_must_be_same": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__no_purchase_unit_conversion_coefficient": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__no_stock_unit_conversion_coefficient": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__purchase_unit_must_be_same": "", "@sage/xtrem-purchasing/nodes__purchase_document_line__receipt_site_must_be_same_as_receipt": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_document_line_tax__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_document_tax__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_receipt_line_not_completed": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_totals_not_equal": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_whitout_lines": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__information_payment_tracking": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__invalid_variance_approval": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__parameter__approve": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__reasonCode": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__supplierDocumentDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__financeIntegrationCheck": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__financeIntegrationCheck__parameter__invoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__post": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__post__parameter__purchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost__parameter__documentData": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost__parameter__purchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resendNotificationForFinance": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resendNotificationForFinance__parameter__purchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resynchronizeDisplayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resynchronizeDisplayStatus__parameter__purchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail__parameter__user": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__payment_tracking": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__posted": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__apOpenItems": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByContact": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalExemptAmount": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalRemainingQuantityToCredit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxableAmount": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxAmount": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxAmountAdjusted": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__dueDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__financeStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__fxRateDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__invoiceDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__isOpenItemPageOptionActive": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__matchingStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__matchingUser": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__openItemSysId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__page": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__paymentTerm": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToContact": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToSupplier": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__pdfSupplierInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__postingDetails": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__returnLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__stockSite": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__supplierDocumentDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__supplierDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__totalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__transactionCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalAmountIncludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalTaxAmount": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__destinationCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__rateDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__sourceCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__destinationCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__rateDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__sourceCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__companyFxRate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__companyFxRateDivisor": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__destinationCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__sourceCurrencyId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_purchase_receipt_line_not_completed": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_receipt_lines": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__insufficient_allocations": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__invalid_variance_approval": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_not_fully_allocated": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_zero_allocation": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__missing_amount_allocation": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__approve": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__purchaseInvoiceId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__purchaseInvoiceLineId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__calculateLineTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__calculateLineTaxes__parameter__data": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__only__applicative_users": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__canHaveLandedCost": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__consumptionLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__creditedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__landedCostCheckResult": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__matchingStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__origin": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__priceOrigin": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__providerLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseCreditMemoLines": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseReturnLines": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__recipientSite": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__remainingQuantityToCredit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentSysId": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentType": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockSite": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockTransactions": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__uiTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceApprover": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceText": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceType": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoAmount": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoUnitPrice": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseInvoiceLine": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__same_properties_invoice_to_credit_memo": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_matching__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_matching__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_matching__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_matching__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_invoice_matching__property__purchaseInvoices": "", "@sage/xtrem-purchasing/nodes__purchase_order__already_exist_order_with_same_supplier_order_reference": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__fixedNumberOfLines": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__itemId": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__itemQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__numberOfLinesPerOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__orderNumberRoot": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__orderQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__supplierId": "", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__massApproval": "", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__printBulk": "", "@sage/xtrem-purchasing/nodes__purchase_order__cannot_create_receipt": "", "@sage/xtrem-purchasing/nodes__purchase_order__document_not_found": "", "@sage/xtrem-purchasing/nodes__purchase_order__invalid_approval_status": "", "@sage/xtrem-purchasing/nodes__purchase_order__invalid_confirm_status": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__parameter__isApproved": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__closeLine": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__closeLine__parameter__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__parameter__isConfirmed": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseOrderReplenishment": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseOrderReplenishment__parameter__data": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseReceipt": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseReceipt__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__financeIntegrationCheck": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__financeIntegrationCheck__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrder__parameter__isPrinted": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrder__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactEmail": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactFirstName": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactLastName": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactTitle": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__financeTransaction": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__orderLines": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__saveOnly": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail__parameter__user": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__setIsSentPurchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__setIsSentPurchaseOrder__parameter__purchaseOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_order__order_date_cannot_be_future": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__approvalStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__approvalUrl": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__changeRequestedDescription": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__defaultBuyer": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__deliveryMode": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__earliestExpectedDate": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__fxRateDate": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__invoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__isApprovalManaged": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__isClosedOrReceived": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__isGrossPriceMissing": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__isOrderAssignmentLinked": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__isPurchaseOrderSuggestion": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__jsonAggregateLandedCostTypes": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__orderDate": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__page": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__paymentTerm": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__receiptStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__stockSite": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierAddress": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierContact": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierOrderReference": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalQuantityToReceiveInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order__property__transactionCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order__query__getFilteredUsers": "", "@sage/xtrem-purchasing/nodes__purchase_order__query__getFilteredUsers__parameter__criteria": "", "@sage/xtrem-purchasing/nodes__purchase_order__receiving_site_is_not_inventory": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__expected_receipt_date_to_order_date": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__mutation__calculateLineTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__mutation__calculateLineTaxes__parameter__data": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__actualLandedCostInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__amountForLandedCostAllocation": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__assignments": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__canHaveLandedCostLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__changeRequestedDescription": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__expectedReceiptDate": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__isPurchaseOrderSuggestion": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__isUsingFunctionToClose": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__lineInvoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__lineReceiptStatus": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__numberOfPurchaseInvoiceLines": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__numberOfPurchaseReceiptLines": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__origin": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__postedQuantityReceivedInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseInvoiceLines": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseReceiptLines": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseRequisitionLines": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityInStockUnitForLandedCostAllocation": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityReceivedInProgress": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityToReceive": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityToReceiveInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantityProgress": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingAmountToReceiveExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingAmountToReceiveExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToInvoiceOnOrder": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToProcessForLandedCost": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__stockSiteAddress": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__stockSiteContact": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderLineLink": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uiTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uSupplyOrderQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__workInProgress": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlCloseLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlCloseLine__parameter__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlDelete": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlDelete__parameter__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__item": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__site": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__supplier": "", "@sage/xtrem-purchasing/nodes__purchase_order_line__total_quantity_from_orders_equal_line_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedAmount": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedUnitPrice": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__receivedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__receivedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_order_purchase_invoice_line__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_invoice__same_properties_invoice_to_order": "", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_completed": "", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_convert_draft": "", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_draft": "", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__same_properties_order_to_receipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__invoices_must_be_posted_first": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseInvoice__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseReturns": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseReturns__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck__parameter__purchaseReceipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck__parameter__receiptNumber": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__post": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__post__parameter__receipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__financeTransaction": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__landedCostControl": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__purchaseReceipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__receiptLines": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__saveOnly": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__resendNotificationForFinance": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__resendNotificationForFinance__parameter__purchaseReceipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__only_inventory_sites_allowed": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__carrier": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__fxRateDate": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__invoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__isStockDetailRequired": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__jsonAggregateLandedCostTypes": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__purchaseReturns": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__receiptDate": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__receivingAddress": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__returnAddress": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__returnStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__supplierAddress": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__supplierDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__mutation__calculateLineTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__mutation__calculateLineTaxes__parameter__data": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__actualLandedCostInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__amountForLandedCostAllocation": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__canHaveLandedCostLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__completed": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__financialSite": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__grossPrice": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__jsonStockDetails": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineInvoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineReturnStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__numberOfPurchaseInvoiceLines": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__orderCost": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__origin": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__priceOrigin": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseInvoiceLines": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseReturnLines": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__quantityInStockUnitForLandedCostAllocation": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingQuantityToInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingReturnQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnAddress": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockDetails": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockDetailStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockMovements": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockTransactions": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__transientPurchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__uiTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__valuedCost": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__workInProgress": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_invoiced_exceeds_lines_received_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_returned_exceeds_lines_received_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__price": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseReturnLine": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__returnedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__returnedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__provided_stock_quantity_error": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_matching__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_matching__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_matching__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_matching__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_matching__property__purchaseReceipts": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_post__stock_details_required": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_post__tax_calculation_failed": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__stock_transaction_status_not_completed": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_invoice__same_properties_invoice_to_receipt": "", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__same_properties_receipt_to_return": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__approval_status_change_forbidden": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__approved_status__line_control": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_approval_workflow": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_confirmation_workflow": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__invalid_status": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__item_mandatory_on_lines": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve__parameter__approve": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__close": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__close__parameter__purchaseRequisition": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__createPurchaseOrders": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__createPurchaseOrders__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail__parameter__user": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__approvalStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__approvalUrl": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__changeRequestedDescription": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__internalNote": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isApplyDefaultSupplierHidden": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isCreateOrderLinesHidden": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isSetDimensionHidden": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isTransferHeaderNote": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isTransferLineNote": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__orderStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__page": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__receivingSite": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__requestDate": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__requester": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_requisition__request_date_cannot_be_future": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__bad_conversion_factor": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__gross_price_currency_mandatory": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__grossPrice": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__netPrice": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__priceOrigin": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__purchaseRequisitionLine": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__quantity": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__supplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__totalTaxExcludedAmount": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__closeLine": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__closeLine__parameter__purchaseRequisitionLine": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__need_by_date_inferior_to_request_date": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__no_purchase_unit_conversion_coefficient": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__analyticalData": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__approvalStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__changeRequestedDescription": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__charge": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__discount": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__discountCharges": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__grossPrice": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSite": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSiteSupplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSupplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__lineOrderStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__lineStatus": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__needByDate": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__netPrice": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedPercentage": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__priceOrigin": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__purchaseOrderLines": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantity": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityToOrder": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityToOrderInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__receivingSite": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__requestedItemDescription": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__stockSiteLinkedAddress": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__supplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__totalTaxExcludedAmount": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__purchase_unit_mandatory_if_no_item": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__currencyId": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__siteId": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__supplierId": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__requested_item_description_mandatory": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__total_quantity_from_orders_equal_line_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_delete__line": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__property__basis": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__orderedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__orderedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseRequisitionLine": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__provided_stock_quantity_error": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_currency": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_item": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_purchase_unit": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_supplier": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_unit_conversion_factor": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__error_updating_requisition_status": "", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__same_properties_order_to_receipt": "", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_return__cant_repost_purchase_return_when_status_is_not_failed": "", "@sage/xtrem-purchasing/nodes__purchase_return__deletion_forbidden_reason_status": "", "@sage/xtrem-purchasing/nodes__purchase_return__document_was_posted": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve__parameter__toBeApproved": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__close": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__close__parameter__returnDoc": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__createPurchaseInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__createPurchaseInvoice__parameter__returnDoc": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__financeIntegrationCheck": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__financeIntegrationCheck__parameter__purchaseReturn": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__post": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__post__parameter__purchaseReturn": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost__parameter__documentLines": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost__parameter__purchaseReturn": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__resendNotificationForFinance": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__resendNotificationForFinance__parameter__purchaseReturn": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__submitForApproval": "", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__submitForApproval__parameter__document": "", "@sage/xtrem-purchasing/nodes__purchase_return__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_return__post__allocation_status": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__allocationStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__approvalStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__businessRelation": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__changeRequestDescription": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__creditStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__date": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__displayStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__documentDate": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__effectiveDate": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__fxRateDate": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__invoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__lines": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__postingDetails": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnItems": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnRequestDate": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnSite": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnToAddress": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__shippingStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__siteAddress": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__stockSite": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierAddress": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierReturnReference": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_return__property__transactionCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__allocated_exceeds_returned_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__bad_conversion_factor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__expected_return_date_inferior_to_return_date": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__allocationStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__approvalStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__computedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__creditedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__creditedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__document": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__expectedReturnDate": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__item": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__lineCreditStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__lineInvoiceStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__origin": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseCreditMemoLines": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseInvoiceLines": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityAllocated": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityToInvoice": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityToInvoiceInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__reason": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__remainingQuantityToAllocate": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__remainingQuantityToCredit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__shippedStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__site": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__sourceDocumentNumber": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__sourceDocumentType": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__status": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockAllocations": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockDetails": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockMovements": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockTransactions": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockTransactionStatus": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__storedAttributes": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__storedDimensions": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__taxDate": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__totalTaxExcludedAmount": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__uiTaxes": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__workInProgress": "", "@sage/xtrem-purchasing/nodes__purchase_return_line__total_invoiced_exceeds_lines_returned_quantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoAmount": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoUnitPrice": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__currency": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseReturnLine": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseReturnLine": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__stockUnit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__unit": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__provided_stock_quantity_error": "", "@sage/xtrem-purchasing/nodes__purchase_return_matching__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__purchase_return_matching__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__purchase_return_matching__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__purchase_return_matching__node_name": "", "@sage/xtrem-purchasing/nodes__purchase_return_matching__property__purchaseReturns": "", "@sage/xtrem-purchasing/nodes__purchase_return_status_draft": "", "@sage/xtrem-purchasing/nodes__purchase_return_status_inconsistent": "", "@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_credit_memo__same_properties_return_to_credit_memo": "", "@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_invoice__same_properties_return_to_invoice": "", "@sage/xtrem-purchasing/nodes__purchase-invoice__cant_create_credit_memo": "", "@sage/xtrem-purchasing/nodes__purchase-invoice__cant_repost_purchasing_invoice_when_status_is_not_failed": "", "@sage/xtrem-purchasing/nodes__purchase-invoice__document_was_posted": "", "@sage/xtrem-purchasing/nodes__purchase-order__cant_repost_purchase_order_when_status_is_not_failed": "", "@sage/xtrem-purchasing/nodes__purchase-order__document_was_posted": "", "@sage/xtrem-purchasing/nodes__purchase-order__document_was_saved": "", "@sage/xtrem-purchasing/nodes__purchase-receipt__cant_repost_purchase_receipt_when_status_is_not_failed": "", "@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_posted": "", "@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_saved": "", "@sage/xtrem-purchasing/nodes__site_extension__check_pending_purchase_requisitions": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__unbilledAccountPayableInquiry": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__unbilledAccountPayableInquiry__parameter__userId": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__node_name": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__asOfDate": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__company": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__executionDate": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__fromSupplier": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__lines": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__sites": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__status": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__toSupplier": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__user": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__node_name": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__account": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__accountItem": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__billBySupplier": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__company": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__companyCurrency": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__creditedQuantity": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__currency": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__documentDate": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__financialSite": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__inputSet": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoicedQuantity": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmount": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmountInCompanyCurrency": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmountInCompanyCurrencyAtAsOfDate": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableQuantity": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__item": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__netPrice": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__purchaseUnit": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__quantity": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__receiptInternalId": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__receiptNumber": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__returnedQuantity": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__stockSite": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__supplier": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_message": "", "@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_notification_title": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__node_name": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__actualQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentId": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentNumber": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__endDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__expectedQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__item": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__originDocumentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__outstandingQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__purchaseOrderLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__site": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__startDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__status": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__node_name": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__actualQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentId": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentNumber": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__endDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__expectedQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__item": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__originDocumentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__outstandingQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__purchaseReceiptLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__site": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__startDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__status": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__node_name": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__actualQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentId": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentNumber": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__endDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__expectedQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__item": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__originDocumentType": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__outstandingQuantity": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__purchaseReturnLine": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__site": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__startDate": "", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__status": "", "@sage/xtrem-purchasing/nodes_different_supplier_address": "", "@sage/xtrem-purchasing/nodes_new_invoice_line_not_allowed": "", "@sage/xtrem-purchasing/package__name": "", "@sage/xtrem-purchasing/page__at_least_one_mandatory_tax_code_not_found": "", "@sage/xtrem-purchasing/page__purchase_credit_memo__at_least_one_mandatory_tax_code_not_found": "", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__expected_date_exceed": "", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__lower-than-min-quantity": "", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__mandatory_supplier": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension____navigationPanel__listItem__line11__title": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyer____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyer____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyer____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyer____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyer____title": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__defaultBuyerBlock____title": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__approvalStatus": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__number": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__orderDate": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__paymentTerm__name": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__status": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__stockSite__name": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____dropdownActions__title": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchaseOrders____title": "", "@sage/xtrem-purchasing/page-extensions__business_entity_supplier_extension__purchasingSection____title": "", "@sage/xtrem-purchasing/page-extensions__item_extension__documentSection____title": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title___constructor": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__displayStatus": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__number": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__orderDate": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__site__id": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__site__name": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__supplier__id": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__supplier__name": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__expectedReceiptDate": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__lineInvoiceStatus": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__lineReceiptStatus": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__quantity": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__quantityToReceive": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__status": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__stockSite__id": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__stockSite__name": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__2": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__3": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__4": "", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__lines____columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__selectFromPurchaseOrder____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__selectFromPurchaseReceipt____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__displayStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__receivedQuantity": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__supplier__name": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__supplierDocumentNumber": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__number": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__receiptStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__site": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__supplier__name": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__displayStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__number": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__site": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__supplier__name": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__displayStatus": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__number": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__site": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__supplier__name": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__supplierDocumentNumber": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____title": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension____navigationPanel__listItem__line11__title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__defaultApproverBlock____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__isPurchaseOrderApprovalManaged____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__isPurchaseRequisitionApprovalManaged____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____title": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__email": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__firstName": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__lastName": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____lookupDialogTitle": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyerBlock____title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__approvalStatus": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__number": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__orderDate": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__paymentTerm__name": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__status": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__stockSite__name": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____dropdownActions__title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____title": "", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchasingSection____title": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list___no_results": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list__search": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__order_number": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__ordered_quantity": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__receipt_number": "", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__received_quantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__id__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__idPayToSupplier__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line12__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line13__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line14__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line3__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line6__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line7__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line8__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line9__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__paymentStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__amountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__approverSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__isActive": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__postcode": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__region": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__lookupDialogTitle__businessEntity__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__nestedFields__primaryAddress__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountExcludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountIncludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyFxRate____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyFxRateDivisor____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__credit_memo_date__cannot__be__future": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__creditMemoDate____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__default_buyer": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__documentNumberLink____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__dueDate____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__email_not_sent": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__email_sent": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__emailAddressApproval____helperText": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__emailAddressApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__financialSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__forcedAmountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__greater_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__smaller_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__fxRateDate____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__goToSysNotificationPage____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__informationBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__informationSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__4": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__5": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__stockUnit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__recipientSite": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__uPurchaseUnit__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__lineAmountIncludingTax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__lineAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__origin": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine___id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__recipientSite": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxAmountAdjusted": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxCalculationStatus": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__uPurchaseUnit__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__uStockUnit__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__netBalance____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notesBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notifyBuyer____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__number____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__isActive": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__postcode": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__region": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__pdfBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__no_line": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__variance": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_errors": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__financeIntegrationAppRecordId": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__financeIntegrationAppRecordId__2": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__postingStatus": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__targetDocumentNumber": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__targetDocumentType": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingMessageBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoExcludingTaxVariance____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoIncludingTaxVariance____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoTaxVariance____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__creditedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__link": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__remainingQuantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__creditedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__link": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__linkReceipt": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__remainingQuantity": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__rateDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__reason____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_accounts_payable_invoice_already_posted": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_documents_already_posted_title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_errors": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_journal_entry_already_posted": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__requestApprovalSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_continue": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_message": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__save____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__save_warnings": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__select_buyer_button_text": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectedUser____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectedUser____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectedUser____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectFromInvoice____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectFromReturn____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__send_approval_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__columns__legalCompany__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__status____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_pay": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_posting": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__stockTransactionStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplier_document_date__cannot__be__future": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierDocumentDate____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierDocumentNumber____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxCalculationStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxEngine____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__postfix__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__isReverseCharge": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__tax": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxableAmount": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxAmountAdjusted": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxCategory": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__text____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__textSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSectionCompanyCurrencyDetailsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalTaxAmountAdjusted____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__users____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__users____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__users____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__users____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__users____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo_from_return__greater_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo_partial__from_return_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo_partial_confirm__partial_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__6": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__7": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__id__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__idPayToSupplier__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line12__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line13__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line14__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line3__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line6__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line7__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line8__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line9__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__paymentStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_invoice____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_invoice____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice___header_grid_warning_message": "", "@sage/xtrem-purchasing/pages__purchase_invoice__acceptAllVariances____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__amountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__approverSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billByLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountExcludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountIncludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalRemainingQuantityToCredit____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__companyFxRate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__companyFxRateDivisor____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__confirm_allocate": "", "@sage/xtrem-purchasing/pages__purchase_invoice__confirm_landed_cost": "", "@sage/xtrem-purchasing/pages__purchase_invoice__createPurchaseCreditNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__default_buyer": "", "@sage/xtrem-purchasing/pages__purchase_invoice__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__documentNumberLink____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__dueDate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__email_not_sent": "", "@sage/xtrem-purchasing/pages__purchase_invoice__email_sent": "", "@sage/xtrem-purchasing/pages__purchase_invoice__emailAddressApproval____helperText": "", "@sage/xtrem-purchasing/pages__purchase_invoice__emailAddressApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors": "", "@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__financialSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__forcedAmountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__fxRateDate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__goToSysNotificationPage____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__informationBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__informationSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__invoice_date__cannot__be__future": "", "@sage/xtrem-purchasing/pages__purchase_invoice__invoiceDate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__isTransferHeaderNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__isTransferLineNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__line_matching_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__recipientSite__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__recipientSite__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__stockUnit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__varianceApprover__displayName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__recipientSite": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__image": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__lineAmountIncludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__lineAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__matchingStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__origin": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine___id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__recipientSite": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxAmountAdjusted": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxCalculationStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__varianceApprover__displayName": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__varianceType": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__4": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__5": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__6": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__7": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__netBalance____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__noteBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__notesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__notifyBuyer____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__number____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__name__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__pdfBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post__no_line": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post__variance": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__post_errors": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__financeIntegrationAppRecordId__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__postingStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__targetDocumentNumber": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__targetDocumentType": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingMessageBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__postingSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title__purchaseOrderLine__status": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__nestedFields__unit__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title__purchaseReceiptLine__status": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id__3": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__rateDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__repost____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_accounts_payable_invoice_already_posted": "", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_documents_already_posted_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_errors": "", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_journal_entry_already_posted": "", "@sage/xtrem-purchasing/pages__purchase_invoice__requestApprovalSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_continue": "", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_message": "", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__save____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__save_warnings": "", "@sage/xtrem-purchasing/pages__purchase_invoice__select_approval_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_invoice__select_buyer_button_text": "", "@sage/xtrem-purchasing/pages__purchase_invoice__selectedUser____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_invoice__selectedUser____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_invoice__selectedUser____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_invoice__selectFromReceipt____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__selectReceiptSecondaryButton____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice__site____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__status____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_pay": "", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_post": "", "@sage/xtrem-purchasing/pages__purchase_invoice__stockTransactionStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__supplier_document_date__cannot__be__future": "", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierDocumentDate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierDocumentNumber____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxCalculationStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxEngine____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__postfix__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__isReverseCharge": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__tax": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxableAmount": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxAmountAdjusted": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxCategory": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__text____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__textSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_fail": "", "@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_success": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountPaid____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSectionCompanyCurrencyDetailsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__totalTaxAmountAdjusted____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__users____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__users____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_invoice__users____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_invoice__users____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_invoice__users____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__variances_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment__mainSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment__validate____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__currencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__documentDate__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__documentLineType__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemDescription__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__netPrice__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__quantity__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__signedLineAmountExcludingTax__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__signedLineAmountExcludingTaxInCompanyCurrency__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__siteId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__siteName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__status__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__stockSiteId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__stockSiteName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__supplierId__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__supplierName__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__unit__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_line_panel__totalMinusVatAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching___no_results": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__confirm__lower_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__confirm_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__error_zero_or_negative": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__item____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__item____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__item____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__lineBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__linesBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__no_lines_selected": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__pdfBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoice____columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoice____columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoice____columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoice____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoice____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__remainingQuantityToCredit": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__purchaseInvoices____levels__columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__resultsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__search": "", "@sage/xtrem-purchasing/pages__purchase_invoice_matching__select_purchase_invoice": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__create____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__creditNoteSection____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__reasonCreditNote____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__reasonCreditNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__supplierDocumentDateCreditNote____title": "", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__totalAmountExcludingTaxCreditNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__bulkActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__10": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__6": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__7": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__8": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__9": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__id__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line1__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line12__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line13__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line3__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line6__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line8__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line9__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__10": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__11": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__8": "", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__9": "", "@sage/xtrem-purchasing/pages__purchase_order____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_order____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_order____title": "", "@sage/xtrem-purchasing/pages__purchase_order__amountsInCompanyCurrencyBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_order__approval_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_order__approvalStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_order__approve____title": "", "@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__approverSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____title": "", "@sage/xtrem-purchasing/pages__purchase_order__changeRequestedDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_order__changeRequestTextSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__close____title": "", "@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_order__companyFxRate____title": "", "@sage/xtrem-purchasing/pages__purchase_order__companyFxRateDivisor____title": "", "@sage/xtrem-purchasing/pages__purchase_order__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__confirm_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__title": "", "@sage/xtrem-purchasing/pages__purchase_order__contactSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__createPurchaseReceipt____title": "", "@sage/xtrem-purchasing/pages__purchase_order__currency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_order__currency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_order__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__currency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_order__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_order__default_approver": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____title": "", "@sage/xtrem-purchasing/pages__purchase_order__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_order__deletePurchaseOrder____title": "", "@sage/xtrem-purchasing/pages__purchase_order__deliveryMode____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__deliveryMode____title": "", "@sage/xtrem-purchasing/pages__purchase_order__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_order__earliestExpectedDate____title": "", "@sage/xtrem-purchasing/pages__purchase_order__earliestExpectedDateTile____title": "", "@sage/xtrem-purchasing/pages__purchase_order__email_not_sent": "", "@sage/xtrem-purchasing/pages__purchase_order__email_sent": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddress____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressApproval____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressChanges____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressChanges____title": "", "@sage/xtrem-purchasing/pages__purchase_order__emailFirstName____title": "", "@sage/xtrem-purchasing/pages__purchase_order__emailLastName____title": "", "@sage/xtrem-purchasing/pages__purchase_order__emailTitle____title": "", "@sage/xtrem-purchasing/pages__purchase_order__externalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__externalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order__externalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__externalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_order__financialBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__financialSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__fxRateDate____title": "", "@sage/xtrem-purchasing/pages__purchase_order__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__informationSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_order__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_order__invoiceStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isExternalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isExternalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isPrinted____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isSent____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isTransferHeaderNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order__isTransferLineNote____title": "", "@sage/xtrem-purchasing/pages__purchase_order__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__just_save": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__actualAllocatedCostAmountInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__landedCostType": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____title": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCostsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__landedCostsSectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__unit__name__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__unit__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__site": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__stockSite": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__unit__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__actualLandedCostInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__changeRequestedDescription": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__expectedReceiptDate": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__isExternalNote": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__status": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineAmountIncludingTax": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineInvoiceStatus": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineReceiptStatus": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__origin": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityReceivedInProgress": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityToReceive": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityToReceiveInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__receivedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__receivedQuantityProgress": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__site": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__stockSite": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__stockSiteLinkedAddress__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__taxCalculationStatus": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__uDemandOrderLineLink": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__unit__name": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__6": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__7": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__8": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__6": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__7": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__8": "", "@sage/xtrem-purchasing/pages__purchase_order__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_order__minimumOrderAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_order__no_price": "", "@sage/xtrem-purchasing/pages__purchase_order__no_price_title": "", "@sage/xtrem-purchasing/pages__purchase_order__no_requisitions": "", "@sage/xtrem-purchasing/pages__purchase_order__no_requisitions_title": "", "@sage/xtrem-purchasing/pages__purchase_order__noteBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__notesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__number____title": "", "@sage/xtrem-purchasing/pages__purchase_order__order_date_cannot_be_future": "", "@sage/xtrem-purchasing/pages__purchase_order__orderDate____title": "", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____columns__title__description": "", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__print____title": "", "@sage/xtrem-purchasing/pages__purchase_order__printed": "", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_closed": "", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_fail": "", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_success": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__invoicedUnitPrice": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__purchaseInvoiceLine__document__status": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____title": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseOrderExcludingValue____title": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseOrderValue____title": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title__purchaseReceiptLine__status": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title__receivedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____title": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title__orderedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title__purchaseRequisitionLine__status": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____title": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__orderedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__orderedQuantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__purchaseRequisitionLine__status": "", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____title": "", "@sage/xtrem-purchasing/pages__purchase_order__rateDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_order__receipt_created": "", "@sage/xtrem-purchasing/pages__purchase_order__receipt_not_created": "", "@sage/xtrem-purchasing/pages__purchase_order__receiptStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_order__reject____title": "", "@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__repost": "", "@sage/xtrem-purchasing/pages__purchase_order__repost____title": "", "@sage/xtrem-purchasing/pages__purchase_order__requestApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_order__requestApprovalSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__requestChanges____title": "", "@sage/xtrem-purchasing/pages__purchase_order__requestChangesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__save____title": "", "@sage/xtrem-purchasing/pages__purchase_order__save_only": "", "@sage/xtrem-purchasing/pages__purchase_order__save_warnings": "", "@sage/xtrem-purchasing/pages__purchase_order__select_approver_button_text": "", "@sage/xtrem-purchasing/pages__purchase_order__select_bill_to_contact_button_text": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__title": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedUser____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedUser____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_order__selectedUser____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_order__selectFromRequisition____title": "", "@sage/xtrem-purchasing/pages__purchase_order__send_approval_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_order__send_change_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_order__send_order_button_text": "", "@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order__sendEmail____title": "", "@sage/xtrem-purchasing/pages__purchase_order__sendEmailBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__sendEmailSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__title": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__site____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__site____title": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____addButtonText": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__columns__country__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__isActive": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__postcode": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__region": "", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__status____title": "", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_approving": "", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_confirm": "", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_invoicing": "", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_receiving": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSite____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSite____title": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____addButtonText": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__columns__country__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__isActive": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__postcode": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__region": "", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__substitute_approver": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____addButtonText": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__lookupDialogTitle__country": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__columns__country__title": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__columns__country__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__addressLine1": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__addressLine2": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__city": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__isActive": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__postcode": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__region": "", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_order__taxCalculationStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_order__taxEngine____title": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__postfix__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__isReverseCharge": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__tax": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxableAmount": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxCategory": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_order__taxes____title": "", "@sage/xtrem-purchasing/pages__purchase_order__text____title": "", "@sage/xtrem-purchasing/pages__purchase_order__textSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalActualLandedCostsInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountExcludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountIncludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalMinusVatAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_order__totalTaxAmountAdjusted____title": "", "@sage/xtrem-purchasing/pages__purchase_order__users____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_order__users____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_order__users____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_order__users____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_order__users____title": "", "@sage/xtrem-purchasing/pages__purchase_order__warn_needByDate_less_than_expected_receipt_date": "", "@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order_approval____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignedQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandDocumentLine__documentNumber": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandType": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandWorkInProgress__expectedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__quantityNotAssigned": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__componentBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__remainingQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__requiredQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_order_confirm__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_purchase_order_line_quantity_in_stock_unit": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-cancel": "", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-delete": "", "@sage/xtrem-purchasing/pages__purchase_order_line_close_action_allowed_line_closed": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__currencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__expectedReceiptDate__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__invoiceStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemDescription__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__lineAmountExcludingTax__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__lineAmountExcludingTaxInCompanyCurrency__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__lineReceiptStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__netPrice__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__orderDate__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__purchaseOrderNumber__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__quantity__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__quantityToReceive__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__remainingAmountToReceiveExcludingTax__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__remainingAmountToReceiveExcludingTaxInCompanyCurrency__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__siteId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__siteName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__status__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierId__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItem__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItemCode__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItemName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierName__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__unit__title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____title": "", "@sage/xtrem-purchasing/pages__purchase_order_line_panel__quantity_in_purchase_unit_negative_value": "", "@sage/xtrem-purchasing/pages__purchase_order_print__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_order_send__email_cannot_be_sent": "", "@sage/xtrem-purchasing/pages__purchase_order_send__email_exception": "", "@sage/xtrem-purchasing/pages__purchase_order_send__email_sent": "", "@sage/xtrem-purchasing/pages__purchase_order_send__invalid-email": "", "@sage/xtrem-purchasing/pages__purchase_order_send__order_cannot_be_sent": "", "@sage/xtrem-purchasing/pages__purchase_order_submit_for_approval__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__businessRelation____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__businessRelation____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success_multi": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__criteriaBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__issueDateFrom____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__issueDateTo____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__mainBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__mainSection____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__document__number": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__expectedReceiptDate": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__supplierName": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__unit__name": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____title": "", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__updatePurchaseOrder____title": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm__lower_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__create_purchase_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_over_ordered": "", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_under_ordered": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__id__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line10__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line11__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line7__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line8__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__10": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__11": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__12": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__8": "", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__9": "", "@sage/xtrem-purchasing/pages__purchase_receipt____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_receipt____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_receipt____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__actualOnReceiptQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__amountsInCompanyCurrencyBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__carriageBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__carrier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__carrier____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__companyFxRate____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__companyFxRateDivisor____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__create_invoice_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_receipt__create_invoice_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_lines_without_supplier": "", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__createPurchaseInvoice____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__createPurchaseReturn____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__customSave____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__delete_confirmation": "", "@sage/xtrem-purchasing/pages__purchase_receipt__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__financialBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__financialSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__fxRateDate____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__goToSysNotificationPage____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__informationBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__informationSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__invoice_created": "", "@sage/xtrem-purchasing/pages__purchase_receipt__invoice_not_created": "", "@sage/xtrem-purchasing/pages__purchase_receipt__invoiceStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__isTransferHeaderNote____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__isTransferLineNote____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__just_save": "", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____columns__title__landedCostType": "", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCostsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCostsSectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__stockUnit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__title__expirationDate": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__columns__columns__document__number__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__actualLandedCostInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineAmountIncludingTax": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineAmountIncludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineInvoiceStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineReturnStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__origin": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__purchaseOrderLine__purchaseReceiptLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockDetailStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__taxCalculationStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__newOnReceiptQuantity____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__notesBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__notesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__number____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__onReceiptQuantityChangeSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__post____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__post_from_main_row": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__documentNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__documentType": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingMessageBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__postingSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__print____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title__purchaseInvoiceLine__document__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__3": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__4": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title__purchaseOrderLine__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title__purchaseReceiptLine__quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReceiptLineCount____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title__purchaseReturnLine__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title__returnedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__rateDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receipt_date_cannot_be_future": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receiptDate____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__repost": "", "@sage/xtrem-purchasing/pages__purchase_receipt__repost____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__repost_errors": "", "@sage/xtrem-purchasing/pages__purchase_receipt__return_created": "", "@sage/xtrem-purchasing/pages__purchase_receipt__return_not_created": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__returnStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__save____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__save_only": "", "@sage/xtrem-purchasing/pages__purchase_receipt__save_warnings": "", "@sage/xtrem-purchasing/pages__purchase_receipt__selectFromPurchaseOrderLookup____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__primaryAddress___id": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt__site____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__status____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_invoicing": "", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_return": "", "@sage/xtrem-purchasing/pages__purchase_receipt__stockTransactionStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierDocumentNumber____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxCalculationStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxEngine____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__postfix__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__isReverseCharge": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__tax": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxableAmount": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxAmount": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxCategory": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxRate": "", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalActualLandedCostsInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountExcludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountIncludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountIncludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalExcludingTaxValue____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalIncludingTaxValue____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalTaxAmount____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt__totalTaxAmountAdjusted____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__currencyId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__invoicedQuantity__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__invoiceStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemDescription__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__lineAmountExcludingTax__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__lineAmountExcludingTaxInCompanyCurrency__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__lineReturnStatus__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__netPrice__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__purchaseOrderNumber__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__quantity__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__receiptDate__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__remainingQuantityToInvoice__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__remainingReturnQuantity__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__returnedQuantity__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__siteId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__siteName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__status__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__stockSiteId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__stockSiteName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierId__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierItem__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierItemCode__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierName__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__unit__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching___no_results": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__confirm__lower_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__confirm_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__error_zero_or_negative": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__item____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__item____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__item____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__lineBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__linesBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__pdfBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____columns__title__invoiceStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____columns__title__receiptStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____columns__title__site__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseOrder____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____columns__title__invoiceStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____columns__title__site__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____columns__title__supplierDocumentNumber": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipt____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__remainingQuantityToInvoice": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__stockTransactionStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__purchaseReceipts____levels__columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt_matching__select_purchase_order_or_purchase_receipt": "", "@sage/xtrem-purchasing/pages__purchase_receipt_post__stock_details_required": "", "@sage/xtrem-purchasing/pages__purchase_receipt_post__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_receipt_print__tax_calculation_failed": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__create_purchase_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__greater_quantity_error": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__columns__document__number__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__columns__document__number__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__document__number": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__invoicedQuantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__item__image": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__lineInvoiceStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__lineReturnStatus": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__remainingReturnQuantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__returnedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__returnedQuantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__purchaseReceiptLines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel__site____title": "", "@sage/xtrem-purchasing/pages__purchase_receipt_table_panel_zero_quantity_error": "", "@sage/xtrem-purchasing/pages__purchase_receipt_to_purchase_invoice": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__6": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__7": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__8": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__9": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__10": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__8": "", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__9": "", "@sage/xtrem-purchasing/pages__purchase_requisition____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_requisition____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_requisition____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__apply_default_supplier_success": "", "@sage/xtrem-purchasing/pages__purchase_requisition__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_requisition__applyDefaultSuppliers____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approval_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approvalStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approve____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__approverSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__changeRequestTextSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__closeRequisition____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_lines_without_supplier": "", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__createPurchaseOrder____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__default_approver": "", "@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier": "", "@sage/xtrem-purchasing/pages__purchase_requisition__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__earliestExpectedDate____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__email_not_sent": "", "@sage/xtrem-purchasing/pages__purchase_requisition__email_sent": "", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressApproval____helperText": "", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressChanges____helperText": "", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressChanges____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__isTransferHeaderNote____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__isTransferLineNote____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__item_mandatory": "", "@sage/xtrem-purchasing/pages__purchase_requisition__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__columns__title__symbol__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__stockUnit__name__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__stockUnit__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__supplier__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__supplier__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__unit__name__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__unit__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__currency__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__stockUnit__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__unit__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__approvalStatus": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__currency__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__lineOrderStatus": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__needByDate": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__orderedPercentage": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__orderedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantityToOrder": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__requestedItemDescription": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__site__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__stockUnit__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__totalTaxExcludedAmount": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__unit__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists": "", "@sage/xtrem-purchasing/pages__purchase_requisition__noteSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__number____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__orderStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_order_created": "", "@sage/xtrem-purchasing/pages__purchase_requisition__Purchase_order_not_created": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_fail": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_success": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_unit_mandatory": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__orderedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__purchaseOrderLine__document__orderDate": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__purchaseOrderLine__status": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseRequisitionLineCount____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier": "", "@sage/xtrem-purchasing/pages__purchase_requisition__reject____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__request_date_cannot_be_future": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requestApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requestApprovalSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requestChanges____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requestChangesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requestDate____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__save____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__select_approver_button_text": "", "@sage/xtrem-purchasing/pages__purchase_requisition__select_supplier_link_text": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedUser____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedUser____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedUser____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__send_approval_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_requisition__send_change_request_button_text": "", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__legalCompany__description": "", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__site____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_requisition__site____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__status____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__status_not_updated": "", "@sage/xtrem-purchasing/pages__purchase_requisition__status_updated": "", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_approving": "", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_confirm": "", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_order": "", "@sage/xtrem-purchasing/pages__purchase_requisition__substitute_approver": "", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__purchaseLeadTime": "", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__type": "", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition__users____columns__title__email": "", "@sage/xtrem-purchasing/pages__purchase_requisition__users____columns__title__firstName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__users____columns__title__lastName": "", "@sage/xtrem-purchasing/pages__purchase_requisition__users____columns__title__type": "", "@sage/xtrem-purchasing/pages__purchase_requisition__users____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_approval____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_approval__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_select_supplier": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__confirm__lower_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__confirm_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__create_credit_memo_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__currency__id__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__currency__id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__currency__id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__currency__id__title__4": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__currency__id__title__5": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__item__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__item__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__stockUnit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__approvalStatus": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__document__number": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__item__image": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__lineOrderStatus": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__needByDate": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__orderedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__orderedQuantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__quantityToOrder": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__quantityToOrderInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__requestedItemDescription": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__requestedItemDescription__2": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__purchaseRequisitionLines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__site____columns__title__legalCompany__id": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__site____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel__supplier____title": "", "@sage/xtrem-purchasing/pages__purchase_requisition_table_panel_zero_quantity_error": "", "@sage/xtrem-purchasing/pages__purchase_requisitionLine_invoicedQuantity_to_much": "", "@sage/xtrem-purchasing/pages__purchase_requisitionLine_orderedQuantity_to_much": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__5": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__id__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line3__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line6__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line7__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line9__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__title__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__10": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__11": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__8": "", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__9": "", "@sage/xtrem-purchasing/pages__purchase_return____objectTypePlural": "", "@sage/xtrem-purchasing/pages__purchase_return____objectTypeSingular": "", "@sage/xtrem-purchasing/pages__purchase_return____title": "", "@sage/xtrem-purchasing/pages__purchase_return__allocationStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_return__apply_dimensions_success": "", "@sage/xtrem-purchasing/pages__purchase_return__approval_status_not_updated": "", "@sage/xtrem-purchasing/pages__purchase_return__approval_status_updated": "", "@sage/xtrem-purchasing/pages__purchase_return__approvalStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_return__approvalStatusHidden____title": "", "@sage/xtrem-purchasing/pages__purchase_return__approve____title": "", "@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____title": "", "@sage/xtrem-purchasing/pages__purchase_return__close____title": "", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_1": "", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_2": "", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__rounding": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_return__companyFxRate____title": "", "@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__createPurchaseInvoice____title": "", "@sage/xtrem-purchasing/pages__purchase_return__currency____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_return__currency____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__currency____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return__currency____placeholder": "", "@sage/xtrem-purchasing/pages__purchase_return__currency____title": "", "@sage/xtrem-purchasing/pages__purchase_return__customSave____title": "", "@sage/xtrem-purchasing/pages__purchase_return__defaultDimension____title": "", "@sage/xtrem-purchasing/pages__purchase_return__displayStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_return__externalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_return__externalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_return__externalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_return__externalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_return__fxRateDate____title": "", "@sage/xtrem-purchasing/pages__purchase_return__goToSysNotificationPage____title": "", "@sage/xtrem-purchasing/pages__purchase_return__headerSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__informationBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return__informationSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__internalNote____helperText": "", "@sage/xtrem-purchasing/pages__purchase_return__internalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_return__internalNoteLine____helperText": "", "@sage/xtrem-purchasing/pages__purchase_return__internalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_return__invoice_created": "", "@sage/xtrem-purchasing/pages__purchase_return__invoice_not_created": "", "@sage/xtrem-purchasing/pages__purchase_return__isExternalNote____title": "", "@sage/xtrem-purchasing/pages__purchase_return__isExternalNoteLine____title": "", "@sage/xtrem-purchasing/pages__purchase_return__isTransferHeaderNote____title": "", "@sage/xtrem-purchasing/pages__purchase_return__isTransferLineNote____title": "", "@sage/xtrem-purchasing/pages__purchase_return__itemsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__lineCount____title": "", "@sage/xtrem-purchasing/pages__purchase_return__lineDetailPanelBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return__lineDetailPanelSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__4": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__5": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__stockUnit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__stockUnit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__unit__symbol__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__unit__symbol__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__reason": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__columns__title__decimalDigits": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__columns__title__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__postfix__charge": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__postfix__discount": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__allocationStatus": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__approvalStatus": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__charge": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__discount": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__isExternalNote": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__lineAmountExcludingTaxInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__origin": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__purchaseReceiptLine__returnedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantityAllocated": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantityInStockUnit": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__reason": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__shippedStatus": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__status": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__stockUnit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__unitToStockUnitConversionFactor": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__4": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____inlineActions__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__line2__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__line2Right__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__title__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__titleRight__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title__3": "", "@sage/xtrem-purchasing/pages__purchase_return__lines____title": "", "@sage/xtrem-purchasing/pages__purchase_return__noteBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return__notesSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__number____title": "", "@sage/xtrem-purchasing/pages__purchase_return__post____title": "", "@sage/xtrem-purchasing/pages__purchase_return__post__allocation_status": "", "@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_content": "", "@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__post_from_main_row": "", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__financeIntegrationAppRecordId": "", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__postingStatus": "", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__targetDocumentNumber": "", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__targetDocumentType": "", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____title": "", "@sage/xtrem-purchasing/pages__purchase_return__postingMessageBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return__postingSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__document__number": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__unit__symbol": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____dropdownActions__title": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____title": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseOrderPod____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title___id": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title___id__2": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title__purchaseReceiptLine__status": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title__returnedQuantity": "", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____title": "", "@sage/xtrem-purchasing/pages__purchase_return__rateDescription____title": "", "@sage/xtrem-purchasing/pages__purchase_return__reject____title": "", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_1": "", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_2": "", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return__repost____title": "", "@sage/xtrem-purchasing/pages__purchase_return__repost_errors": "", "@sage/xtrem-purchasing/pages__purchase_return__requestApproval____title": "", "@sage/xtrem-purchasing/pages__purchase_return__return_date_cannot_be_future": "", "@sage/xtrem-purchasing/pages__purchase_return__return_items__uncheck_return_items_with_allocations": "", "@sage/xtrem-purchasing/pages__purchase_return__return_request_date_cannot_be_future": "", "@sage/xtrem-purchasing/pages__purchase_return__returnItems____title": "", "@sage/xtrem-purchasing/pages__purchase_return__returnRequestDate____title": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__columns__legalCompany__name__title": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__columns__legalCompany__name__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__id": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__name": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____title": "", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_return__selectFromReceipt____title": "", "@sage/xtrem-purchasing/pages__purchase_return__shippingStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_return__status____title": "", "@sage/xtrem-purchasing/pages__purchase_return__status_updated": "", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_allocate": "", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_approve": "", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_creation": "", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_post": "", "@sage/xtrem-purchasing/pages__purchase_return__stockSite____title": "", "@sage/xtrem-purchasing/pages__purchase_return__stockTransactionStatus____title": "", "@sage/xtrem-purchasing/pages__purchase_return__submit_for_approval_from_main_row": "", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____title": "", "@sage/xtrem-purchasing/pages__purchase_return__supplierReturnReference____title": "", "@sage/xtrem-purchasing/pages__purchase_return__supplierSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__text____title": "", "@sage/xtrem-purchasing/pages__purchase_return__textSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalAmountExcludingTax____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalAmountExcludingTaxInCompanyCurrency____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalExcludingTaxValue____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalIncludingTaxValue____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalsSection____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalsSectionCompanyCurrencyDetailsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching___no_results": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__cancel____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__confirm____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__confirm__lower_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__confirm_greater_quantity": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__confirm_lower_quantity": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__dialog_title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__error_zero_or_negative": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__item____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__item____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__item____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__lineBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__linesBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__pdfBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReceipt____columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReceipt____columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReceipt____columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReceipt____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReceipt____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturn____columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturn____columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturn____columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturn____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturn____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__2": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__displayStatus": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__grossPrice": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__item__id": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__item__name": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__itemDescription": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__lineAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__number": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__priceOrigin": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__quantity": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__remainingQuantityToCredit": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__purchaseReturns____levels__columns__title__totalAmountExcludingTax": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__resultsBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__search": "", "@sage/xtrem-purchasing/pages__purchase_return_matching__select_purchase_receipt_or_purchase_return": "", "@sage/xtrem-purchasing/pages__purchase_return_select_supplier": "", "@sage/xtrem-purchasing/pages__purchase_supplier__default_buyer": "", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_invoice": "", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_receipt_from_purchase_return": "", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_return": "", "@sage/xtrem-purchasing/pages__purchasing_order__on_price_determination_title": "", "@sage/xtrem-purchasing/pages__purchasing_order__update_price_from_quantity": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__cancelPurchaseOrder____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__createPurchaseOrder____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__date____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__expectedReceiptDate____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__grossPrice____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__item____columns__title__category__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__item____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__priceOrigin____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__projected_stock_button_text": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__purchaseQuantity____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__select_supplier_link_text": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__site____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__site____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockQuantity____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____columns__title__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__supplier____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__id": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__purchaseLeadTime": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__type": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____title": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____columns__title__name": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____columns__title__symbol": "", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____title": "", "@sage/xtrem-purchasing/pages__site_extension_control_PO_approval": "", "@sage/xtrem-purchasing/pages__site_extension_control_PR_approval": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__asOfDate____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____columns__title__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____columns__title__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____placeholder": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__criteriaBlock____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__executionDate____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__account__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__account__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__accountItem__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__accountItem__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__billBySupplier": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__billBySupplier__businessEntity__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__company__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__company__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__companyCurrency__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__companyCurrency__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__creditedQuantity": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__currency__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__currency__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__documentDate": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__financialSite": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__financialSite__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoicedQuantity": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmount": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmountInCompanyCurrency": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmountInCompanyCurrencyAtAsOfDate": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableQuantity": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__item__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__item__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__netPrice": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__purchaseUnit__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__quantity": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__receiptInternalId": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__returnedQuantity": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__stockSite": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__stockSite__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__mainSection____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_inquiry_finished": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_request_sent": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__resultsSection____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__runUnbilledAccountPayableInquiry____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____columns__title__legalCompany__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____placeholder": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__status____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__id": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__name": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____lookupDialogTitle": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____title": "", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__user____title": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_content": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_title": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_content": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_title": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_content": "", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_title": "", "@sage/xtrem-purchasing/pages_confirm_button": "", "@sage/xtrem-purchasing/pages_purchase_order_receipt_date_cannot_be_less_than_order_date": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_order": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_orders": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_price": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_purchase": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_quantity": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_stock": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_totals": "", "@sage/xtrem-purchasing/pages_sidebar_block_title_variance": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_address": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_information": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_matching": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_origin": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_price": "", "@sage/xtrem-purchasing/pages_sidebar_tab_title_progress": "", "@sage/xtrem-purchasing/pages_warning_button": "", "@sage/xtrem-purchasing/pages-confirm-approve": "", "@sage/xtrem-purchasing/pages-confirm-cancel": "", "@sage/xtrem-purchasing/pages-confirm-change": "", "@sage/xtrem-purchasing/pages-confirm-continue": "", "@sage/xtrem-purchasing/pages-confirm-create": "", "@sage/xtrem-purchasing/pages-confirm-post": "", "@sage/xtrem-purchasing/pages-confirm-reject": "", "@sage/xtrem-purchasing/pages-confirm-send": "", "@sage/xtrem-purchasing/pages-confirm-submit": "", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_content": "", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_title": "", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_content": "", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_title": "", "@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-cancel": "", "@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-continue": "", "@sage/xtrem-purchasing/permission__accept_all_variances__name": "", "@sage/xtrem-purchasing/permission__approve__name": "", "@sage/xtrem-purchasing/permission__close__name": "", "@sage/xtrem-purchasing/permission__confirm__name": "", "@sage/xtrem-purchasing/permission__manage__name": "", "@sage/xtrem-purchasing/permission__post__name": "", "@sage/xtrem-purchasing/permission__read__name": "", "@sage/xtrem-purchasing/purchase_order__email_subject": "", "@sage/xtrem-purchasing/purchase_order__lib__purchase_receipt_creation_failed": "", "@sage/xtrem-purchasing/purchase_order_approval_managed": "", "@sage/xtrem-purchasing/purchase_order_lib__resync__end": "", "@sage/xtrem-purchasing/purchase_order_lib__resync__start": "", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_invoice_creation_failed": "", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_failed": "", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_succeeded": "", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order__price_not_set": "", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_failed": "", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_succeeded": "", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_required_date_not_met": "", "@sage/xtrem-purchasing/purchase_return__lib__credit_memo_creation_failed": "", "@sage/xtrem-purchasing/purchase_return__lib__no_financial_site": "", "@sage/xtrem-purchasing/purchase_return__purchase_return_already_closed": "", "@sage/xtrem-purchasing/purchase_return__purchase_return_line_without_item": "", "@sage/xtrem-purchasing/purchase_return__purchase_return_not_found": "", "@sage/xtrem-purchasing/purchase_return__uncheck_return_items_approved_or_rejected": "", "@sage/xtrem-purchasing/purchase_return__uncheck_return_items_with_allocations": "", "@sage/xtrem-purchasing/search": "", "@sage/xtrem-purchasing/status_and_is_purchase_requisition_approval_managed": "", "@sage/xtrem-purchasing/widgets__orders_not_received____callToActions__SeeAll__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__number__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__orderDate__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__status__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line2__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line2Right__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line3__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line3Right__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__title__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__titleRight__title": "", "@sage/xtrem-purchasing/widgets__orders_not_received____title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____callToActions__SeeAll__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__number__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__receiptDate__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__status__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line2__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line2Right__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line3__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__title__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__titleRight__title": "", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____callToActions__SeeAll__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__number__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__receiptDate__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__status__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line2__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line2Right__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line3__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__title__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__titleRight__title": "", "@sage/xtrem-purchasing/widgets__unposted_receipts____title": ""}