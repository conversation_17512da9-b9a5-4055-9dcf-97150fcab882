import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';
import { sameAddressesProperty, sameCommonProperties, sameSupplierProperty, siteLocalized } from './same-properties';

// this have to be converted into a generic function
export async function sameProperties(
    orderLineToReceiptLine: xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine,
    cx: ValidationContext,
) {
    const differentProperties: string[] = [];

    const from = await orderLineToReceiptLine.purchaseOrderLine;
    const to = await orderLineToReceiptLine.purchaseReceiptLine;
    const fromDocument = await from.document;
    const toDocument = await to.document;
    const { context } = orderLineToReceiptLine.$;

    differentProperties.push(...(await sameCommonProperties(context, { from, to })));

    const fromSite = await from.stockSite;
    const toSite = await (await to.document).site;
    if (toSite._id !== fromSite._id) {
        differentProperties.push(siteLocalized(context));
    }

    differentProperties.push(...(await sameAddressesProperty(context, { from, to })));

    differentProperties.push(...(await sameSupplierProperty(context, { from: fromDocument, to: toDocument })));

    if (differentProperties.length) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__same_properties_order_to_receipt',
            'The purchase receipt must have the same values for the following properties: \n {{differentProperties}}',
            { differentProperties: differentProperties.join(' \n ') },
        );
    }
}
