import type { Context } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { loggers } from '../../functions/loggers';
import type * as xtremPurchasing from '../../index';

const itemLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__differentProperties_item', 'item');
export const unitLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_purchase_unit', 'purchase unit');
export const billBySupplierLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_bill_by_supplier', 'billBySupplier');
export const siteLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_site', 'site');
export const currencyLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_currency', 'currency');
export const conversionFactorLocalized = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_unit_conversion_factor', 'unit conversion factor');

export const sameSupplierAddress = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes_different_supplier_address', 'supplier address');

export const sameReceivingSiteAddress = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_receiving_site_address', 'receiving site address');

export const sameSupplier = (context: Context) =>
    context.localize('@sage/xtrem-purchasing/nodes__different_supplier', 'supplier');

type PurchaseLines =
    | xtremPurchasing.nodes.PurchaseOrderLine
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseReceiptLine;
type PurchaseDocument =
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseReceipt;

interface Lines {
    from: PurchaseLines;
    to: PurchaseLines;
}

interface LinesToInvoiceLines {
    from: PurchaseLines;
    to: xtremPurchasing.nodes.PurchaseInvoiceLine;
}

interface Documents {
    from: PurchaseDocument;
    to: PurchaseDocument;
}

interface OrderToReceiptLines {
    from: xtremPurchasing.nodes.PurchaseOrderLine;
    to: xtremPurchasing.nodes.PurchaseReceiptLine;
}

/* check if same item , unit , site , currency */
export async function sameCommonProperties(context: Context, lines: Lines): Promise<string[]> {
    const differentProperties: string[] = [];

    if ((await lines.from.item)._id !== (await lines.to.item)?._id) {
        differentProperties.push(itemLocalized(context));
        await loggers.same.debugAsync(
            async () => `from : ${await (await lines.from.item).id} to : ${await (await lines.to.item).id}`,
        );
    }

    if ((await lines.from.unit) && (await lines.to.unit)._id !== (await lines.from.unit)._id) {
        differentProperties.push(unitLocalized(context));
        await loggers.same.debugAsync(
            async () => `from : ${await (await lines.from.unit).id} to : ${await (await lines.to.unit).id}`,
        );
    }

    if (
        (await lines.from.currency) &&
        (await (await lines.to.document).currency)._id !== (await lines.from.currency)._id
    ) {
        differentProperties.push(currencyLocalized(context));
        await loggers.same.debugAsync(
            async () =>
                `from : ${await (await lines.from.currency).id} to : ${await (
                    await (
                        await lines.to.document
                    ).currency
                ).id}  `,
        );
    }

    if ((await lines.to.unitToStockUnitConversionFactor) !== (await lines.from.unitToStockUnitConversionFactor)) {
        differentProperties.push(conversionFactorLocalized(context));
        await loggers.same.debugAsync(
            async () =>
                `from : ${await lines.from.unitToStockUnitConversionFactor} to : ${await lines.to.unitToStockUnitConversionFactor}`,
        );
    }

    return differentProperties;
}

export async function sameInvoiceProperties(context: Context, lines: LinesToInvoiceLines): Promise<string[]> {
    const differentProperties: string[] = [];

    const billBySupplierFrom = await (await lines.from.document).billBySupplier;
    const billBySupplierTo = await (await lines.to.document).billBySupplier;
    if (billBySupplierFrom && billBySupplierTo._id !== billBySupplierFrom._id) {
        differentProperties.push(billBySupplierLocalized(context));
    }

    const fromSite = await lines.from.site;
    const toSite = await (await lines.to.document).site;
    if (
        (await (await lines.from.document).site) &&
        toSite._id !== fromSite._id &&
        toSite._id !== (await fromSite.financialSite)?._id
    ) {
        differentProperties.push(siteLocalized(context));
    }

    return differentProperties;
}

async function logAddressComparison(
    from: xtremMasterData.nodes.Address | null,
    to: xtremMasterData.nodes.Address | null,
) {
    const logger = [`_id => from : '${from?._id}' to : '${to?._id}'`];
    if (!from || !to) {
        return logger.join(' \n');
    }
    logger.push(`name => from : ${await from.name} to : ${await to.name}`);
    logger.push(`line1 => from : ${await from.addressLine1} to : ${await to.addressLine1}`);
    logger.push(`line2 => from : ${await from.addressLine2} to : ${await to.addressLine2}`);
    logger.push(`city => from : ${await from.city} to : ${await to.city}`);

    return logger.join(' \n');
}

/** as address are  isContentAddressable: true if address are identical we must have the same _id */
export async function sameAddressesProperty(context: Context, lines: OrderToReceiptLines): Promise<string[]> {
    const differentProperties: string[] = [];
    const from = await lines.from.document;
    const to = await lines.to.document;

    const supplierAddress = await to.supplierAddress;

    if (supplierAddress) {
        if (
            !(await xtremMasterData.functions.areSameAddresses(
                supplierAddress,
                (await from.supplierAddress) || (await from.supplierLinkedAddress),
            ))
        ) {
            differentProperties.push(sameSupplierAddress(context));
            await loggers.same.debugAsync(async () => logAddressComparison(supplierAddress, await to.supplierAddress));
        }
    }

    const receivingAddress = await to.receivingAddress;
    if (receivingAddress) {
        if (
            !(await xtremMasterData.functions.areSameAddresses(
                receivingAddress,
                (await lines.from.stockSiteAddress) || (await lines.from.stockSiteLinkedAddress),
            ))
        ) {
            differentProperties.push(sameReceivingSiteAddress(context));
            await loggers.same.debugAsync(async () =>
                logAddressComparison(await lines.from.stockSiteAddress, receivingAddress),
            );
        }
    }
    return differentProperties;
}

export async function sameSupplierProperty(context: Context, documents: Documents) {
    if ((await documents.from.businessRelation)._id !== (await documents.to.businessRelation)._id) {
        return [sameSupplier(context)];
    }
    return [];
}
