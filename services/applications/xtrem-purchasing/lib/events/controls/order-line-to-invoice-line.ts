import type { ValidationContext } from '@sage/xtrem-core';
import { loggers } from '../../functions/loggers';
import type * as xtremPurchasing from '../../index';
import { sameCommonProperties, sameInvoiceProperties } from './same-properties';

// this have to be converted into a generic function
export async function sameProperties(
    orderLineToInvoiceLine: xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine,
    cx: ValidationContext,
) {
    const differentProperties: string[] = [];

    const from = await orderLineToInvoiceLine.purchaseOrderLine;
    const to = await orderLineToInvoiceLine.purchaseInvoiceLine;
    const { context } = orderLineToInvoiceLine.$;

    differentProperties.push(...(await sameCommonProperties(context, { from, to })));

    differentProperties.push(...(await sameInvoiceProperties(context, { from, to })));

    if (differentProperties.length) {
        loggers.same.debug(() => ' --- From purchase order line to purchase invoice line --- ');
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_invoice__same_properties_invoice_to_order',
            'The purchase invoice must have the same values for the following properties: \n {{differentProperties}}',
            { differentProperties: differentProperties.join(' \n ') },
        );
    }
}
