import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';
import { sameCommonProperties, sameInvoiceProperties } from './same-properties';

export async function sameProperties(
    orderLineToReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
    cx: ValidationContext,
) {
    const differentProperties: string[] = [];
    const from = await orderLineToReceiptLine.purchaseReceiptLine;
    const to = await orderLineToReceiptLine.purchaseInvoiceLine;
    const { context } = orderLineToReceiptLine.$;

    differentProperties.push(...(await sameCommonProperties(context, { from, to })));

    differentProperties.push(...(await sameInvoiceProperties(context, { from, to })));

    if (differentProperties.length) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_invoice__same_properties_invoice_to_receipt',
            'The purchase invoice must have the same values for the following properties: \n {{differentProperties}}',
            { differentProperties: differentProperties.join(' \n ') },
        );
    }
}
