import type { Dict, ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue, date } from '@sage/xtrem-date-time';
import type { BaseOpenItem, FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
// Had to do this \(override from /lib/interfaces in /client-functions/interfaces\) to avoid using server entities (from /lib/nodes or enums etc) from some of the interface properties
// import * as accountingInterfaces from '@sage/xtrem-finance-data/build/lib/interfaces/accounting-integration';
import { Decimal } from '@sage/xtrem-decimal';
import {
    assignEmailContactFrom,
    confirmDialogWithAcceptButtonText,
    loadContacts,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Contact,
    Currency,
    Customer,
    CustomerPriceReason,
    Item,
    ItemCustomer,
    PaymentTerm,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import { fetchDefaultsForDueDate } from '@sage/xtrem-master-data/lib/client-functions/common';
import { checkStockSite } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type {
    GraphApi,
    SalesCreditMemoDisplayStatus,
    SalesCreditMemoLine,
    SalesCreditMemoLineBinding,
    SalesCreditMemo as SalesCreditMemoNode,
    SalesCreditMemoReason,
    SalesCreditMemoTax,
    SalesPriceOrigin,
} from '@sage/xtrem-sales-api';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableFieldActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import type { TotalTaxCalculatorState } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/total-tax-calculator';
import * as ui from '@sage/xtrem-ui';
import {
    commonUpdateGrossProfit,
    getFullyPaidFilter,
    getNotFullyPaidFilter,
    showErrorsBlockingMessage,
    showTaxCalcFiledPostBlockingMessage,
    showTaxCalcFiledPrintBlockingMessage,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-sales-credit-memo';
import { displayTaxes } from '../client-functions/display-taxes';
import { getSiteAndCustomer } from '../client-functions/finance-integration';
import type { SalesCreditMemo as salesCreditMemoInterfaces } from '../client-functions/interfaces';
import type { SalesCreditMemoStepSequenceStatus } from '../client-functions/interfaces/interfaces';
import { calculateLinePrices, getPrices, isExchangeRateHidden } from '../client-functions/page-functions';
import * as actionFunctions from '../client-functions/sales-credit-memo-actions-functions';

@ui.decorators.page<SalesCreditMemo, SalesCreditMemoNode>({
    title: 'Sales credit memo',
    objectTypeSingular: 'Sales credit memo',
    objectTypePlural: 'Sales credit memos',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesCreditMemo',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 400,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.avalara, this.post, this.repost],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [],
            quickActions: [this.print],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.sendEmail,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.creditMemoStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    onLoad() {
        this.creditMemoStepSequence.statuses = this.getDisplayStatusStepSequence();
        this.initParams();
        this.initPage();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
        this.manageDisplayButtonGoToSysNotificationPageAction();
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxDetails, this.taxEngine.value ?? '');
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulkSalesCreditMemo',
                title: 'Print',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesCreditMemo',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
            line2: ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'billToCustomer',
                title: 'Bill-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Credit memo date', isMandatory: true }),
            line_4: ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line_5: ui.nestedFields.numeric<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
                scale: null,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            line14: ui.nestedFields.date({ bind: 'dueDate', title: 'Due date' }),
            line6: ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'reason',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesCreditMemoDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line7: ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line8: ui.nestedFields.text({
                bind: 'creationNumber',
                title: 'Creation number',
                isHiddenOnMainField: true,
            }),
            line9: ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ bind: 'symbol', canFilter: false }),
                ],
            }),
            line10: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isPrinted ? 'tick' : 'none'),
            }),
            line11: ui.nestedFields.icon({
                bind: 'isSent',
                title: 'Sent',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isSent ? 'tick' : 'none'),
            }),
            line12: ui.nestedFields.numeric<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line13: ui.nestedFields.numeric<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'totalGrossProfit',
                title: 'Gross profit',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line15: ui.nestedFields.numeric<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            paymentStatus: ui.nestedFields.label<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'paymentStatus',
                title: 'Payment status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.paymentStatus),
            }),
            billToLinkedAddress: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode, BusinessEntityAddress>(
                {
                    bind: 'billToLinkedAddress',
                    node: '@sage/xtrem-master-data/BusinessEntityAddress',
                    nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                },
            ),
            billToContact: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode, Contact>({
                bind: 'billToContact',
                node: '@sage/xtrem-master-data/Contact',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'title' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                ],
            }),
            status: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode>({ bind: 'status' }),
            taxCalculationStatus: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'taxCalculationStatus',
            }),
            openItemSysId: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode>({ bind: 'openItemSysId' }),
            isOpenItemPageOptionActive: ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoNode>({
                bind: 'isOpenItemPageOptionActive',
            }),
        },
        optionsMenu(_graph, _storage, _queryParam, _username, _userCode, serviceOptions) {
            return Promise.resolve([
                {
                    title: 'All open statuses',
                    graphQLFilter: {
                        displayStatus: { _nin: ['posted', 'partiallyPaid', 'paid'] },
                    },
                },
                { title: 'All statuses', graphQLFilter: {} },
                { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
                { title: 'Posted', graphQLFilter: { displayStatus: { _eq: 'posted' } } },
                { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
                ...(serviceOptions.paymentTrackingOption ? [getNotFullyPaidFilter(false)] : []),
                ...(serviceOptions.paymentTrackingOption ? [getFullyPaidFilter()] : []),
                { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
                { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
            ] as ui.containers.OptionsMenuItemType<SalesCreditMemoNode>[]);
        },
        dropdownActions: [
            {
                title: 'Post',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    if (rowItem.taxCalculationStatus === 'failed') {
                        showTaxCalcFiledPostBlockingMessage(this);
                        return;
                    }
                    await actionFunctions.postAction({
                        creditMemoPage: this,
                        recordId,
                        isCalledFromRecordPage: false,
                        taxCalculationStatus: rowItem.taxCalculationStatus,
                        status: rowItem.status ?? '',
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: { status: rowItem.status, taxCalculationStatus: rowItem.taxCalculationStatus },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Open item',
                icon: 'none',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    await this.$.dialog.page(
                        `@sage/xtrem-finance/AccountsReceivableOpenItem`,
                        { _id: rowItem.openItemSysId ?? '', fromSales: true },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return rowItem.status !== 'posted' || !rowItem.isOpenItemPageOptionActive;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                icon: 'print',
                refreshesMainList: 'record',
                access: { node: '@sage/xtrem-sales/SalesCreditMemo', bind: 'beforePrint' },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    if (rowItem.taxCalculationStatus === 'failed') {
                        showTaxCalcFiledPrintBlockingMessage(this);
                        return;
                    }
                    if (rowItem.status === 'error') {
                        showErrorsBlockingMessage(this);
                        return;
                    }
                    if (rowItem.number) {
                        if (recordId && rowItem.number && rowItem.status && rowItem.isPrinted !== undefined) {
                            if (
                                await actionFunctions.canPrintCreditMemo({
                                    salesCreditMemoPage: this,
                                    number: rowItem.number ?? '',
                                    isCalledFromRecordPage: false,
                                    _id: recordId,
                                    status: '',
                                })
                            ) {
                                await actionFunctions.printSalesCreditMemoAction({
                                    isCalledFromRecordPage: false,
                                    salesCreditMemoPage: this,
                                    _id: recordId,
                                    status: rowItem.status,
                                    number: rowItem.number,
                                });
                            } else {
                                throw new Error(
                                    ui.localize(
                                        '@sage/xtrem-sales/pages__sales_invoice__open_items_update_error',
                                        'The total amount paid could not be updated from Sage Intacct, the sales invoice cannot be printed.',
                                    ),
                                );
                            }
                        }
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return displayButtons.isHiddenButtonPrintAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return (
                        displayButtons.isDisabledButtonPrintAction({
                            parameters: {
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Send',
                icon: 'email',
                refreshesMainList: 'record',
                async onClick(recordId, rowItem) {
                    this._id.value = recordId;
                    this.sendEmailSection.isHidden = false;
                    if (rowItem.billToContact) {
                        await loadContacts(rowItem.billToContact, this, rowItem.billToLinkedAddress?._id || '');
                    }
                    await this.$.dialog.custom('info', this.sendEmailSection, {
                        cancelButton: { isHidden: true },
                        acceptButton: { isHidden: true },
                        resolveOnCancel: true,
                    });
                    this.sendEmailSection.isHidden = true;
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return displayButtons.isHiddenButtonSendMailAction({
                        parameters: {
                            status: rowItem.status,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                    });
                },
                isDisabled(recordId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return (
                        displayButtons.isDisabledButtonSendMailAction({
                            parameters: {
                                status: rowItem.status,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    if (
                        !['posted', 'inProgress', 'error'].includes(rowItem.status ?? '') ||
                        this.fromNotificationHistory
                    ) {
                        const { site, customer } = await getSiteAndCustomer({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            customerId: rowItem.billToCustomer?._id ?? '',
                        });
                        await actionFunctions.setDimensions({
                            salesCreditMemoPage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site,
                            customer,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status, taxCalculationStatus: rowItem.taxCalculationStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesCreditMemo',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesCreditMemoNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class SalesCreditMemo extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    displayTaxesClicked = false;

    currentSelectedLineId: string;

    fromNotificationHistory: boolean;

    canEditPaymentDataAfterPost: boolean;

    get salesSiteCurrency() {
        return this.site.value?.legalCompany?.currency;
    }

    private readonly creditMemoStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_creation',
        'Create',
    );

    private readonly creditMemoStepSequencePost = ui.localize(
        '@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_post',
        'Post',
    );

    private readonly creditMemoStepSequencePay = ui.localize(
        '@sage/xtrem-sales/pages__sales_credit_memo__step_sequence_pay',
        'Pay',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.paymentStatus.value === 'paid') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                pay: 'complete',
            });
        }
        if (this.paymentStatus.value === 'partiallyPaid') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
                pay: 'current',
            });
        }
        if (['inProgress', 'error'].includes(this.status.value || '')) {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'current' });
        }
        if (this.status.value === 'posted') {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'complete' });
        }
        return this._setStepSequenceStatusObject({ create: 'current', post: 'incomplete' });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: [],
            remove: this.$standardDeleteAction,
            businessActions: [this.$standardOpenCustomizationPageWizardAction, this.avalara, this.post, this.repost],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonAvalaraAction(isDirty, this.status.value);
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonPrintAction(isDirty);
        this.manageDisplayButtonSendMailAction(isDirty);
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    manageDisplayButtonAvalaraAction(isDirty: boolean, status: string | null) {
        // implemented in the avalara-gateway extension, not private
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: { status: this.status.value, taxCalculationStatus: this.taxCalculationStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { fromNotificationHistory: this.fromNotificationHistory },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value, taxCalculationStatus: this.taxCalculationStatus.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                dueDate: this.dueDate.value,
                paymentTerm: this.paymentTerm.value,
                date: this.date.value,
                currency: this.currency.value,
            },
        });
    }

    private manageDisplayButtonPrintAction(isDirty: boolean) {
        this.print.isHidden = displayButtons.isHiddenButtonPrintAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
        });

        this.print.isDisabled = displayButtons.isDisabledButtonPrintAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSendMailAction(isDirty: boolean) {
        this.sendEmail.isHidden = displayButtons.isHiddenButtonSendMailAction({
            parameters: {
                status: this.status.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
        });

        this.sendEmail.isDisabled = displayButtons.isDisabledButtonSendMailAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.billToAddress?._id) < 0) {
            delete values.billToAddress._id;
        }

        this.getLineValues(values.lines);
        return values;
    }

    // TODO: todo when implementing addressesDetail on lines
    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines?: Partial<Dict<any>>[]) {
        lines?.forEach(line => {
            if (line.consumptionAddress && Number(line.consumptionAddress._id) < 0) {
                delete line.consumptionAddress._id;
            }
        });
    }

    @ui.decorators.pageAction<SalesCreditMemo>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        async onClick() {
            await this.$standardSaveAction.execute();
            const financeIntegrationCheckResult = await this.$.graph
                .node('@sage/xtrem-sales/SalesCreditMemo')
                .mutations.financeIntegrationCheck(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    { creditMemo: this._id.value ?? '' },
                )
                .execute();

            if (!financeIntegrationCheckResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__save_warnings',
                        'Warnings while saving:',
                    )}**\n${financeIntegrationCheckResult.message}`,
                    { type: 'warning', timeout: 20000 },
                );
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<SalesCreditMemo>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesCreditMemo>({
        title: 'Print',
        icon: 'print',
        access: { node: '@sage/xtrem-sales/SalesCreditMemo', bind: 'beforePrint' },
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                showTaxCalcFiledPrintBlockingMessage(this);
                return;
            }
            if (this.status.value === 'error') {
                showErrorsBlockingMessage(this);
                return;
            }
            await this.printSalesCreditMemo();
        },
    })
    print: ui.PageAction;

    @ui.decorators.textField<SalesCreditMemo>({
        isHidden: true,
        title: 'ID',
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.section<SalesCreditMemo>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<SalesCreditMemo>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<SalesCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return !['partiallyPaid', 'paid'].includes(this.paymentStatus.value ?? 'notPaid')
                ? [this.creditMemoStepSequenceCreate, this.creditMemoStepSequencePost]
                : [this.creditMemoStepSequenceCreate, this.creditMemoStepSequencePost, this.creditMemoStepSequencePay];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    creditMemoStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesCreditMemo, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical<SalesCreditMemo, Site, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<SalesCreditMemo, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<SalesCreditMemo, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'doStockPosting' }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical<SalesCreditMemo, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesCreditMemo, Company, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
            ui.nestedFields.technical<SalesCreditMemo, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical<SalesCreditMemo, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        filter: { isSales: true },
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
            if (this.site.value) {
                this.taxEngine.value = this.site.value.legalCompany?.taxEngine ?? '';
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value;
            }
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesCreditMemo, Customer>({
        parent() {
            return this.headerBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Bill-to customer',
        lookupDialogTitle: 'Select bill-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesCreditMemo, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesCreditMemo, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesCreditMemo, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesCreditMemo, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
        ],
        async onChange() {
            this.updatePrefixScaleOnCurrencyFields();
            await this.fetchDefaultsFromBillToCustomer();
            if (this.billToCustomer.value?.businessEntity?.currency) {
                this.currency.value = this.billToCustomer.value.businessEntity.currency;
            }
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
        },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        title: 'Credit memo date',
        isMandatory: true,
        fetchesDefaults: true,
        onChange() {
            this.dueDate.value = fetchDefaultsForDueDate({
                paymentTerm: this.paymentTerm.value,
                baseDate: this.date.value ? date.parse(this.date.value) : null,
                dueDateValue: this.dueDate.value,
            });
            this.manageDisplayButtonDefaultDimensionAction();
            if (
                this.date.value &&
                Date.parse(this.date.value) > Date.now() &&
                this.site?.value?.legalCompany?.legislation?.id !== 'FR'
            ) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__credit_memo_date__cannot__be__future',
                        'The credit memo date cannot be later than today.',
                    ),
                );
            }
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<SalesCreditMemo>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    salesCreditMemoLineCount: ui.fields.Count;

    @ui.decorators.aggregateField<SalesCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'lineAmountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    salesCreditMemoExcludingTax: ui.fields.Aggregate;

    @ui.decorators.aggregateField<SalesCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'lineAmountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        width: 'medium',
    })
    salesCreditMemoIncludingTax: ui.fields.Aggregate;

    @ui.decorators.numericField<SalesCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        bind: 'totalAmountPaid',
        title: 'Total payments',
        size: 'medium',
        unit() {
            return this.currency.value;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    amountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Net balance',
        size: 'medium',
        unit() {
            return this.currency.value;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    netBalance: ui.fields.Numeric;

    @ui.decorators.labelField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.referenceField<SalesCreditMemo, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-to address',
        lookupDialogTitle: 'Select bill-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesCreditMemo, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({
                        bind: 'regionLabel',
                    }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesCreditMemo, BusinessEntityAddress>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.billToCustomer.value
                ? { businessEntity: { id: this.billToCustomer?.value?.businessEntity?.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromBillToAddress();
        },
    })
    billToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.referenceField<SalesCreditMemo, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length || 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    private applyCompanyCurrency() {
        this.totalAmountExcludingTaxInCompanyCurrency.scale =
            this.site.value?.businessEntity?.currency?.decimalDigits ?? 2;
        this.totalAmountExcludingTaxInCompanyCurrency.prefix = this.site.value?.businessEntity?.currency?.symbol || '';
        this.totalAmountIncludingTaxInCompanyCurrency.scale =
            this.site.value?.businessEntity?.currency?.decimalDigits ?? 2;
        this.totalAmountIncludingTaxInCompanyCurrency.prefix = this.site.value?.businessEntity?.currency?.symbol || '';
    }

    @ui.decorators.dateField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden() {
            // XT-24062 - Hide exchange rate for now
            // return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
            return true;
        },
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.dropdownListField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.labelField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Posting status',
        bind: 'status',
        optionType: '@sage/xtrem-sales/SalesCreditMemoStatus',
        isHidden: true,
        width: 'small',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<SalesCreditMemo>({
        title: 'Display status',
        optionType: '@sage/xtrem-sales/SalesCreditMemoDisplayStatus',
        onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-sales/page__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<SalesCreditMemoDisplayStatus>;

    @ui.decorators.tableField<SalesCreditMemo, SalesCreditMemoLine>({
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        hasLineNumbers: true,
        node: '@sage/xtrem-sales/SalesCreditMemoLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'originDocumentType',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isExcludedFromMainField: true,
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' || this.taxEngine.value !== 'genericTaxCalculation'
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData.taxCalculationStatus),
                async onClick(_rowId, rowItem) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                valueField: 'name',
                helperTextField: 'id',
                isAutoSelectEnabled: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<SalesCreditMemo, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesCreditMemo, Item, UnitOfMeasure>({
                        bind: 'salesUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'minimumSalesQuantity' }),
                    ui.nestedFields.technical({ bind: 'maximumSalesQuantity' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        await this.checkItemCustomer(rowData, true);
                        rowData.stockUnit = rowData.item.stockUnit;

                        const quantity = rowData.quantity ?? 0;
                        const unitToStockUnitConversionFactor = rowData.unitToStockUnitConversionFactor ?? 1;
                        rowData.quantityInStockUnit = String(+quantity * +unitToStockUnitConversionFactor);
                        await this.retrieveItemSites(rowData);
                        if (rowData.providerSite) {
                            await this.getPriceDetermination(rowData, true);
                            rowData.providerSite = await checkStockSite(this, rowData.item, rowData.providerSite);
                        }
                        this.updateGrossProfit(rowData);
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isMandatory: true,
                scale: (_value, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
                validation(val) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__quantity_in_sales_unit_negative_value',
                            'The quantity in sales unit cannot be less than or equal to 0.',
                        );
                    }
                    return undefined;
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    const oldQuantityInStockUnit = rowData.quantityInStockUnit;
                    if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.quantityInStockUnit = String(
                            +rowData.quantity * +rowData.unitToStockUnitConversionFactor,
                        );
                    }
                    if (
                        (rowData.quantityInStockUnit ? parseFloat(rowData.quantityInStockUnit) : 0) !==
                        (oldQuantityInStockUnit ? parseFloat(oldQuantityInStockUnit) : 0)
                    ) {
                        const isPriceDetermined = await this.priceDetermination2(rowData);
                        if (!isPriceDetermined) {
                            await this.calculatePrices(rowData);
                        }
                        this.updateGrossProfit(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isHiddenOnMainField: true,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                scale: (_value, rowData) => rowData?.stockUnit?.decimalDigits || 0,
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden(value, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                unit() {
                    return this.currency.value;
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    if (!rowData.grossPrice) rowData.grossPrice = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    this.updateGrossProfit(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__gross_price_greater_or_equal_to_0',
                            'The gross price needs to be more than or equal to 0.',
                        );
                    }
                    return undefined;
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__percentage_is_negative',
                            'The percentage cannot be negative.',
                        );
                    }
                    if (val > 100) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__percentage_greater_than_100',
                            'The percentage cannot exceed 100.',
                        );
                    }
                    return undefined;
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    if (!rowData.discount) rowData.discount = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    this.updateGrossProfit(rowData);
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__percentage_is_negative',
                            'The percentage cannot be negative.',
                        );
                    }
                    if (val > 100) {
                        return ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__percentage_greater_than_100',
                            'The percentage cannot exceed 100.',
                        );
                    }
                    return undefined;
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    if (!rowData.charge) rowData.charge = '0';
                    await this.calculatePrices(rowData);
                    this.setPriceOriginAndReason(rowData);
                    this.updateGrossProfit(rowData);
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.numeric({
                bind: 'grossPriceDeterminated',
                isHidden: true,
            }),
            ui.nestedFields.technical({ bind: 'discountDeterminated' }),
            ui.nestedFields.technical({ bind: 'chargeDeterminated' }),
            ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoLine>({ bind: 'priceOriginDeterminated' }),
            ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoLine, CustomerPriceReason>({
                bind: 'priceReasonDeterminated',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isPriceDeterminated' }),
            ui.nestedFields.numeric({
                title: 'Net price',
                isReadOnly: true,
                bind: 'netPrice',
                unit() {
                    return this.currency.value;
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.label<SalesCreditMemo, SalesCreditMemoLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-sales/SalesPriceOrigin',
                isHiddenOnMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, CustomerPriceReason>({
                title: 'Price reason',
                bind: 'priceReason',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                valueField: 'name',
                isHiddenOnMainField: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.numeric({ bind: 'priority', title: 'Priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                isReadOnly: true,
                bind: 'lineAmountExcludingTax',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'lineAmountExcludingTaxInCompanyCurrency',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                isReadOnly: true,
                isHidden() {
                    return (
                        this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation'
                    );
                },
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                isReadOnly: true,
                bind: 'lineAmountIncludingTax',
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'lineAmountIncludingTaxInCompanyCurrency',
                unit() {
                    return this.salesSiteCurrency;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden() {
                    return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
                },
            }),

            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost amount',
                bind: 'stockCostAmount',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Gross profit amount',
                bind: 'grossProfitAmount',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.technical({ bind: 'stockCostUnit' }),

            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, Site>({
                title: 'Provider site',
                lookupDialogTitle: 'Select provider site',
                bind: 'providerSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                minLookupCharacters: 0,
                isAutoSelectEnabled: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<SalesCreditMemo, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'isInventory' }),
                ],
                async onChange(rowId, rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    await this.priceDetermination2(rowData);
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
            }),
            ui.nestedFields.reference<SalesCreditMemo, SalesCreditMemoLine, BusinessEntityAddress>({
                bind: 'consumptionLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.checkbox({ bind: 'isActive' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'addressLine1' }),
                    ui.nestedFields.text({ bind: 'addressLine2' }),
                    ui.nestedFields.text({ bind: 'city' }),
                    ui.nestedFields.text({ bind: 'region' }),
                    ui.nestedFields.text({ bind: 'postcode' }),
                    ui.nestedFields.reference<SalesCreditMemo, BusinessEntityAddress, Country>({
                        bind: 'country',
                    }),
                    ui.nestedFields.text({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical<SalesCreditMemo, BusinessEntityAddress, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
                filter() {
                    return this.billToCustomer.value
                        ? { businessEntity: { id: this.billToCustomer?.value?.businessEntity?.id } }
                        : {};
                },
            }),

            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),

            ui.nestedFields.technical<SalesCreditMemo, SalesCreditMemoLine, Address>({
                bind: 'consumptionAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable:
                                    !['posted', 'inProgress', 'error'].includes(this.status.value ?? '') ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                isHidden: (_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) => !rowItem.uiTaxes,
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
                isHidden() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                },
                async onClick(rowId, rowItem) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_credit_memo__lines_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_credit_memo_delete_action_dialog_content',
                                'You are about to delete this sales credit memo line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                        await this.calculateTaxExcludedTotalAmount();
                        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                        this.disableSomeHeaderPropertiesIfLines();
                    }
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'lineAmountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (recordValue && +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-sales/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },

            headerDropdownActions: [
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable:
                                        !['posted', 'inProgress', 'error'].includes(this.status.value ?? '') ||
                                        this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden: (_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) =>
                        !rowItem.uiTaxes,
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
                        if (rowItem.uiTaxes) {
                            await this.callDisplayTaxes(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isDisabled() {
                        return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                    },
                    isHidden() {
                        return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],

            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                this.currentSelectedLineId = recordValue?._id ?? '';
                this.internalNoteLine.value = recordValue?.internalNote?.value ?? '';
                this.externalNoteLine.value = recordValue?.externalNote?.value ?? '';
                this.isExternalNoteLine.value = recordValue?.isExternalNote ?? null;
                this.externalNoteLine.isDisabled = !recordValue?.isExternalNote;
                this.consumptionAddress.value = recordValue?.consumptionAddress ?? null;
                if (recordValue && recordValue._id && +recordValue._id > 0) {
                    await this.fillSalesLinkedDocuments(recordValue._id, recordValue);
                    TotalTaxCalculator.getInstance().synchronizeState(
                        JSON.parse(
                            String(JSON.stringify(TotalTaxCalculator.getInstance().getState())),
                        ) as TotalTaxCalculatorState,
                    );
                } else if (recordValue) {
                    recordValue.site = this.site.value as unknown as Site;
                    if (
                        this._defaultDimensionsAttributes.dimensions !== '{}' ||
                        this._defaultDimensionsAttributes.attributes !== '{}'
                    ) {
                        // using as any cause partial type is used in the function and not for linedata
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            recordValue as any,
                            this._defaultDimensionsAttributes,
                        ) as any;
                        recordValue.storedAttributes = line.storedAttributes;
                        recordValue.storedDimensions = line.storedDimensions;
                    }
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<SalesCreditMemoLine>,
                    );
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    recordValue.externalNote.value = this.externalNoteLine.value ? this.externalNoteLine.value : '';
                    recordValue.isExternalNote = this.isExternalNoteLine.value || false;
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<SalesCreditMemoLine>,
                    );
                    await TotalTaxCalculator.getInstance().updateTaxDetails(
                        this.taxDetails,
                        this.totalTaxAmountAdjusted,
                        this.totalTaxAmount,
                    );
                    await this.calculateTaxExcludedTotalAmount();
                    recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                }
            },

            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'originDocumentType', 'itemDescription', 'providerSite'],
                            },
                            sales: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stock: {
                                isHidden(rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem?.item.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: {
                                fields: ['grossPrice', 'discount', 'charge', 'netPrice'],
                            },
                            mainBlock1: {
                                fields: ['priceOrigin', 'priceReason'],
                            },
                            totals: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['lineAmountExcludingTax', 'taxAmount', 'lineAmountIncludingTax'],
                            },
                            totals2: {
                                fields: [
                                    'stockCostAmount',
                                    'grossProfitAmount',
                                    'lineAmountExcludingTaxInCompanyCurrency',
                                    'lineAmountIncludingTaxInCompanyCurrency',
                                ],
                            },
                        },
                    },
                    address: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_address', 'Address'),
                        blocks: {
                            mainBlock: {
                                fields: [this.consumptionAddress],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.salesLinkedDocuments.value.length === 0;
                        },
                        blocks: {
                            mainBlock: {
                                fields: [this.salesLinkedDocuments],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesCreditMemoLine>;

    @ui.decorators.pageAction<SalesCreditMemo>({
        title: 'Post',
        isHidden: true,
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                showTaxCalcFiledPostBlockingMessage(this);
                return;
            }
            if (this._id.value && this.taxCalculationStatus.value && this.status.value) {
                await actionFunctions.postAction({
                    creditMemoPage: this,
                    recordId: this._id.value,
                    isCalledFromRecordPage: true,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                    status: this.status.value,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<SalesCreditMemo>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            await actionFunctions.postDetails(this, this.postingDetails.value);

            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-sales/SalesCreditMemo')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    {
                        salesCreditMemo: this._id.value || '',
                        documentData: {
                            header: {
                                paymentTerm: this.canEditPaymentDataAfterPost
                                    ? (this.paymentTerm?.value?._id ?? 0)
                                    : undefined,
                            },
                            lines: documentLines,
                        },
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__repost_errors',
                        'Errors occurred while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<SalesCreditMemo>({
        // To be implemented into the page extension from the avalara-gateway package
        onClick() {},
        isHidden: true,
    })
    avalara: ui.PageAction;

    @ui.decorators.tableField<SalesCreditMemo, salesCreditMemoInterfaces.LinkedDocument>({
        title: 'Origin document',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Origin document number',
                bind: '_id',
                map(_fieldValue, rowData) {
                    return `${rowData.number}`;
                },
                page(_value, rowData) {
                    return rowData?.nodeName;
                },
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.url ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Document type',
                bind: 'documentType',
                isDisabled: true,
            }),
            ui.nestedFields.technical({ bind: 'uDocumentStatus' }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'documentStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.documentStatus),
            }),
        ],
    })
    salesLinkedDocuments: ui.fields.Table<salesCreditMemoInterfaces.LinkedDocument>;

    @ui.decorators.vitalPodField<SalesCreditMemo, Address>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Consumption address',
        isFullWidth: true,
        isTransient: true,
        onAddButtonClick() {
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryRegionTitle(rowItem?.consumptionAddress);
                },
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryPostCodeTitle(rowItem?.consumptionAddress);
                },
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-structure/Country',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.consumptionAddress.isReadOnly === true;
                },
            }),
        ],
    })
    consumptionAddress: ui.fields.VitalPod;

    @ui.decorators.section<SalesCreditMemo>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Sent',
        isDisabled: true,
    })
    isSent: ui.fields.Checkbox;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Creation number',
        isReadOnly: true,
        isHidden() {
            return this.site?.value?.legalCompany?.legislation?.id !== 'FR';
        },
    })
    creationNumber: ui.fields.Text;

    @ui.decorators.referenceField<SalesCreditMemo, Currency>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        onChange() {
            this.updatePrefixScaleOnCurrencyFields();
            this.manageDisplayButtonDefaultDimensionAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.billToCustomer.value,
            );
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.billToCustomer.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.referenceField<SalesCreditMemo, SalesCreditMemoReason>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-sales/SalesCreditMemoReason',
        title: 'Reason',
        lookupDialogTitle: 'Select reason',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    reason: ui.fields.Reference<SalesCreditMemoReason>;

    @ui.decorators.vitalPodField<SalesCreditMemo, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.billToLinkedAddress.value) {
                const { ...values } = { ...this.billToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '') || !this.$.recordId;
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return (
                        ['posted', 'inProgress', 'error'].includes(this.status.value ?? '') ||
                        !this.billToAddress.isReadOnly ||
                        !this.$.recordId
                    );
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billToAddress.isReadOnly = true;

                    if (this.billToAddress.value) {
                        this.billToAddress.value.concatenatedAddress = getConcatenatedAddress(this.billToAddress.value);
                    }
                },
                isDisabled() {
                    return (
                        ['posted', 'inProgress', 'error'].includes(this.status.value ?? '') ||
                        this.billToAddress.isReadOnly ||
                        !this.$.recordId
                    );
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    billToAddress: ui.fields.VitalPod;

    @ui.decorators.section<SalesCreditMemo>({
        title: 'Totals',
    })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        width: 'large',
        title: '',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Tax adjusted',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.salesSiteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Gross profit',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalGrossProfit: ui.fields.Numeric;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Summary by tax',
        width: 'large',
    })
    totalsSectionTaxDetailsBlock: ui.containers.Block;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Amounts company currency',
        width: 'large',
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesCreditMemo>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<SalesCreditMemo>({
        title: 'Add notes to customer document',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<SalesCreditMemo>({
        isFullWidth: true,
        title: 'Customer notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'posted';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.tableField<SalesCreditMemo, SalesCreditMemoTax>({
        bind: 'taxes',
        title: 'Taxes',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-sales/SalesCreditMemoTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSectionTaxDetailsBlock;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({
                title: 'Category',
                bind: 'taxCategory',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Tax',
                bind: 'tax',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Rate',
                postfix: '%',
                scale: 2,
                bind: 'taxRate',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({ bind: 'isReverseCharge' }),
        ],
    })
    taxDetails: ui.fields.Table<SalesCreditMemoTax>;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<SalesCreditMemo>({
        title: 'Posting',
        isHidden() {
            return !this.site.value?.legalCompany?.doStockPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesCreditMemo, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page(_value, rowData) {
                    switch (rowData?.targetDocumentType) {
                        case 'journalEntry':
                            return '@sage/xtrem-finance/JournalEntry';
                        case 'accountsReceivableInvoice':
                        default:
                            return '@sage/xtrem-finance/AccountsReceivableInvoice';
                    }
                },
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table;

    @ui.decorators.textAreaField<SalesCreditMemo>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<SalesCreditMemo>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'salesCreditMemo',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.section<SalesCreditMemo>({ title: 'Payments' })
    paymentsSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
        title: 'Payments',
        isTitleHidden: true,
    })
    paymentsBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesCreditMemo, PaymentTerm>({
        parent() {
            return this.paymentsBlock;
        },
        lookupDialogTitle: 'Select payment term',
        width: 'small',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        filter: { businessEntityType: { _in: ['customer', 'all'] } },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'days' }),
        ],
        async onChange() {
            await this.fetchDefaultsFromPaymentTerm();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.dateField<SalesCreditMemo>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Due date',
        isMandatory: true,
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    dueDate: ui.fields.Date;

    @ui.decorators.tableField<SalesCreditMemo, BaseOpenItem>({
        bind: { arOpenItems: true },
        isHidden: true,
        node: '@sage/xtrem-finance-data/BaseOpenItem',
        columns: [
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { documentNumber: true } }),
        ],
    })
    arOpenItems: ui.fields.Table<BaseOpenItem>;

    @ui.decorators.checkboxField<SalesCreditMemo>({ isHidden: true })
    isOpenItemPageOptionActive: ui.fields.Checkbox;

    @ui.decorators.linkField<SalesCreditMemo>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Open item',
        width: 'small',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                `@sage/xtrem-finance/AccountsReceivableOpenItem`,
                { _id: this.arOpenItems.value?.at(0)?._id ?? '', fromSales: true },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return this.arOpenItems.value?.at(0)?.documentNumber ?? '';
        },
        isHidden() {
            return this.status.value !== 'posted' || !this.isOpenItemPageOptionActive.value;
        },
        isDisabled() {
            return this.$.queryParameters.fromFinance?.toString() === 'true';
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.labelField<SalesCreditMemo>({
        bind: 'paymentStatus',
        isHidden: true,
        optionType: '@sage/xtrem-finance-data/OpenItemStatus',
    })
    paymentStatus: ui.fields.Label;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Total amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
        width: 'small',
        isHidden() {
            return this.status.value !== 'posted' || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    totalAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<SalesCreditMemo>({
        title: 'Forced amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        width: 'small',
        isHidden() {
            return (
                this.status.value !== 'posted' ||
                !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                !this.forcedAmountPaid.value ||
                this.forcedAmountPaid.value === 0
            );
        },
    })
    forcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
    })
    receiptsBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<SalesCreditMemo>({
        isTitleHidden: true,
        parent() {
            return this.receiptsBlock;
        },
        fragment: '@sage/xtrem-sales/SalesReceiptLine',
    })
    receipts: ui.containers.FragmentFields;

    @ui.decorators.section<SalesCreditMemo>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesCreditMemo>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'posted';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesCreditMemo>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'posted';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<SalesCreditMemo>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'posted';
        },
    })
    externalNote: ui.fields.RichText;

    // Send credit memo by email start here
    @ui.decorators.pageAction<SalesCreditMemo>({
        isTransient: true,
        isHidden: true,
        title: 'Send',
        icon: 'email',
        async onClick() {
            this.sendEmailSection.isHidden = false;
            if (this.billToContact.value) {
                await loadContacts(this.billToContact.value, this, this.billToLinkedAddress?.value?._id || '');
            }

            await this.$.dialog.custom('info', this.sendEmailSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.sendEmailSection.isHidden = true;
            this.$.setPageClean();
        },
    })
    sendEmail: ui.PageAction;

    @ui.decorators.section<SalesCreditMemo>({ isHidden: true, title: 'Send sales credit memo' })
    sendEmailSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.sendEmailSection;
        },
        title: 'To',
    })
    sendEmailBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesCreditMemo, Contact>({
        parent() {
            return this.contactSelectionBlock;
        },
        lookupDialogTitle: 'Select bill-to customer address contact detail',
        title: 'Bill-to customer address contact detail',
        isTitleHidden: true,
        isHidden: true,
        node: '@sage/xtrem-master-data/Contact',
        valueField: 'email',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
    })
    billToContact: ui.fields.Reference<Contact>;

    @ui.decorators.dropdownListField<SalesCreditMemo>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Title',
        optionType: '@sage/xtrem-master-data/Title',
        isTransient: true,
        isFullWidth: true,
    })
    emailTitle: ui.fields.DropdownList;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'First name',
        maxLength: 30,
        isTransient: true,
        isFullWidth: true,
    })
    emailFirstName: ui.fields.Text;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Last name',
        maxLength: 30,
        isMandatory: true,
        isTransient: true,
        isFullWidth: true,
    })
    emailLastName: ui.fields.Text;

    @ui.decorators.textField<SalesCreditMemo>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Email',
        helperText: 'A sales credit memo will be sent to this address.',
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_credit_memo__invalid-email',
                    'Email address incorrect: {{email}}',
                    {
                        email: val,
                    },
                );
            }
            return undefined;
        },
    })
    emailAddress: ui.fields.Text;

    @ui.decorators.buttonField<SalesCreditMemo>({
        title: 'Bill-to customer contact',
        isTitleHidden: true,
        isTransient: true,
        width: 'small',
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_credit_memo__select_bill_to_contact_button_text',
                'Select bill-to customer contact',
            );
        },
        async onClick() {
            this.contactSelectionSection.isHidden = false;
            await this.$.dialog
                .custom('info', this.contactSelectionSection, { resolveOnCancel: true })
                .then(() => {
                    if (this.selectedContact.value) assignEmailContactFrom(this.selectedContact.value, this);
                })
                .finally(() => {
                    this.selectedContact.value = null;
                    this.contactSelectionSection.isHidden = false;
                });
        },
    })
    selectBillToContact: ui.fields.Button;

    @ui.decorators.buttonField<SalesCreditMemo>({
        isTransient: true,
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_button_text', 'Send');
        },
        onError(error) {
            this.$.loader.isHidden = true;
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_credit_memo__email_exception',
                    'Could not send sales credit memo email. ({{exception}})',
                    { exception: error.message },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_dialog_title',
                        'Sales credit memo email sending confirmation',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__send_credit_memo_dialog_content',
                        'You are about to send the sales credit memo email.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-send', 'Send'),
                )
            ) {
                if (
                    await actionFunctions.canPrintCreditMemo({
                        salesCreditMemoPage: this,
                        number: this.number.value ?? '',
                        status: this.status.value ?? '',
                        _id: this._id.value ?? '',
                        isCalledFromRecordPage: true,
                    })
                ) {
                    this.$.loader.isHidden = false;

                    // Send the email
                    await this.$.graph
                        .node('@sage/xtrem-sales/SalesCreditMemo')
                        .mutations.printSalesCreditMemoAndEmail(true, {
                            creditMemo: this._id.value ?? '',
                            contactTitle: this.emailTitle.value ?? '',
                            contactFirstName: this.emailFirstName.value ?? '',
                            contactLastName: this.emailLastName.value ?? '',
                            contactEmail: this.emailAddress.value ?? '',
                        })
                        .execute();

                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-sales/pages__sales_credit_memo__email_sent',
                            'Sales credit memo sent to {{email}}.',
                            { email: this.emailAddress.value },
                        ),
                        { type: 'success' },
                    );
                    this.$.setPageClean();
                    await this.$.refreshNavigationPanel();
                    await this.$.router.refresh();
                    this.sendEmailSection.isHidden = true;
                    this.$.loader.isHidden = true;
                }
            }
        },
        title: 'Send credit memo',
        isTitleHidden: true,
    })
    sendCreditMemoButton: ui.fields.Button;

    @ui.decorators.section<SalesCreditMemo>({ isHidden: true, isTitleHidden: true })
    contactSelectionSection: ui.containers.Section;

    @ui.decorators.block<SalesCreditMemo>({
        parent() {
            return this.contactSelectionSection;
        },
        title: '',
        isTitleHidden: true,
    })
    contactSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesCreditMemo, BusinessEntityContact>({
        parent() {
            return this.contactSelectionSection;
        },
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        canSelect: false,
        title: 'Contacts',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        orderBy: { lastName: +1, firstName: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Title',
                bind: 'title',
                optionType: '@sage/xtrem-master-data/enums/title',
            }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
        ],
        onRowClick(_rowId, rowData) {
            // TODO: need to be fixed later
            this.selectedContact.value = rowData as unknown as ExtractEdgesPartial<BusinessEntityContact>;
        },
    })
    contacts: ui.fields.Table<BusinessEntityContact>;

    @ui.decorators.referenceField<SalesCreditMemo, BusinessEntityContact>({
        parent() {
            return this.contactSelectionBlock;
        },
        title: 'Selected contact',
        lookupDialogTitle: 'Select selected contact',
        isTitleHidden: true,
        isReadOnly: true,
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.technical({ bind: 'email' }),
            ui.nestedFields.technical({ bind: 'preferredName' }),
            ui.nestedFields.technical({ bind: 'role' }),
            ui.nestedFields.technical({ bind: 'position' }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
            ui.nestedFields.text({ title: 'ID', bind: '_id' }),
        ],
    })
    selectedContact: ui.fields.Reference<BusinessEntityContact>;

    // Send credit memo by email end here

    initPage() {
        // TODO: when enabling direct creation, remove this line below
        this.$standardNewAction.isHidden = true;
        this.billToAddress.isReadOnly = true;
        if (this.$.recordId) {
            if (['posted', 'inProgress', 'error'].includes(this.status.value ?? '')) {
                this.site.isDisabled = true;
                this.date.isDisabled = true;
                this.billToCustomer.isDisabled = true;
                this.paymentTerm.isDisabled = !this.canEditPaymentDataAfterPost;
                this.dueDate.isDisabled = true;
                this.currency.isDisabled = true;
                this.reason.isDisabled = true;
                this.billToAddress.isDisabled = true;
            } else {
                this.site.isDisabled = true;
                this.date.isDisabled = false;
                this.billToCustomer.isDisabled = true;
                this.paymentTerm.isDisabled = false;
                this.dueDate.isDisabled = false;
                this.currency.isDisabled = true;
                this.reason.isDisabled = false;
                this.billToAddress.isDisabled = false;
            }
            this.updatePrefixScaleOnCurrencyFields();
            this.documentNumberLink.value = this.arOpenItems.value?.at(0)?.documentNumber ?? '';
        } else {
            this.date.value = DateValue.today().toString();
            // TODO: when enabling direct creation, remove these lines below
            this.number.isDisabled = true;
            this.site.isDisabled = true;
            this.date.isDisabled = true;
            this.billToCustomer.isDisabled = true;
            this.paymentTerm.isDisabled = true;
            this.dueDate.isDisabled = true;
            this.currency.isDisabled = true;
            this.reason.isDisabled = true;
            this.billToAddress.isDisabled = true;
        }

        if (!this.status.value) {
            this.status.value = 'draft';
        }

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();

        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    initParams() {
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.canEditPaymentDataAfterPost =
            this.fromNotificationHistory && (this.paymentStatus.value ?? 'notPaid') === 'notPaid';
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.billToCustomer.isDisabled = isDisabled;
        this.currency.isDisabled = isDisabled;
    }

    async calculateTaxExcludedTotalAmount(withTaxDetails = true) {
        if (withTaxDetails) {
            await TotalTaxCalculator.getInstance().updateTaxDetails(
                this.taxDetails,
                this.totalTaxAmountAdjusted,
                this.totalTaxAmount,
            );
        }
        this.totalAmountExcludingTax.value = this.lines.value.reduce((accumulator: number, agLine) => {
            if (
                !this.$.values.lines?.find(
                    (pageLine: any) => pageLine._id === agLine._id && pageLine._action === 'delete',
                )
            ) {
                return Number(accumulator + +(agLine.lineAmountExcludingTax ?? 0));
            }
            return accumulator;
        }, 0);
        this.totalAmountIncludingTax.value =
            this.totalAmountExcludingTax.value + (this.totalTaxAmountAdjusted.value ?? 0);

        // Company currency amounts
        this.totalAmountExcludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.lineAmountExcludingTaxInCompanyCurrency, 0),
            this.currency?.value?.decimalDigits,
        );
        this.totalAmountIncludingTaxInCompanyCurrency.value = Decimal.roundAt(
            this.lines.value.reduce((amount: any, line) => amount + line.lineAmountIncludingTaxInCompanyCurrency, 0),
            this.currency?.value?.decimalDigits,
        );
    }

    updatePrefixScaleOnCurrencyFields() {
        this.totalAmountExcludingTax.unit = this.currency.value;
        this.totalAmountIncludingTax.unit = this.currency.value;
        this.totalTaxAmount.unit = this.currency.value;
        this.totalTaxAmountAdjusted.unit = this.currency.value;
    }

    async fillSalesLinkedDocuments(rowId: string, rowItem: SalesCreditMemoLine) {
        const oldIsDirty = this.$.isDirty;
        this.salesLinkedDocuments.value.forEach(elt => {
            this.salesLinkedDocuments.removeRecord(elt._id);
        });

        const salesBaseDocumentLines: Array<ui.PartialNodeWithId<salesCreditMemoInterfaces.LinkedDocument>> = [];
        if (rowItem.originDocumentType === 'return') {
            const salesReturnRequests = await this.$.graph
                .node('@sage/xtrem-sales/SalesReturnRequestLineSalesCreditMemoLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            document: {
                                _id: true,
                            },
                            linkedDocument: {
                                _id: true,
                                document: {
                                    _id: true,
                                    number: true,
                                    status: true,
                                    displayStatus: true,
                                },
                            },
                        },
                        {
                            filter: { document: { _id: rowId } },
                        },
                    ),
                )
                .execute();
            if (salesReturnRequests.edges.length) {
                const linkedSalesReturnRequest = salesReturnRequests.edges[0].node.linkedDocument;
                salesBaseDocumentLines.push({
                    _id: linkedSalesReturnRequest._id,
                    nodeName: '@sage/xtrem-sales/SalesReturnRequest',
                    documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesReturnRequest'),
                    documentStatus: ui.localizeEnumMember(
                        '@sage/xtrem-sales/SalesReturnRequestDisplayStatus',
                        linkedSalesReturnRequest.document.displayStatus,
                    ),
                    uDocumentStatus: linkedSalesReturnRequest.document.displayStatus,
                    url: linkedSalesReturnRequest.document._id,
                    number: linkedSalesReturnRequest.document.number,
                });
            }
        }

        const salesInvoicesLine = await this.$.graph
            .node('@sage/xtrem-sales/SalesInvoiceLineToSalesCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        document: {
                            _id: true,
                        },
                        linkedDocument: {
                            _id: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                                displayStatus: true,
                            },
                        },
                    },
                    {
                        filter: { document: { _id: rowId } },
                    },
                ),
            )
            .execute();

        if (salesInvoicesLine.edges.length) {
            const linkedSalesInvoice = salesInvoicesLine.edges[0].node.linkedDocument;
            salesBaseDocumentLines.push({
                _id: linkedSalesInvoice._id,
                nodeName: '@sage/xtrem-sales/SalesInvoice',
                documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesInvoice'),
                documentStatus: ui.localizeEnumMember(
                    '@sage/xtrem-sales/SalesInvoiceDisplayStatus',
                    linkedSalesInvoice.document.displayStatus,
                ),
                uDocumentStatus: linkedSalesInvoice.document.displayStatus,
                url: linkedSalesInvoice.document._id,
                number: linkedSalesInvoice.document.number,
            });
        }

        this.salesLinkedDocuments.value = salesBaseDocumentLines;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fetchDefaultsFromBillToCustomer() {
        await this.$.fetchDefaults([
            'billToLinkedAddress',
            'billToAddress',
            'billToContact',
            'currency',
            'paymentTerm',
            'dueDate',
            'internalNote',
        ]);
    }

    async fetchDefaultsFromBillToAddress() {
        await this.$.fetchDefaults(['billToAddress', 'billToContact']);
    }

    async fetchDefaultsFromPaymentTerm() {
        await this.$.fetchDefaults(['dueDate']);
    }

    async printSalesCreditMemo() {
        if (
            await actionFunctions.canPrintCreditMemo({
                salesCreditMemoPage: this,
                number: this.number.value ?? '',
                isCalledFromRecordPage: false,
                _id: this._id.value,
                status: '',
            })
        ) {
            await actionFunctions.printSalesCreditMemoAction({
                isCalledFromRecordPage: false,
                salesCreditMemoPage: this,
                _id: this._id.value,
                status: this.status.value,
                number: this.number.value,
            });
        } else {
            throw new Error(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales_invoice__open_items_update_error',
                    'The total amount paid could not be updated from Sage Intacct, the sales invoice cannot be printed.',
                ),
            );
        }
    }

    async callDisplayTaxes(rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
        const result = await displayTaxes(this, rowItem, {
            currency: this.currency.value as ExtractEdgesPartial<Currency>,
            taxAmount: Number(rowItem.taxAmount),
            taxAmountAdjusted: Number(rowItem.taxAmountAdjusted),
            lineAmountExcludingTax: Number(rowItem.lineAmountExcludingTax),
            lineAmountIncludingTax: Number(rowItem.lineAmountIncludingTax),
            documentType: DocumentTypeEnum.salesCreditMemoLine,
            taxDate: rowItem.taxDate,
            legislation: this.site?.value?.legalCompany?.legislation,
            isPosted: ['posted', 'inProgress', 'error'].includes(this.status.value ?? ''),
            destinationCountry: rowItem.consumptionAddress?.country as ExtractEdgesPartial<Country>,
            originCountry: this.site.value?.primaryAddress?.country as ExtractEdgesPartial<Country>,
            validTypes: ['sales', 'purchasingAndSales'],
        });
        if (result) {
            const recordValue = this.lines.getRecordValue(rowItem._id ?? '');
            rowItem.lineAmountIncludingTaxInCompanyCurrency = convertAmount(
                Number(recordValue?.lineAmountIncludingTax),
                this.companyFxRate.value ?? 1,
                this.companyFxRateDivisor.value ?? 1,
                this.currency.value?.decimalDigits ?? 2,
                this.salesSiteCurrency?.decimalDigits ?? 2,
            ).toString();
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                lineAmountIncludingTaxInCompanyCurrency: rowItem.lineAmountIncludingTaxInCompanyCurrency,
            });
            await this.calculateTaxExcludedTotalAmount(false);
        }
    }

    async checkItemCustomer(
        rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>,
        includeSalesUnit: boolean = false,
    ) {
        const itemCustomers = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemCustomer')
                .query(
                    ui.queryUtils.edgesSelector<ItemCustomer>(
                        {
                            _id: true,
                            salesUnit: {
                                _id: true,
                                name: true,
                                id: true,
                                symbol: true,
                                decimalDigits: true,
                            },
                            minimumSalesQuantity: true,
                            maximumSalesQuantity: true,
                            salesUnitToStockUnitConversion: true,
                        },
                        {
                            filter: {
                                item: rowData?.item?._id,
                                customer: String(this.billToCustomer?.value?._id),
                            },
                        },
                    ),
                )
                .execute(),
        );
        const salesToStock = await this.$.graph
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .queries.convertFromTo(false, {
                fromUnit: rowData.item?.salesUnit?._id ?? '',
                toUnit: rowData?.item?.stockUnit?._id ?? '',
                type: 'sales',
                item: rowData?.item?._id,
                quantity: 1,
                formatToUnitDecimalDigits: false,
            })
            .execute();
        if (itemCustomers.length) {
            const itemCustomer = itemCustomers.shift();
            if (includeSalesUnit) {
                rowData.unit = itemCustomer?.salesUnit;
                rowData.unitToStockUnitConversionFactor = itemCustomer?.salesUnitToStockUnitConversion;
            }
        } else if (includeSalesUnit) {
            rowData.unit = rowData.item?.salesUnit;
            rowData.unitToStockUnitConversionFactor = String(salesToStock || 1);
        }
        this.lines.addOrUpdateRecordValue(rowData);
    }

    async getPriceDetermination(
        rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>,
        isWithMessage?: boolean,
    ) {
        const prices = await getPrices(this.$.graph, {
            salesSiteId: rowItem.providerSite?._id ?? '',
            stockSiteId: rowItem.providerSite?._id ?? '',
            soldToCustomerId: this.billToCustomer.value?._id ?? '',
            currencyId: this.currency.value?._id ?? '',
            itemId: rowItem.item?._id ?? '',
            unitId: rowItem.unit?._id ?? '',
            quantity: Number(rowItem.quantity),
            date: new Date(this.date?.value ?? ''),
        });

        rowItem.priceReason = prices.priceReason;
        rowItem.priceOrigin = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPrice = prices.grossPrice.toString();
        rowItem.discount = prices.discount.toString();
        rowItem.charge = prices.charge.toString();
        rowItem.priceReasonDeterminated = prices.priceReason;
        rowItem.priceOriginDeterminated = prices.priceOrigin as SalesPriceOrigin;
        rowItem.grossPriceDeterminated = prices.grossPrice.toString();
        rowItem.discountDeterminated = prices.discount.toString();
        rowItem.chargeDeterminated = prices.charge.toString();

        await this.calculatePrices(rowItem);

        this.lines.addOrUpdateRecordValue(rowItem);
        await this.calculateTaxExcludedTotalAmount();
        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
        this.disableSomeHeaderPropertiesIfLines();

        if (prices.error !== '' && isWithMessage === true) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-sales/page__sales_order_line_panel__no_rate_found',
                    'The price could not been determined from the item sales base price. The currency rate between the sales base price currency and the document currency is missing.',
                ),
                { type: 'warning', timeout: 5000 },
            );
        }
    }

    async priceDetermination2(rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>): Promise<boolean> {
        const errorOnQuantityInSalesUnit = false; // await this.quantityInSalesUnitLine.validate();
        if (
            !errorOnQuantityInSalesUnit &&
            Number(rowItem.quantity) !== 0 &&
            rowItem.item !== null &&
            rowItem.providerSite !== null
        ) {
            if (!rowItem.isPriceDeterminated) {
                await this.getPriceDetermination(rowItem);
                rowItem.isPriceDeterminated = true;
                this.lines.addOrUpdateRecordValue(rowItem);
                return true;
            }
            const response = await this.$.dialog
                .confirmation(
                    'info',
                    ui.localize('@sage/xtrem-sales/pages__sales_order__on_price_determination_title', 'Search price'),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_credit_memo__on_price_determination_confirmation',
                        'Do you want to recalculate prices, discounts, and charges?',
                    ),
                    {
                        cancelButton: { isHidden: false },
                        acceptButton: { isHidden: false },
                    },
                )
                .then(() => true)
                .catch(() => false);
            if (response) {
                await this.getPriceDetermination(rowItem);
                return true;
            }
        }
        return false;
    }

    setPriceOriginAndReason(rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
        if (
            Number(rowData.grossPriceDeterminated) === Number(rowData.grossPrice) &&
            Number(rowData.discountDeterminated) === Number(rowData.discount) &&
            Number(rowData.chargeDeterminated) === Number(rowData.charge)
        ) {
            rowData.priceOrigin = rowData.priceOriginDeterminated;
            rowData.priceReason = rowData.priceReasonDeterminated;
        } else {
            rowData.priceOrigin = 'manual';
            rowData.priceReason = undefined;
        }
        this.lines.addOrUpdateRecordValue(rowData);
    }

    async calculatePrices(rowItem: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
        const prices = await calculateLinePrices({
            grossPrice: Number(rowItem.grossPrice),
            charge: Number(rowItem.charge),
            discount: Number(rowItem.discount),
            netPriceScale: this.currency.value?.decimalDigits ?? 2,
            quantity: Number(rowItem.quantity),
            lineAmountExcludingTax: Number(rowItem.lineAmountExcludingTax),
            lineAmountIncludingTax: Number(rowItem.lineAmountIncludingTax),
            taxAmount: Number(rowItem.taxAmount),
            taxAmountAdjusted: Number(rowItem.taxAmountAdjusted),
            rateMultiplication: this.companyFxRate.value ?? 1,
            rateDivision: this.companyFxRateDivisor.value ?? 1,
            fromDecimals: this.currency.value?.decimalDigits ?? 2,
            toDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
            taxes: {
                site: rowItem?.providerSite?._id,
                businessPartner: this.billToCustomer?.value?._id,
                item: rowItem?.item?._id,
                currency: this.currency?.value?._id,
                lineNodeName: '@sage/xtrem-sales/SalesInvoiceLine',
                taxEngine: this.taxEngine.value ?? '',
                uiTaxes: rowItem.uiTaxes,
                graphObject: this.$.graph,
                taxDate: rowItem.taxDate ?? this.date.value ?? '',
            },
        });
        rowItem.netPrice = prices.netPrice.toString();
        rowItem.lineAmountExcludingTax = prices.lineAmountExcludingTax.toString();
        rowItem.lineAmountIncludingTax = prices.lineAmountIncludingTax.toString();
        rowItem.taxAmount = prices.taxAmount.toString();
        rowItem.taxAmountAdjusted = prices.taxAmountAdjusted.toString();
        rowItem.uiTaxes = prices.uiTaxes.toString();
        rowItem.taxCalculationStatus = prices.taxCalculationStatus;
        rowItem.lineAmountExcludingTaxInCompanyCurrency = prices.lineAmountExcludingTaxInCompanyCurrency.toString();
        rowItem.lineAmountIncludingTaxInCompanyCurrency = prices.lineAmountIncludingTaxInCompanyCurrency.toString();
        this.lines.addOrUpdateRecordValue(rowItem);
    }

    async retrieveItemSites(rowData: ui.PartialCollectionValue<SalesCreditMemoLineBinding>) {
        const itemSites = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            site: {
                                _id: true,
                            },
                        },
                        {
                            filter: {
                                _id: rowData.item?._id,
                            },
                        },
                    ),
                )
                .execute(),
        );

        if (itemSites && itemSites.length > 0) {
            rowData.item = {
                ...rowData.item,
                itemSites,
            };
        }
    }

    _setStepSequenceStatusObject(stepSequenceValues: SalesCreditMemoStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        return {
            [this.creditMemoStepSequenceCreate]: stepSequenceValues.create,
            [this.creditMemoStepSequencePost]: stepSequenceValues.post,
            ...(stepSequenceValues.pay
                ? ({ [this.creditMemoStepSequencePay]: stepSequenceValues.pay } as Dict<ui.StepSequenceStatus>)
                : {}),
        };
    }

    updateGrossProfit(rowData: ExtractEdgesPartial<SalesCreditMemoLineBinding>) {
        // the stock cost unit doesn't change because it comes from the invoice
        commonUpdateGrossProfit(
            this,
            {
                itemId: rowData.item?._id ?? '',
                siteId: rowData.site?._id ?? '',
                rowData: rowData as unknown as SalesCreditMemoLineBinding,
                quantityInStockUnit: Number(rowData.quantityInStockUnit),
                currencyInfo: {
                    rateMultiplication: this.companyFxRateDivisor?.value ?? 0,
                    rateDivision: this.companyFxRate?.value ?? 0,
                    fromDecimals: this.salesSiteCurrency?.decimalDigits ?? 2,
                    toDecimals: this.currency?.value?.decimalDigits ?? 2,
                },
            },
            Number(rowData.stockCostUnit ?? 0) * Number(rowData.quantityInStockUnit ?? 0),
        );
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
