import type { Context, decimal, Dict, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremSales from '../index';
import { BaseSalesDocumentsCreator } from './base-sales-documents-creator';

export class SalesShipmentsCreator extends BaseSalesDocumentsCreator {
    salesOutputDocuments: Dict<NodeCreateData<xtremSales.nodes.SalesShipment>> = {};

    outputNode = xtremSales.nodes.SalesShipment;

    salesDocumentLineDependency: 'salesOrderLines' | 'salesShipmentLines' = 'salesOrderLines';

    numberOfName: 'numberOfShipments' = 'numberOfShipments';

    hasDiscountCharges = true;

    aggregateNode = xtremSales.nodes.SalesOrderLineToSalesShipmentLine;

    static override readonly parametersAreIncorrect: xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues = {
        ...BaseSalesDocumentsCreator.parametersAreIncorrect,
        numberOfShipments: 0,
    };

    constructor(public classContext: Context) {
        super();
    }

    /**
     * In this method we are preparing dicts and arrays that will be used to
     * create new shipments and update sales orders on which base they were created
     * @param salesDocumentLines
     */
    override async prepareNodeCreateData(
        salesDocumentLines: Array<{
            salesDocumentLine: xtremSales.nodes.SalesOrderLine;
            quantityToProcess?: decimal;
        }>,
        options?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<this> {
        if (options?.processAllShippableLines) {
            const refSalesOrderLines = salesDocumentLines;

            this.lineErrors = [
                ...this.lineErrors,
                ...((await asyncArray(salesDocumentLines)
                    .map(async line => {
                        const document = await line.salesDocumentLine.document;
                        const doNotShipAfterDate = await line.salesDocumentLine.doNotShipAfterDate;
                        const doNotShipBeforeDate = await line.salesDocumentLine.doNotShipBeforeDate;
                        const documentLineStatus = await line.salesDocumentLine.status;
                        const todaysDateValue = date.today().value;

                        // Shipping date
                        if (
                            (doNotShipBeforeDate && doNotShipBeforeDate.value > todaysDateValue) ||
                            (doNotShipAfterDate && doNotShipAfterDate.value < todaysDateValue)
                        ) {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__incorrect_shipping_dates',
                                    'The shipping date {{todaysDate}} needs to be between the "Do-not-ship-before date" and "Do-not-ship-after date" on line {{linePosition}}.',
                                    { linePosition: refSalesOrderLines.indexOf(line) + 1, todaysDate: todaysDateValue },
                                ),
                            };
                        }
                        // Customer on hold
                        if (
                            (await (await document.billToCustomer).isOnHold) &&
                            (await (await (await document.site).legalCompany).customerOnHoldCheck) === 'blocking'
                        ) {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__improper_bill_to_customer_on_hold_exception',
                                    'The Bill-to customer is on hold on line {{linePosition}}.',
                                    { linePosition: refSalesOrderLines.indexOf(line) + 1 },
                                ),
                            };
                        }
                        // Line allocation request status in progress
                        if (
                            (await line.salesDocumentLine.allocationRequestStatus) === 'inProgress' &&
                            !options?.isForFinanceCheck
                        ) {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__allocation_request_status_exception',
                                    'You can only ship the sales order line after the allocation request is complete.',
                                ),
                            };
                        }
                        // Line already shipped
                        if ((await line.salesDocumentLine.shippingStatus) === 'shipped') {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__shipping_status_status_exception',
                                    'The sales order line shipped already.',
                                ),
                            };
                        }
                        // Line tax calculation failed
                        if ((await (await line.salesDocumentLine.document).taxCalculationStatus) === 'failed') {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__tax_calculation_status_status_exception',
                                    'The sales order tax calculation failed.',
                                ),
                            };
                        }
                        // Line status Quote
                        if (documentLineStatus === 'quote' && !options?.isForFinanceCheck) {
                            return {
                                lineNumber: line.salesDocumentLine._id,
                                linePosition: refSalesOrderLines.indexOf(line) + 1,
                                message: this.classContext.localize(
                                    '@sage/xtrem-sales/classes__sales_order__line_quote_exception',
                                    'The line status cannot be  "Quote".',
                                ),
                            };
                        }

                        // If no errors and line not closed, create document
                        if (documentLineStatus !== 'closed') {
                            await this.createDictKey(line.salesDocumentLine, document);
                            await this.createOrAppendNewDocument(
                                line.salesDocumentLine,
                                (await line.salesDocumentLine.quantity) -
                                    (await this.getLineTotalShippedQuantity(line.salesDocumentLine)),
                            );
                        }

                        return null;
                    })
                    .filter(line => !!line)
                    .toArray()) as xtremSales.interfaces.CreateSalesOutputDocumentsLineErrorReturnValues[]),
            ];
        } else {
            return super.prepareNodeCreateData(salesDocumentLines, options);
        }
        return this;
    }

    async controlInputLine(
        salesDocumentLine: xtremSales.nodes.SalesOrderLine,
        options?: xtremSales.sharedFunctions.PrepareNodeCreateDataOptions,
    ): Promise<void> {
        const isStatusOk =
            ((await salesDocumentLine.status) !== 'closed' && (await salesDocumentLine.status) !== 'quote') ||
            options?.isForFinanceCheck;
        if (!isStatusOk) {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_order__invalid_selected_lines_status',
                    'The status for selected lines cannot be  "Quote" or "Closed".',
                ),
            );
        }

        if ((await salesDocumentLine.shippingStatus) === 'shipped') {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_order__invalid_selected_lines__shipping_status',
                    'The sales order line shipped already.',
                ),
            );
        }

        if ((await salesDocumentLine.allocationRequestStatus) === 'inProgress' && !options?.isForFinanceCheck) {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_order__invalid_selected_lines__allocation_request_status',
                    'You can only ship the sales order line after the allocation request is complete.',
                ),
            );
        }

        const shippingDateValue = (await salesDocumentLine.shippingDate)?.value;
        const doNotShipBeforeDateValue = (await salesDocumentLine.doNotShipBeforeDate)?.value;
        const doNotShipAfterDateValue = (await salesDocumentLine.doNotShipAfterDate)?.value;

        if (!shippingDateValue || !doNotShipBeforeDateValue || !doNotShipAfterDateValue) {
            return;
        }

        if (shippingDateValue < doNotShipBeforeDateValue || shippingDateValue > doNotShipAfterDateValue) {
            throw new BusinessRuleError(
                this.classContext.localize(
                    '@sage/xtrem-sales/classes__sales_order__improper_shipping_date',
                    'The shipping date cannot be lower than the doNotShipBefore field date and cannot be higher than the doNotShipAfter field date.',
                ),
            );
        }
    }

    async getLineTotalShippedQuantity(line: xtremSales.nodes.SalesOrderLine): Promise<number> {
        return (
            (
                await this.classContext
                    .queryAggregate(xtremSales.nodes.SalesOrderLineToSalesShipmentLine, {
                        filter: {
                            linkedDocument: line,
                        },
                        group: {
                            linkedDocument: { _id: { _by: 'value' } },
                        },
                        values: {
                            quantity: { sum: true },
                        },
                    })
                    .toArray()
            )[0]?.values.quantity.sum || 0
        );
    }

    override async createDictKey(
        line: xtremSales.nodes.SalesOrderLine,
        header: xtremSales.nodes.SalesOrder,
    ): Promise<void> {
        await super.createDictKey(line, header);
        const shipToAddress = await line.shipToAddress;
        this.dictKey +=
            String((await line.site)._id) +
            String((await line.stockSite)._id) +
            String((await header.shipToCustomer)._id) +
            String((await line.shipToCustomerAddress)._id) +
            String((await header.deliveryMode)._id) +
            String(shipToAddress?._id) +
            String(await header.customerNumber);

        if ((await line.shippingDate).value >= date.today().value) {
            this.dictKey += String(await line.shippingDate);
        }
    }

    override async addHeaderDataToDocumentNode(line: xtremSales.nodes.SalesOrderLine): Promise<void> {
        await super.addHeaderDataToDocumentNode(line);
        const header = await line.document;

        this.salesOutputDocuments[this.dictKey] = {
            ...this.salesOutputDocuments[this.dictKey],
            site: (await line.site)?._id,
            stockSite: (await line.stockSite)?._id,
            currency: (await header.currency)?._id,
            paymentTerm: (await header.paymentTerm)?._id,
            shipToCustomer: (await header.shipToCustomer)?._id,
            shipToCustomerAddress: (await line.shipToCustomerAddress)?._id,
            deliveryMode: (await line.deliveryMode)?._id,
            deliveryLeadTime: await header.deliveryLeadTime,
            shipToAddress: await line.shipToAddress,
            shipToContact: await line.shipToContact,
        };
        if ((await line.shippingDate).value >= date.today().value) {
            this.salesOutputDocuments[this.dictKey].shippingDate = await line.shippingDate;
            this.salesOutputDocuments[this.dictKey].deliveryDate = await line.expectedDeliveryDate;
        }
    }

    override async prepareNewLineToDocumentNode(
        line: xtremSales.nodes.SalesOrderLine,
        outputQuantity: decimal,
    ): Promise<number> {
        const SalesShipmentLinesLength = await super.prepareNewLineToDocumentNode(line, outputQuantity);
        const isTransferLineNote = await (await line.document).isTransferLineNote;
        const lineData: NodeCreateData<xtremSales.nodes.SalesShipmentLine> = {
            originDocumentType: 'order',
            grossPrice: await line.grossPrice,
            priceOrigin: await line.priceOrigin,
            priceReason: (await line.priceReason)?._id,
            isPriceDeterminated: await line.isPriceDeterminated,
            grossPriceDeterminated: await line.grossPriceDeterminated,
            priceOriginDeterminated: await line.priceOriginDeterminated,
            priceReasonDeterminated: (await line.priceReasonDeterminated)?._id,
            isExternalNote: isTransferLineNote ? await line.isExternalNote : false,
            externalNote: isTransferLineNote ? await line.externalNote : undefined,
            customerNumber: (await (await line.document).customerNumber) ?? '',
        };

        this.salesOutputDocuments[this.dictKey].lines![SalesShipmentLinesLength - 1] = {
            ...this.salesOutputDocuments[this.dictKey].lines![SalesShipmentLinesLength - 1],
            ...lineData,
        };
        return SalesShipmentLinesLength;
    }

    getCreateSalesOutputDocumentsStatus() {
        let status: string = '';
        if (this.lineErrors[0]?.message) {
            if (Object.keys(this.salesOutputDocuments).length === 0) {
                status = 'salesOrderIsNotShipped';
            } else {
                status = 'salesOrderIsPartiallyShipped';
            }
        } else {
            status = 'salesOrderIsShipped';
        }
        return status;
    }

    createErrorConsoleLog(message: string, inputDocumentsNumbers: string): string | undefined {
        const errorMessage = this.classContext.localize(
            '@sage/xtrem-sales/class__sales_shipments_creator__console_log_error_message',
            `The sales shipment could not be created using the sales order numbers: {{inputDocumentsNumbers}}. Details: {{message}}`,
            {
                message,
                inputDocumentsNumbers,
            },
        );
        this.classContext.logger.error(errorMessage);
        return errorMessage;
    }
}
