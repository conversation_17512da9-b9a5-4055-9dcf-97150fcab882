import type { Collection, Context, decimal, integer, NodeCreateData, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, date, decorators, NodeStatus, TextStream, useDefaultValue } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremSales from '../index';

@decorators.subNode<SalesOrderLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    async createEnd() {
        await xtremSales.functions.updateLineWorkInProgressIfItemStatusActive(this);
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.unchanged) {
            await xtremSales.functions.fromQuoteToPendingUpdateLineStatus(this);
        }

        // Reset the allocationRequestStatus if line is updated
        if (this.$.status === NodeStatus.modified) {
            const old = await this.$.old;
            await xtremSales.functions.resetAllocationRequestStatusOnLineUpdate(this, old);
        }
    },
    async saveEnd() {
        await this.checkQuantityAlreadyShipped();
        await this.checkAndUpdateQuantityOnAssignment();
    },
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.modified && (await this.remainingQuantityToShipInStockUnit) >= 0) {
            await xtremSales.events.control.orderLine.checkIfAllocatedOrderQuantityIsGreaterThanRemainingQuantity(
                cx,
                this,
            );
        }
        if (this.$.status === NodeStatus.added) {
            await xtremSales.events.control.orderLine.checkOrderLineStatus(cx, this);
            await xtremSales.events.control.orderLine.checkIfSameStatus(cx, this);
            await xtremSales.events.control.orderLine.checkIfSameShippingStatus(cx, this);
            await xtremSales.events.control.orderLine.checkIfSameSalesSite(cx, this);
            await xtremSales.events.control.orderLine.checkIfSameStockSiteLegalCompany(cx, this);
        }

        // Automatic allocation in progress : we can't decrease the quantityInSalesUnit
        if (this.$.status === NodeStatus.modified) {
            const old = await this.$.old;
            await xtremSales.events.control.orderLine.checkIfAllocationCompletedBeforeReducingQuantity(cx, this, old);
        }
    },
    async controlDelete(cx) {
        await xtremSales.events.control.orderLine.checkShippingStatusUponLineDeletion(cx, this);
        await xtremSales.events.control.orderLine.removeStockAllocationUponLineDeletion(cx, this);
        await xtremSales.events.control.orderLine.checkAllocationStatusBeforeLineDeletion(cx, this);
    },

    async deleteBegin() {
        await this.checkAndDeleteAssignment();
    },

    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
export class SalesOrderLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    getItem() {
        return this.item;
    }

    @decorators.referenceProperty<SalesOrderLine, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSales.nodes.SalesOrder,
    })
    override readonly document: Reference<xtremSales.nodes.SalesOrder>;

    @decorators.stringPropertyOverride<SalesOrderLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<SalesOrderLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<SalesOrderLine, 'originDocumentType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesOriginDocumentTypeDataType,
        defaultValue: () => 'direct',
        duplicatedValue: useDefaultValue,
    })
    readonly originDocumentType: Promise<xtremSales.enums.SalesOriginDocumentType>;

    @decorators.enumProperty<SalesOrderLine, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesOrderStatusDataType,
        dependsOn: ['document'],
        async defaultValue() {
            return (await (await this.document).status) === 'quote' ? 'quote' : 'pending';
        },
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes_sales_line_on_hold_blocking', 'Bill to customer is on hold.')
                .if(
                    this.$.status === NodeStatus.modified &&
                        xtremSales.events.control.order.onHoldCustomerControl({
                            status: val,
                            prevStatus: await (await this.$.old).status,
                            isOnHold: await (await this.document).isOnHold,
                            customerOnHoldCheck: await (
                                await (
                                    await (
                                        await this.document
                                    ).site
                                ).legalCompany
                            ).customerOnHoldCheck,
                            skipIsOnHold: (await this.document).skipIsOnHold,
                        }),
                )
                .is.true();
        },
    })
    readonly status: Promise<xtremSales.enums.SalesOrderStatus>;

    @decorators.enumProperty<SalesOrderLine, 'invoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentInvoiceStatusDataType,
        defaultValue: 'notInvoiced',
        duplicatedValue: useDefaultValue,
    })
    readonly invoiceStatus: Promise<xtremSales.enums.SalesDocumentInvoiceStatus>;

    @decorators.enumProperty<SalesOrderLine, 'shippingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentShippingStatusDataType,
        defaultValue: 'notShipped',
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly shippingStatus: Promise<xtremSales.enums.SalesDocumentShippingStatus>;

    @decorators.referenceProperty<SalesOrderLine, 'item'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Item,
        ignoreIsActive: true,
        async control(cx, item) {
            if ((await this.status) !== 'closed') {
                await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
            }
        },
        filters: {
            control: {
                isSold: true,
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
            },
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.stringProperty<SalesOrderLine, 'itemDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        dependsOn: ['item'],
        lookupAccess: true,
        async defaultValue() {
            return (await (await this.item)?.description) || (await this.item)?.name;
        },
    })
    readonly itemDescription: Promise<string>;

    /** deprecated */
    @decorators.referenceProperty<SalesOrderLine, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesOrderLine, 'site'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        filters: { control: { isSales: true } },
        dependsOn: [{ document: ['site'] }],
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async defaultValue() {
            return (await (
                await this.document
            )?.site)!;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesOrderLine, 'stockSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['item', 'allocationStatus', { document: ['stockSite', 'site'] }],
        lookupAccess: true,
        filters: {
            control: {
                isInventory: true,
                async legalCompany() {
                    return (await this.site).legalCompany;
                },
                async itemSites() {
                    return (await this.item) ? { _atLeast: 1, item: await this.item } : { _atLeast: 1 };
                },
            },
        },
        async defaultValue() {
            const document = await this.document;
            if (document) {
                return document.stockSite;
            }
            return null;
        },
        async isFrozen() {
            return (
                ['partiallyAllocated', 'allocated'].includes(await this.allocationStatus) ||
                (await this.assignments.length) > 0
            );
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<SalesOrderLine, 'itemSite'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.ItemSite,
        dependsOn: ['item', 'stockSite'],
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.stockSite;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.referenceProperty<SalesOrderLine, 'stockSiteLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['stockSite'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.stockSite).primaryAddress;
        },
    })
    readonly stockSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesOrderLine, 'stockSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['stockSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.stockSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly stockSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesOrderLine, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['shipToCustomerAddress'] }],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.document).shipToCustomerAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await (await this.document).shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesOrderLine, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress', 'document'],
        node: () => xtremMasterData.nodes.Address,

        async defaultValue() {
            const document = await this.document;
            const shipToCustomerAddress = await this.shipToCustomerAddress;
            const documentShipToAddressDetail = await document?.shipToAddress;
            if ((await document?.shipToCustomerAddress) === shipToCustomerAddress && documentShipToAddressDetail) {
                return documentShipToAddressDetail;
            }
            return shipToCustomerAddress.address;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesOrderLine, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress', { document: ['shipToCustomerAddress', 'shipToContact'] }],
        node: () => xtremMasterData.nodes.Contact,

        async defaultValue() {
            const document = await this.document;
            const shipToCustomerAddress = await this.shipToCustomerAddress;
            const documentShipToContact = await document?.shipToContact;
            if ((await document?.shipToCustomerAddress) === shipToCustomerAddress && documentShipToContact) {
                return documentShipToContact;
            }
            return (await shipToCustomerAddress.primaryContact)?.contact || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.decimalProperty<SalesOrderLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['item', 'unit', { document: ['soldToCustomer'] }],
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        lookupAccess: true,
        async control(cx, val) {
            await xtremSales.events.control.quantityInSalesUnitControls.checkQuantityInSalesUnit(
                this.$.context,
                cx,
                val,
                await this.item,
                await (
                    await this.document
                ).soldToCustomer,
                await this.unit,
            );
        },
    })
    readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesOrderLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.referenceProperty<SalesOrderLine, 'unit'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['item', { document: ['soldToCustomer'] }],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async defaultValue() {
            return (await xtremMasterData.functions.getSalesUnitDefaultValue(
                this.$.context,
                await this.item,
                (await (await this.document).soldToCustomer)?._id,
            ))!;
        },
        async control(cx, val) {
            await cx.error
                .if(await val.id)
                .is.not.in(
                    await xtremMasterData.functions.getValidSalesUnitList(
                        this.$.context,
                        await this.item,
                        await (
                            await this.document
                        ).soldToCustomer,
                    ),
                );
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // @TODO DELETE AFTER BASE DOCUMENT REFACTORING
    @decorators.referenceProperty<SalesOrderLine, 'salesUnit'>({
        isPublished: true,
        dependsOn: ['unit'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
    })
    readonly salesUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<SalesOrderLine, 'unitToStockUnitConversionFactor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['item', { document: ['soldToCustomer'] }],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.zero();
        },
        async defaultValue() {
            return xtremMasterData.functions.calculateConversionFactor(
                this.$.context,
                await this.item,
                (await (await this.document).soldToCustomer)?._id,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    // @TODO SHOULD BE REMOVED AFTER BASE DOCUMENT REFACTORING
    @decorators.decimalProperty<SalesOrderLine, 'salesUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
    })
    readonly salesUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['stockUnit', 'quantity', 'unitToStockUnitConversionFactor'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
        defaultValue() {
            return this.quantityInStockUnitComputed();
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(Number(val)).is.not.equal.to(await this.quantityInStockUnitComputed());
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.referenceProperty<SalesOrderLine, 'stockUnit'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['item'],
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        lookupAccess: true,
        async defaultValue() {
            return (await (
                await this.item
            )?.stockUnit)!;
        },
        async control(cx, val) {
            await cx.error.if(await val.id).is.not.equal.to(await (await (await this.item).stockUnit).id);
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<SalesOrderLine, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['deliveryMode'] }],
        node: () => xtremMasterData.nodes.DeliveryMode,
        async defaultValue() {
            return (await this.document).deliveryMode;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    @decorators.dateProperty<SalesOrderLine, 'requestedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['requestedDeliveryDate'] }],
        async defaultValue() {
            return (await (
                await this.document
            )?.requestedDeliveryDate)!;
        },
        duplicatedValue: useDefaultValue,
    })
    readonly requestedDeliveryDate: Promise<date>;

    @decorators.integerProperty<SalesOrderLine, 'deliveryLeadTime'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['deliveryLeadTime'] }],
        async defaultValue() {
            return (await (
                await this.document
            )?.deliveryLeadTime)!;
        },
    })
    readonly deliveryLeadTime: Promise<integer>;

    @decorators.dateProperty<SalesOrderLine, 'doNotShipBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        duplicatedValue: null,
    })
    readonly doNotShipBeforeDate: Promise<date | null>;

    @decorators.dateProperty<SalesOrderLine, 'doNotShipAfterDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        duplicatedValue: null,
    })
    readonly doNotShipAfterDate: Promise<date | null>;

    @decorators.dateProperty<SalesOrderLine, 'shippingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [
            'requestedDeliveryDate',
            { document: ['date'] },
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'deliveryLeadTime',
            { document: ['workDays'] },
        ],
        async defaultValue() {
            const document = await this.document;

            return xtremDistribution.functions.subWorkDays(this.$.context, {
                requestedDeliveryDate: await this.requestedDeliveryDate,
                orderDate: await document.date,
                doNotShipBeforeDate: (await this.doNotShipBeforeDate) ?? null,
                doNotShipAfterDate: (await this.doNotShipAfterDate) ?? null,
                deliveryLeadTime: await this.deliveryLeadTime,
                workDaysMask: await document.workDays,
            });
        },
        async control(cx, val) {
            const document = await this.document;
            if ((await document.date) && val < (await document.date)) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_orderdate',
                    'The shipping date needs to be after the order date.',
                );
            }
            const doNotShipBeforeDate = await this.doNotShipBeforeDate;
            if (doNotShipBeforeDate && val < doNotShipBeforeDate) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_doNotShipBeforeDate',
                    'The shipping date needs to be after the Do not ship before date.',
                );
            }
            const doNotShipAfterDate = await this.doNotShipAfterDate;
            if (doNotShipAfterDate && val > doNotShipAfterDate) {
                cx.error.addLocalized(
                    '@sage/xtrem-sales/nodes__sales_order_line__sales_order_lines_control_shippingdate_with_doNotShipAfterDate',
                    'The shipping date needs to be before the Do not ship after date.',
                );
            }
        },
        duplicatedValue: useDefaultValue,
    })
    readonly shippingDate: Promise<date>;

    @decorators.dateProperty<SalesOrderLine, 'expectedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shippingDate', 'deliveryLeadTime', { document: ['workDays'] }],
        lookupAccess: true,
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.shippingDate,
                await this.deliveryLeadTime,
                await (
                    await this.document
                ).workDays,
            );
        },
        duplicatedValue: useDefaultValue,
    })
    readonly expectedDeliveryDate: Promise<date>;

    @decorators.referenceProperty<SalesOrderLine, 'taxZone'>({
        isStoredOutput: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremTax.nodes.TaxZone,
        dependsOn: ['shipToCustomerAddress', 'shipToAddress', { shipToAddress: ['country', 'postcode'] }],
        async defaultValue() {
            const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
            return SalesOrderLine.getTaxZone(deliveryDetail, await this.shipToAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly taxZone: Reference<xtremTax.nodes.TaxZone | null>;

    @decorators.collectionProperty<SalesOrderLine, 'discountCharges'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremSales.nodes.SalesOrderLineDiscountCharge,
    })
    readonly discountCharges: Collection<xtremSales.nodes.SalesOrderLineDiscountCharge>;

    @decorators.decimalProperty<SalesOrderLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly discount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesOrderLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                (await this.grossPrice) ?? 0,
            );
        },
    })
    readonly charge: Promise<decimal | null>;

    @decorators.decimalProperty<SalesOrderLine, 'grossPrice'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly grossPrice: Promise<decimal | null>;

    @decorators.decimalProperty<SalesOrderLine, 'discountDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['discountCharges'],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'decrease', val);
        },
    })
    readonly discountDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'chargeDeterminated'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'valueDeterminated'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValueDeterminated(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValueDeterminated(this.discountCharges, 'increase', val);
        },
    })
    readonly chargeDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'grossPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly grossPriceDeterminated: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        lookupAccess: true,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign'] },
            { document: ['currency'] },
            'currency',
            'discountCharges',
            'document',
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'taxableAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly taxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'taxAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'exemptAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly exemptAmount: Promise<decimal>;

    @decorators.dateProperty<SalesOrderLine, 'taxDate'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ document: ['date'] }],
        async defaultValue() {
            return (await this.document).date;
        },
        updatedValue: useDefaultValue,
    })
    readonly taxDate: Promise<date>;

    @decorators.decimalProperty<SalesOrderLine, 'netPriceExcludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
        dependsOn: [
            'grossPrice',
            'quantity',
            { discountCharges: ['sign'] },
            { document: ['currency'] },
            'currency',
            'discountCharges',
            'document',
        ],
        async defaultValue() {
            return (await this.currency)
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      (await this.grossPrice) ?? 0,
                      await this.quantity,
                      await (
                          await (
                              await this.document
                          ).currency
                      ).decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'netPriceIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        lookupAccess: true,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['netPriceExcludingTax', 'taxAmount', 'quantity'],
        async defaultValue() {
            return (await this.netPriceExcludingTax) + (await this.taxAmount) / (await this.quantity);
        },
        updatedValue: useDefaultValue,
    })
    readonly netPriceIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'lineAmountIncludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        lookupAccess: true,
        dependsOn: ['lineAmountExcludingTax', 'taxAmountAdjusted'],
        async defaultValue() {
            return (await this.lineAmountExcludingTax) + (await this.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    readonly lineAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'lineAmountIncludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'lineAmountIncludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.lineAmountIncludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly lineAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'taxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly taxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'lineAmountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['quantity', 'netPriceExcludingTax'],
        lookupAccess: true,
        async defaultValue() {
            return (await this.quantity) * (await this.netPriceExcludingTax);
        },
        updatedValue: useDefaultValue,
    })
    readonly lineAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'lineAmountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'quantity',
            'netPrice',
            'lineAmountExcludingTax',
            { document: ['site', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            const document = await this.document;
            // convert transaction amount into company currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.lineAmountExcludingTax,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly lineAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingAmountToShipExcludingTax'>({
        dependsOn: ['remainingQuantityToShipInSalesUnit', 'netPrice'],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (await this.remainingQuantityToShipInSalesUnit) * (await this.netPrice);
        },
    })
    readonly remainingAmountToShipExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingAmountToShipExcludingTaxInCompanyCurrency'>({
        dependsOn: ['remainingQuantityToShipInSalesUnit', 'netPrice', { document: ['companyFxRate'] }],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (
                (await this.remainingQuantityToShipInSalesUnit) *
                (await this.netPrice) *
                (await (
                    await this.document
                ).companyFxRate)
            );
        },
    })
    readonly remainingAmountToShipExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.jsonProperty<SalesOrderLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            const item = await this.item;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'salesDirect',
                companyId: (await site.legalCompany)._id,
                site,
                customer: await (await this.document).soldToCustomer,
                item,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<SalesOrderLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<SalesOrderLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        dependsOn: ['document', 'item'],
        lookupAccess: true,
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'salesDirect',
                companyId: (await site.legalCompany)._id,
                site,
                customer: await (await this.document).soldToCustomer,
                item: await this.item,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<SalesOrderLine, 'computedAttributes'>({
        isPublished: true,

        dependsOn: ['item', { document: ['site', 'stockSite', 'billToCustomer'] }],
        async computeValue() {
            return xtremSales.functions.computeAttributes(
                this.$.context,
                await this.site,
                await this.stockSite,
                await this.item,
                await (
                    await this.document
                ).billToCustomer,
            );
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.decimalProperty<SalesOrderLine, 'availableQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['itemSite'],
        lookupAccess: true,
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return (await itemSite.acceptedStockQuantity) - (await itemSite.allocatedQuantity);
            }
            return 0;
        },
    })
    readonly availableQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'stockOnHand'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['itemSite'],
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return itemSite.inStockQuantity;
            }
            return 0;
        },
    })
    readonly stockOnHand: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'availableQuantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        dependsOn: ['unit', 'availableQuantityInStockUnit', 'unitToStockUnitConversionFactor'],
        async getValue() {
            const availableQuantityInStockUnit = await this.availableQuantityInStockUnit;
            const unitToStockUnitConversionFactor = await this.unitToStockUnitConversionFactor;
            if (!availableQuantityInStockUnit && !unitToStockUnitConversionFactor) return 0;
            return availableQuantityInStockUnit / unitToStockUnitConversionFactor;
        },
    })
    readonly availableQuantityInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'stockShortageInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockUnit', 'quantityInStockUnit', 'availableQuantityInStockUnit', 'quantityAllocated'],
        async getValue() {
            const availableQuantityInStockUnit = await this.availableQuantityInStockUnit;
            const quantityInStockUnit = await this.quantityInStockUnit;
            const quantityAllocated = await this.quantityAllocated;

            if (availableQuantityInStockUnit + quantityAllocated < quantityInStockUnit) {
                return quantityInStockUnit - (availableQuantityInStockUnit + quantityAllocated);
            }
            return 0;
        },
    })
    readonly stockShortageInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'stockShortageInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        dependsOn: ['stockShortageInStockUnit', 'unitToStockUnitConversionFactor'],
        async getValue() {
            return (await this.stockShortageInStockUnit) * (await this.unitToStockUnitConversionFactor);
        },
    })
    readonly stockShortageInSalesUnit: Promise<decimal>;

    @decorators.booleanProperty<SalesOrderLine, 'stockShortageStatus'>({
        isPublished: true,
        dependsOn: ['stockShortageInStockUnit'],
        async getValue() {
            return (await this.stockShortageInStockUnit) > 0;
        },
    })
    readonly stockShortageStatus: Promise<boolean>;

    @decorators.collectionProperty<SalesOrderLine, 'salesShipmentLines'>({
        // TODO: should we publish it?
        node: () => xtremSales.nodes.SalesOrderLineToSalesShipmentLine,
        reverseReference: 'linkedDocument',
    })
    readonly salesShipmentLines: Collection<xtremSales.nodes.SalesOrderLineToSalesShipmentLine>;

    @decorators.decimalProperty<SalesOrderLine, 'quantityToShipInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.salesShipmentLines
                .where(async line => ['readyToProcess', 'readyToShip'].includes(await (await line.document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityToShipInProgressInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'shippedQuantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.salesShipmentLines
                .where(async line => ['shipped'].includes(await (await line.document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly shippedQuantityInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingQuantityToShipInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        lookupAccess: true,
        async getValue() {
            return (
                (await this.quantity) -
                (await this.shippedQuantityInSalesUnit) -
                (await this.quantityToShipInProgressInSalesUnit)
            );
        },
    })
    readonly remainingQuantityToShipInSalesUnit: Promise<decimal>;

    @decorators.collectionProperty<SalesOrderLine, 'toInvoiceLines'>({
        node: () => xtremSales.nodes.SalesOrderLineToSalesInvoiceLine,
        reverseReference: 'linkedDocument',
    })
    readonly toInvoiceLines: Collection<xtremSales.nodes.SalesOrderLineToSalesInvoiceLine>;

    @decorators.decimalProperty<SalesOrderLine, 'quantityToInvoiceInProgressInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.toInvoiceLines
                .where(async line => ['draft'].includes(await (await (await line.document).document).status))
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly quantityToInvoiceInProgressInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'invoicedQuantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.toInvoiceLines
                .where(async line =>
                    ['posted', 'inProgress', 'error'].includes(await (await (await line.document).document).status),
                )
                .sum(async line => (await line.document).quantity);
        },
    })
    readonly invoicedQuantityInSalesUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingQuantityToInvoiceInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        async getValue() {
            return (
                (await this.quantity) -
                (await this.invoicedQuantityInSalesUnit) -
                (await this.quantityToInvoiceInProgressInSalesUnit)
            );
        },
    })
    readonly remainingQuantityToInvoiceInSalesUnit: Promise<decimal>;

    @decorators.referenceProperty<SalesOrderLine, 'currency'>({
        isPublished: false,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<SalesOrderLine, 'priceOrigin'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
        lookupAccess: true,
    })
    readonly priceOrigin: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesOrderLine, 'priceReason'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
        lookupAccess: true,
    })
    readonly priceReason: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.enumProperty<SalesOrderLine, 'priceOriginDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremSales.enums.salesPriceOriginDataType,
        dependsOn: ['priceOrigin'],
        defaultValue() {
            return this.priceOrigin;
        },
    })
    readonly priceOriginDeterminated: Promise<xtremSales.enums.SalesPriceOrigin | null>;

    @decorators.referenceProperty<SalesOrderLine, 'priceReasonDeterminated'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.CustomerPriceReason,
        dependsOn: ['priceReason'],
        defaultValue() {
            return this.priceReason;
        },
    })
    readonly priceReasonDeterminated: Reference<xtremMasterData.nodes.CustomerPriceReason | null>;

    @decorators.booleanProperty<SalesOrderLine, 'isPriceDeterminated'>({
        excludedFromPayload: true,
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isPriceDeterminated: Promise<boolean>;

    @decorators.collectionProperty<SalesOrderLine, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', 'status'],
        async isFrozen() {
            return (await this.status) === 'closed';
        },
        node: () => xtremSales.nodes.SalesOrderLineTax,
        duplicatedValue: [],
    })
    readonly taxes: Collection<xtremSales.nodes.SalesOrderLineTax>;

    @decorators.jsonProperty<SalesOrderLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremSales.nodes.SalesOrderLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.enumProperty<SalesOrderLine, 'taxCalculationStatus'>({
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        dependsOn: ['taxes', { document: ['taxCalculationStatus'] }],
        async getValue() {
            const document = await this.document;
            if (
                (await document.taxCalculationStatus) === 'failed' &&
                (await this.taxes.some(async tax => (await tax.isTaxMandatory) && !(await tax.taxReference)))
            ) {
                return 'failed';
            }
            if ((await document.taxCalculationStatus) === 'notDone') {
                return 'notDone';
            }
            return 'done';
        },
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.referenceProperty<SalesOrderLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'salesOrderLine',
        duplicatedValue: null,
        node: () => xtremSales.nodes.WorkInProgressSalesOrderLine,
        // FIXME: move instance create from createEnd here once the platform fixes usage of defaultValue on vital
        // defaultValue() {
        //     // TODO: create entry according to document line criteria
        //     this.$.set({workInProgress: {}});
        // },
    })
    readonly workInProgress: Reference<xtremSales.nodes.WorkInProgressSalesOrderLine | null>;

    @decorators.decimalProperty<SalesOrderLine, 'suppliedQuantity'>({
        isPublished: true,
        dependsOn: ['assignments'],
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,

        getValue() {
            return this.assignments.sum(
                async assignment => (await (await assignment.supplyWorkInProgress)?.actualQuantity) ?? 0,
            );
        },
    })
    readonly suppliedQuantity: Promise<decimal>;

    @decorators.enumProperty<SalesOrderLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            const item = await this.item;
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                await item.isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.decimalProperty<SalesOrderLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockAllocations'],
        lookupAccess: true,

        getValue() {
            return this.stockAllocations.sum(allocation => allocation.quantityInStockUnit);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingQuantityToShipInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
        dependsOn: ['remainingQuantityToShipInSalesUnit', 'unitToStockUnitConversionFactor', 'stockUnit'],
        async getValue() {
            const unitDigits = await (await this.stockUnit).decimalDigits;
            const roundedQuantity = Decimal.roundAt(
                (await this.remainingQuantityToShipInSalesUnit) * (await this.unitToStockUnitConversionFactor),
                unitDigits,
            );
            return roundedQuantity;
        },
    })
    readonly remainingQuantityToShipInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['remainingQuantityToShipInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.remainingQuantityToShipInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.collectionProperty<SalesOrderLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.enumProperty<SalesOrderLine, 'allocationRequestStatus'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'noRequest',
        duplicatedValue: 'noRequest',
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        lookupAccess: true,
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    @decorators.collectionProperty<SalesOrderLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<SalesOrderLine, 'assignments'>({
        isPublished: true,
        reverseReference: 'demandDocumentLine',
        node: () => xtremStockData.nodes.OrderAssignment,
        duplicatedValue: [],
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
    })
    readonly assignments: Collection<xtremStockData.nodes.OrderAssignment>;

    @decorators.referenceProperty<SalesOrderLine, 'uWorkOrderLine'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dependsOn: ['assignments'],
        async getValue() {
            const demand = await this.assignments.takeOne(
                async assignment =>
                    (await assignment.demandType) === 'salesOrderLine' && (await assignment.supplyType) === 'workOrder',
            );
            return (await demand?.supplyDocumentLine) ?? null;
        },
    })
    readonly uWorkOrderLine: Reference<xtremMasterData.nodes.BaseDocumentLine> | null;

    @decorators.referenceProperty<SalesOrderLine, 'uPurchaseOrderLine'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dependsOn: ['assignments'],
        async getValue() {
            const demand = await this.assignments.takeOne(
                async assignment =>
                    (await assignment.demandType) === 'salesOrderLine' &&
                    (await assignment.supplyType) === 'purchaseOrderLine',
            );
            return (await demand?.supplyDocumentLine) ?? null;
        },
    })
    readonly uPurchaseOrderLine: Reference<xtremMasterData.nodes.BaseDocumentLine> | null;

    @decorators.decimalProperty<SalesOrderLine, 'assignedQuantity'>({
        isPublished: true,
        dependsOn: ['assignments'],
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,

        getValue() {
            return this.assignments.sum(
                async assignment => (await (await assignment.supplyWorkInProgress)?.expectedQuantity) ?? 0,
            );
        },
    })
    readonly assignedQuantity: Promise<decimal>;

    @decorators.stringProperty<SalesOrderLine, 'uAssignmentOrder'>({
        isPublished: true,
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dependsOn: ['uWorkOrderLine', 'uPurchaseOrderLine'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.uWorkOrderLine)?.documentNumber ?? (await this.uPurchaseOrderLine)?.documentNumber ?? '';
        },
    })
    readonly uAssignmentOrder: Promise<string>;

    @decorators.textStreamProperty<SalesOrderLine, 'internalNote'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremMasterData.dataTypes.note,
        defaultValue: TextStream.empty,
        duplicatedValue: useDefaultValue,
    })
    readonly internalNote: Promise<TextStream>;

    @decorators.textStreamProperty<SalesOrderLine, 'externalNote'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremMasterData.dataTypes.note,
        defaultValue: TextStream.empty,
        async control(cx) {
            await xtremMasterData.events.control.externalNoteControl.externalNoteEmpty(
                await this.isExternalNote,
                await this.externalNote,
                cx,
            );
        },
        duplicatedValue: useDefaultValue,
    })
    readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanProperty<SalesOrderLine, 'isExternalNote'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: useDefaultValue,
    })
    readonly isExternalNote: Promise<boolean>;

    @decorators.decimalProperty<SalesOrderLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', 'stockSite', 'netPrice'],
        defaultValue() {
            return this.getNewStockCostAmount();
        },
        updatedValue: useDefaultValue,
        duplicatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'grossProfitAmountInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['lineAmountExcludingTaxInCompanyCurrency', 'stockCostAmountInCompanyCurrency'],
        async getValue() {
            return (await this.lineAmountExcludingTaxInCompanyCurrency) - (await this.stockCostAmountInCompanyCurrency);
        },
    })
    readonly grossProfitAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['stockCostAmountInCompanyCurrency'],
        lookupAccess: true,
        async getValue() {
            const document = await this.document;
            // convert from company currency to transaction currency
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.stockCostAmountInCompanyCurrency,
                await document.companyFxRateDivisor,
                await document.companyFxRate,
                await (
                    await (
                        await (
                            await document.site
                        ).legalCompany
                    ).currency
                ).decimalDigits,
                await (
                    await document.currency
                ).decimalDigits,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'grossProfitAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: ['lineAmountExcludingTax', 'stockCostAmount'],
        lookupAccess: true,
        async getValue() {
            return (await this.lineAmountExcludingTax) - (await this.stockCostAmount);
        },
    })
    readonly grossProfitAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantity', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantity = await this.quantity;
            return quantity ? (await this.stockCostAmount) / quantity : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.decimalProperty<SalesOrderLine, 'grossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        dependsOn: ['netPrice', 'stockCostUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.netPrice) - (await this.stockCostUnit);
        },
    })
    readonly grossProfit: Promise<decimal>;

    async checkQuantityAlreadyShipped(): Promise<void> {
        const quantity = await this.quantity;
        if ((await this.status) === 'inProgress' && quantity !== (await (await this.$.old).quantity)) {
            const totalShipped = (
                await this.$.context
                    .queryAggregate(xtremSales.nodes.SalesOrderLineToSalesShipmentLine, {
                        filter: { linkedDocument: { _id: this._id } },
                        group: { linkedDocument: { _id: { _by: 'value' } } },
                        values: { quantity: { sum: true } },
                    })
                    .toArray()
            )[0]?.values.quantity.sum;
            if (quantity < totalShipped) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-sales/sales_order_line__quantity_bellow_already_shipped_quantity',
                        'The sales order line quantity cannot be lower than the quantity already shipped.',
                    ),
                );
            } else if (quantity === totalShipped) {
                await this.$.set({ status: 'closed' as any });
            }
        }
    }

    private async updateQuantityOnOrderAssignment(
        assignment: {
            _id: number;
        } | null,
    ): Promise<void> {
        if (assignment && assignment._id) {
            const orderAssignment = await this.$.context.read(
                xtremStockData.nodes.OrderAssignment,
                { _id: assignment._id },
                { forUpdate: true },
            );
            const quantityInStockUnit = await this.quantityInStockUnit;
            if (quantityInStockUnit < (await orderAssignment.quantityInStockUnit)) {
                await orderAssignment.$.set({ quantityInStockUnit });
            }
            if (orderAssignment.$.status === NodeStatus.modified) {
                await orderAssignment.$.save();
            }
        }
    }

    async checkAndUpdateQuantityOnAssignment(): Promise<void> {
        if (
            this.$.status === NodeStatus.modified &&
            (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption))
        ) {
            const workOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'workOrder',
            );
            await this.updateQuantityOnOrderAssignment(workOrderAssignment);
            const purchaseOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'purchaseOrderLine',
            );
            await this.updateQuantityOnOrderAssignment(purchaseOrderAssignment);
        }
    }

    async checkAndDeleteAssignment(): Promise<void> {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            const workOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'workOrder',
            );
            if (workOrderAssignment && workOrderAssignment._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: workOrderAssignment._id });
            }

            const purchaseOrderAssignment = await xtremStockData.nodes.OrderAssignment.getDemandAssignment(
                this.$.context,
                'salesOrderLine',
                this,
                'purchaseOrderLine',
            );
            if (purchaseOrderAssignment && purchaseOrderAssignment._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: purchaseOrderAssignment._id });
            }
        }
    }

    async quantityInStockUnitComputed(): Promise<decimal> {
        return +new Decimal((await this.quantity) * (await this.unitToStockUnitConversionFactor)).toDecimalPlaces(
            (await (await this.stockUnit)?.decimalDigits) || 0,
        );
    }

    /**
     * Calculates sales order line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates order
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param lineAmountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof SalesOrderLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 is done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<typeof SalesOrderLine>(
                    xtremMasterData.nodes.Customer,
                ),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof SalesOrderLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Customer>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        const salesOrderCreateData: NodeCreateData<xtremSales.nodes.SalesOrder> = {
            stockSite: data.site,
            site: data.site,
            soldToCustomer: data.businessPartner,
        };

        if (data.addresses) {
            salesOrderCreateData.shipToCustomerAddress = data.addresses.shipToCustomerAddress;
        }

        const salesOrder = await context.create(xtremSales.nodes.SalesOrder, salesOrderCreateData, {
            isOnlyForDefaultValues: true,
        });

        // get a line with default data
        await xtremTax.functions.setCalculateLineTaxesLines(salesOrder, data);
        const salesOrderLine = await salesOrder.lines.at(0);

        // if we have a consumerCountry, we assign it to the shipToAddress
        // in order to have a correct tax determination
        if (data.addresses?.consumerCountry) {
            await salesOrderLine?.$.set({
                shipToAddress: {
                    _id: -1,
                    country: data.addresses?.consumerCountry?._id,
                    postcode: data.addresses?.consumerPostcode,
                },
            });
        }

        return xtremTax.functions.calculateLineTaxes(
            salesOrder,
            await xtremSales.classes.SalesOrderTaxCalculator.create(context, data.site),
            data,
            salesOrderLine,
        );
    }

    async getNewStockCostAmount() {
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                this.$.context,
                await this.item,
                await this.stockSite,
                { quantity: await this.quantityInStockUnit, dateOfValuation: date.today(), valuationType: 'issue' },
            )
        ).amount;
    }

    @decorators.bulkMutation<typeof SalesOrderLine, 'massAutoAllocation'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    requestType: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremStockData.enums.allocationRequestTypeDataType,
                    },
                    requestDescription: 'string',
                    userEntries: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                lineId: { isMandatory: true, type: 'integer' },
                                quantityToProcess: { isMandatory: true, type: 'decimal' },
                            },
                        },
                    },
                },
            },
            // make sure that the stockAllocationParameters is matched with the output of getStockAllocationParameterGraphQLDescriptor function
            {
                name: 'stockAllocationParameters',
                type: 'object',
                properties: { cannotOverAllocate: 'boolean', shouldControlAllocationInProgress: 'boolean' },
            },
        ],
        onComplete(context: Context, salesOrderLineUpdateData) {
            return xtremStockData.functions.automaticAllocationLib.massAllocationOnCompleteHandler<xtremSales.nodes.SalesOrder>(
                context,
                {
                    documentClass: xtremSales.nodes.SalesOrder,
                    documentType: 'salesOrder',
                    allocableLinesCollectionName: 'lines',
                    documentLineUpdateData: salesOrderLineUpdateData,
                    convertFilterToMassProcessCriteriaCallBack:
                        xtremSales.functions.allocationLib.salesConvertFilterToMassProcessCriteria,
                },
            );
        },
    })
    static massAutoAllocation(
        context: Context,
        salesOrderLine: SalesOrderLine,
        data: {
            requestType: xtremStockData.enums.AllocationRequestType;
            requestDescription: string;
            userEntries?: {
                lineId: integer;
                quantityToProcess: decimal;
            }[];
        },
        stockAllocationParameters?: xtremStockData.interfaces.StockAllocationParameters,
    ) {
        return xtremStockData.functions.automaticAllocationLib.massAllocationLineHandler<SalesOrderLine>(
            context,
            {
                documentLine: salesOrderLine,
                requestType: data.requestType,
                documentType: 'salesOrder',
                requestDescription: data.requestDescription,
                userEntries: data.userEntries,
                lineFilterCallback: xtremSales.functions.isSalesOrderLineAllocable,
            },
            stockAllocationParameters,
        );
    }

    /**
     * Method that applies dimensions and attributes to the lines of a sales order line
     * @param context
     * @param SalesOrderLineLine
     * @param storedDimensions
     * @param storedAttributes
     * @returns true or throws error
     */
    @decorators.mutation<typeof SalesOrderLine, 'setDimension'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesOrderLine',
                type: 'reference',
                node: () => xtremSales.nodes.SalesOrderLine,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: 'boolean',
    })
    static async setDimension(
        _context: Context,
        salesOrderLine: xtremSales.nodes.SalesOrderLine,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        await salesOrderLine.$.set({
            storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
            storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
        });
        await salesOrderLine.$.save();
        return true;
    }

    private static async getTaxZone(
        customerDeliveryAddress: xtremMasterData.nodes.DeliveryDetail | null,
        address: xtremMasterData.nodes.Address | null,
    ): Promise<xtremTax.nodes.TaxZone | null> {
        if (!((await (await customerDeliveryAddress?.address)?.country) && (await customerDeliveryAddress?.taxZone))) {
            return null;
        }
        if (!customerDeliveryAddress) {
            return null;
        }

        return SalesOrderLine.getTaxZoneByAddressesData({
            customerDeliveryCountry: await (await customerDeliveryAddress.address)?.country,
            customerDeliveryPostcode: await (await customerDeliveryAddress.address)?.postcode,
            shiptToCountry: (await address?.country) || null,
            shiptToPostcode: (await address?.postcode) || '',
            taxZone: await customerDeliveryAddress.taxZone,
        });
    }

    private static getTaxZoneByAddressesData(params: {
        customerDeliveryCountry: xtremStructure.nodes.Country | null;
        customerDeliveryPostcode: string;
        shiptToCountry: xtremStructure.nodes.Country | null;
        shiptToPostcode: string;
        taxZone: xtremTax.nodes.TaxZone | null;
    }): xtremTax.nodes.TaxZone | null {
        return params.customerDeliveryCountry?._id === params.shiptToCountry?._id &&
            params.customerDeliveryPostcode === params.shiptToPostcode
            ? params.taxZone
            : null;
    }
}
