{
    // current behavior generates useless changes
    // and it cycles with the same updates over and over
    enabled: true,
    baseBranches: ['master'],
    // baseBranches: ['master', '/^release\/(39|38)\\.0/'],
    labels: ['dependencies', 'renovate'],
    // UTC based time
    schedule: ['after 6pm every weekday', 'before 4am every weekday', 'every weekend'],
    prConcurrentLimit: 20,
    dependencyDashboard: true,
    ignorePaths: ['**/docker/**', '**/fixtures/**', '**/examples/**'],
    constraints: {
        pnpm: '10.x',
    },
    packageRules: [
        {
            description: 'lockFileMaintenance',
            matchUpdateTypes: ['pin', 'digest', 'patch', 'minor', 'major', 'lockFileMaintenance'],
            dependencyDashboardApproval: false,
            minimumReleaseAge: null,
        },
        {
            description: 'ignore sage api packages',
            enabled: false,
            matchPackageNames: ['/^@sage/.*-api$/'],
        },
        {
            description: 'ignore lerna major updates',
            matchUpdateTypes: ['major'],
            enabled: false,
            matchPackageNames: ['/^lerna$/', '/^@lerna/.*$/'],
        },
        {
            description: 'sage cirrus packages',
            matchPackageNames: [
                '@sage/async-context-provider',
                '@sage/xtrem-access-token',
                '@sage/xtrem-file-storage',
                '@sage/xtrem-messaging-wrapper',
                '@sage/xtrem-secret',
                '@sage/xtrem-sumologic-helper',
                '@sage/xtrem-user-event',
                '@sage/xtrem-websocket-emiter',
                '@sage/api-tools',
                '@sage/xtrem-deployment',
            ],
            groupName: 'sage cirrus',
        },
        {
            matchUpdateTypes: ['minor', 'patch'],
            matchCurrentVersion: '!/^0/',
            groupName: 'non-major types dependencies',
            matchPackageNames: ['@types/{/,}**'],
        },
        // Packages that require manual review should have their group name with dependencyDashboardApproval to true
        {
            description: 'ignore fullcalendar packages',
            matchPackageNames: [
                '@fullcalendar/core',
                '@fullcalendar/daygrid',
                '@fullcalendar/interaction',
                '@fullcalendar/react',
                '@fullcalendar/timegrid',
            ],
            dependencyDashboardApproval: true,
        },
        {
            description: 'ignore turbo packages',
            matchPackageNames: ['@turbo/codemod', 'turbo'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'ignore ui managed packages',
            matchPackageNames: ['downshift', '@swc/core', 'carbon-react', 'css-loader'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'ignore ag-grid ui managed packages',
            dependencyDashboardApproval: true,
            matchPackageNames: ['@ag-grid-{/,}**', 'ag-charts-{/,}**'],
        },
        {
            description: 'cucumber html reporter requires ATP review',
            matchPackageNames: ['multiple-cucumber-html-reporter'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'wdio json reporter requires ATP review',
            matchPackageNames: ['wdio-cucumberjs-json-reporter'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'wdio report portal requires ATP review',
            matchPackageNames: ['wdio-reportportal-reporter'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'cucumber requires ATP review',
            matchPackageNames: ['@cucumber/cucumber'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'obsolete dev tools to review',
            dependencyDashboardApproval: true,
            matchPackageNames: ['typedoc{/,}**'],
        },
        {
            description: 'esbuild-loader package',
            matchPackageNames: ['esbuild-loader'],
            groupName: 'esbuild-loader',
            dependencyDashboardApproval: true,
        },
        {
            description: '@sage/gms-chat-ui package',
            matchPackageNames: ['@sage/gms-chat-ui'],
            groupName: 'gms-chat-ui',
            dependencyDashboardApproval: true,
        },
        {
            extends: ['monorepo:webdriverio'],
            dependencyDashboardApproval: true,
        },
        // Rule to avoid updating packages version that are ESM - to review once we have migrated to ESM
        {
            description: 'avoid updating chalk to ESM version',
            matchPackageNames: ['chalk'],
            allowedVersions: '< 5.0.0',
            groupName: 'chalk package',
        },
        {
            description: 'avoid updating chai to ESM version',
            matchPackageNames: ['chai', '@types/chai'],
            allowedVersions: '< 5.0.0',
            groupName: 'chai package',
        },
        {
            description: 'avoid updating chai-as-promised to ESM version',
            matchPackageNames: ['chai-as-promised', '@types/chai-as-promised'],
            allowedVersions: '< 8.0.0',
            groupName: 'chai-as-promised package',
        },
        {
            description: 'avoid updating matcher to ESM version',
            matchPackageNames: ['matcher'],
            allowedVersions: '< 5.0.0',
            groupName: 'matcher package',
        },
        {
            description: 'avoid updating nanoid to ESM version and stick to 3.x.x',
            matchPackageNames: ['nanoid'],
            allowedVersions: '< 4.0.0',
            groupName: 'nanoid package',
        },
        {
            description: 'avoid updating usehooks-ts to ESM version',
            matchPackageNames: ['usehooks-ts'],
            allowedVersions: '< 3.0.0',
            groupName: 'usehooks-ts package',
        },
        // Other rules with grouping and eventually sticking the version
        {
            description: 'pin graphql package version because of stream and defer',
            matchPackageNames: ['graphql'],
            allowedVersions: '16.1.0-experimental-stream-defer.6',
        },
        {
            description: 'pin express-graphql package version because of stream and defer',
            matchPackageNames: ['express-graphql'],
            allowedVersions: '0.12.0-experimental-stream-defer.1',
        },
        {
            description: 'pin pdfjs-dist package',
            matchPackageNames: ['pdfjs-dist'],
            // must be in line with react-pdf
            allowedVersions: '/^4\\.4\\.[0-9]+$/',
        },
        {
            description: 'group postgreSQL packages',
            groupName: 'postgresql',
            matchPackageNames: ['/^pg$/', '/^pg-.*$/'],
        },
        {
            description: 'group ckeditor packages',
            groupName: 'ckeditor',
            matchPackageNames: ['@ckeditor/{/,}**', '@types/ckeditor__{/,}**'],
        },
        {
            description: 'group cucumber packages',
            groupName: 'cucumber',
            matchPackageNames: ['@cucumber/{/,}**'],
        },
        {
            description: 'group newrelic packages',
            groupName: 'newrelic',
            matchPackageNames: [
                'newrelic{/,}**',
                '@newrelic/{/,}**',
                '@types/newrelic{/,}**',
                '@types/new-relic-{/,}**',
            ],
        },
        {
            description: 'group testing-library packages',
            groupName: 'testing-library',
            matchPackageNames: ['testing-library-{/,}**', '@testing-library/{/,}**', '@types/testing-library__{/,}**'],
        },
        {
            description: 'group inquirer packages',
            groupName: 'inquirer',
            matchPackageNames: [
                'inquirer',
                'inquirer-autocomplete-prompt',
                'inquirer-checkbox-autocomplete-prompt',
                '@types/inquirer',
            ],
        },
        {
            description: 'ignore newrelic package',
            matchPackageNames: ['newrelic'],
            dependencyDashboardApproval: true,
        },
        {
            description: 'ignore broken mocha package',
            matchPackageNames: ['mocha'],
            dependencyDashboardApproval: true,
        },
    ],
}
