**/.nyc_output
**/crypt.node
**/junit-report*.xml
**/junit-report-*.xml
**/x3-builder-agent
# Cucumber script run temp files like "services/shared/xtrem-master-data/C:\\Users\\<USER>\\AppData\\Local\\lighthouse.41556302/"
**\lighthouse*/
*.0x/
*.log
*.new
*.old
*.tsbuildinfo
*.vsix
.DS_Store
.bash_aliases
.cache/
.env
.idea
.nyc_output/
.xdev/
apps.yml
base.*.json
build
cobertura-coverage.xml
code-coverage
coverage
csvs
debug/
etna-config.yml
etna-security.yml
lcov-report/
lcov.info
logs/
modified.txt
node_modules/
out/
pnpm-lock.tmp
packages.txt
prometheus.yml
reports/
# ignore export data
**/data/exports/
tenant-data/
test-report/
test-results.xml
test-results/
tmp/
todo.txt
translations
upgrade-metrics-*
wdio-parameters
parameters-atp
xtrem-config-debug.yml
xtrem-config.yml
xtrem-security.yml
xtrem-ui.yml
.config
.turbo
*/*/*/test/cucumber/screenshots/
777777777777777777777/
# temp file used during the 'build' process
allPackages.json
