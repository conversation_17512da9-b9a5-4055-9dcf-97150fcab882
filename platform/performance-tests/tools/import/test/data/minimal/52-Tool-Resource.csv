*id;*name;description;isActive;activeRange;*site;resourceImage;*weeklyShift;item;quantity;consumptionMode;hoursTracked;unitProduced;*efficiency;postingClass;#resourceCostCategories;*costCategory;setupCost;runCost;*costUnit;
string;localized text;localized text;boolean;[date,date);reference;binaryStream;reference;reference;decimal;enum(none,time,quantity);decimal;decimal;decimal;reference;collection;reference;decimal;decimal;reference;IGNORE
id;"default locale: en-US, other locales can be specified with (locale-name), for example ""description (de-DE)"". Available locales are (en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations";"default locale: en-US, other locales can be specified with (locale-name), for example ""description (de-DE)"". Available locales are (en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations";is active(false/true);[YYYY-MM-DD,YYYY-MM-DD);site;resource image;weekly shift;item;quantity;consumption mode;hours tracked;unit produced;efficiency;posting class;resource cost categories;cost category;setup cost;run cost;cost unit;IGNORE
SAW_SHARPENER;Saw sharpener;Automatic saw sharpener;TRUE;"[2023-01-01,2024-01-01)";S01-FR;;SEVEN_DAYS_EIGHT_HOURS;;;;;10;0.98;;1;Standard;12;23;HOUR;
