{"name": "@sage/xtrem-cli", "version": "54.0.58", "description": "", "main": "build/index.js", "types": "build/index.d.ts", "typings": "build/package-definition.d.ts", "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:watch": "tsc -b -v --watch .", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "start": "pnpm build && node .", "xtrem-bytenode": "xtrem-bytenode"}, "files": ["bin", "build/*.*", "build/lib"], "author": "Sage", "bin": {"xtrem": "./bin/xtrem"}, "license": "UNLICENSED", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "chalk": "^4.0.0", "find-up": "^7.0.0", "glob": "^11.0.0", "lodash": "^4.17.21", "newrelic": "^12.0.0", "yargs": "^17.7.2"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/yargs": "^17.0.24", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^8.49.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}