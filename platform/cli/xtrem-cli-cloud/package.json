{"name": "@sage/xtrem-cli-cloud", "description": "Xtrem CLI cloud", "version": "54.0.58", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build"], "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-data-management": "workspace:*", "lodash": "^4.17.21", "yargs": "^17.7.2"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/yargs": "^17.0.24", "eslint": "^8.49.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "xtrem-bytenode": "xtrem-bytenode"}}