{"name": "@sage/xtrem-cli-compile", "description": "Xtrem cli compile", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build/package-definition.d.ts", "build/index.*", "build/lib/**/*", "package.json", "CHANGELOG.md"], "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-cli-transformers": "workspace:*", "@sage/xtrem-client-gen": "workspace:*", "@sage/xtrem-cop": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "chalk": "^4.0.0", "css-loader": "^6.8.1", "espree": "^10.1.0", "esquery": "^1.4.0", "glob": "^11.0.0", "glob-parent": "^6.0.2", "istanbul-lib-instrument": "^6.0.0", "lodash": "^4.17.21", "rxjs": "^7.0.0", "sass-loader": "^16.0.0", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.1.1", "thread-loader": "^4.0.2", "ts-loader": "^9.4.2", "typescript": "~5.8.0", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpack-merge": "^6.0.0"}, "devDependencies": {"@types/esquery": "^1.0.1", "@types/estree": "1.0.6", "@types/istanbul-lib-instrument": "^1.7.4", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/webpack": "5.28.5", "eslint": "^8.49.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "rm -rf ./xtrem-i18n && eslint --no-eslintrc -c .eslintrc-filename.js \"**\""}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}