{"name": "@sage/xtrem-show-case", "description": "Xtrem Show Case", "version": "54.0.58", "xtrem": {"hasListeners": true, "isMain": true, "queue": "showcase", "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "sql", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-cloud": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-customization": "workspace:*", "@sage/xtrem-dashboard": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-import-export": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-mailer": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-reporting": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-service": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-plugin-graphiql": "workspace:*", "@sage/xtrem-ui-plugin-monaco": "workspace:*", "@sage/xtrem-ui-plugin-pdf": "workspace:*", "@sage/xtrem-upload": "workspace:*", "@sage/xtrem-workflow": "workspace:*", "eslint": "^8.49.0", "lodash": "^4.17.21"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-authorization-api": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-import-export-api": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-show-case-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-upload-api": "workspace:*", "@sage/xtrem-workflow-api": "workspace:*", "@types/chai": "^4.3.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "chai": "^4.3.10", "eslint": "^8.49.0", "mocha": "^10.8.2", "typescript": "~5.8.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:references": "tsc -b -v .", "clean": "rm -rf build", "db:delete:seed1": "xtrem tenant --delete '000000000000000000001'", "db:export:seed1": "xtrem tenant --export '000000000000000000001' --export-id 'exp-1'", "db:init:seed": "xtrem tenant --init '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'", "db:init:seed1": "xtrem tenant --init '************************************************************************************************************************************************************************************************************************************************************************************'", "db:reset": "xtrem schema --reset --layers setup", "db:reset:seed": "xtrem schema --create --reset-database", "db:seed": "xtrem layers --load setup,test", "db:update:seed": "xtrem tenant --update 'ewoJInRlbmFudCI6IHsKCQkiaWQiOiAiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIgoJfSwKCSJwYWNrYWdlcyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlIjogdHJ1ZQoJfSwKCSJzZXJ2aWNlT3B0aW9ucyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlL3Nob3dDYXNlRGlzY291bnRPcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VFeHBlcmltZW50YWxPcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VPcHRpb25IaWdoTGV2ZWwiOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VXb3JrSW5Qcm9ncmVzc09wdGlvbiI6IGZhbHNlCgoJfSwKCSJwYWNrcyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlIjogdHJ1ZSwKCQkiQHNhZ2UveHRyZW0tc3lzdGVtIjogdHJ1ZQoJfQp9'", "dev": "pnpm build:references && pnpm xtrem start --watch-all", "lint": "xtrem lint", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "extract:setup:data": "xtrem layers --extract setup", "extract:test:data": "xtrem layers --extract test", "postgres:clean": "docker rm xtrem_postgres || exit 0", "postgres:reset": "pnpm postgres:stop && pnpm postgres:clean && pnpm postgres:setup", "postgres:setup": "docker run --shm-size=1g -p 5432:5432 -e POSTGRES_PASSWORD=secret -d --restart unless-stopped --name xtrem_postgres postgres:$(cat ../../../.pgdbrc)-alpine -c max_locks_per_transaction=256", "postgres:stop": "docker stop xtrem_postgres || exit 0", "schema:create": "xtrem schema --create", "schema:reset": "xtrem schema --reset --layers setup,test", "schema:upgrade": "xtrem upgrade --run --execute", "sqs:clean": "cd ../../.. && pnpm sqs:clean", "sqs:reset": "cd ../../.. && pnpm sqs:reset", "sqs:setup": "cd ../../.. && pnpm sqs:setup", "sqs:stop": "cd ../../.. && pnpm sqs:stop", "start": "UNSECURE_DEV_LOGIN=1 xtrem start", "test": "xtrem test --unit --graphql --service-options=showCaseWorkInProgressOption", "test:ci": "xtrem test --unit --ci --service-options=showCaseWorkInProgressOption", "test:ci:integration": "xtrem test --integration --ci --service-options=showCaseWorkInProgressOption,showCaseDiscountOption", "test:integration": "xtrem test --integration", "xtrem": "xtrem", "xtrem:debug": "node --inspect-brk ../../cli/xtrem-cli/build/lib/cli.js", "view-report": "node ../../../scripts/allure/view-report.js"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}