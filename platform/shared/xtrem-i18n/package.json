{"name": "@sage/xtrem-i18n", "description": "Xtrem internationalization service", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "package.json", "CHANGELOG.md"], "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "accept-language-parser": "^1.5.0", "glob": "^11.0.0", "lodash": "^4.17.21", "rimraf": "^6.0.0"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/accept-language-parser": "^1.5.1", "@types/chai": "^4.3.6", "@types/express": "^5.0.0", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/sinon": "^17.0.0", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "sinon": "^19.0.0", "ts-node": "^10.9.1", "ts-patch": "^3.0.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "rm -rf ./xtrem-i18n && eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-i18n.xml JUNIT_REPORT_NAME='xtrem-i18n' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}