{"name": "@sage/xtrem-shared", "description": "Xtrem shared definitions", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build/index.*", "build/lib", "build/package-definition.d.ts"], "dependencies": {"@sage/xtrem-decimal": "workspace:*", "handlebars": "^4.7.8", "handlebars-intl": "^1.1.2", "lodash": "^4.17.21", "prettier": "^3.3.3"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "carbon-react": "146.2.1", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.3", "draft-js": "^0.11.7", "eslint": "^8.49.0", "mime-db": "^1.52.0", "mocha": "^10.8.2", "ts-essentials": "^10.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsd": "^0.31.0", "typescript": "~5.8.0"}, "scripts": {"build": "pnpm gen-mime-types && tsc -b -v . && pnpm dts-bundle && cd ../../.. && pnpm generate:config:json:schema", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "gen-mime-types": "node ./scripts/gen-mime-types.mjs", "lint": "eslint -c .eslintrc.js --ext .ts lib", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-shared.xml JUNIT_REPORT_NAME='xtrem-shared' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}