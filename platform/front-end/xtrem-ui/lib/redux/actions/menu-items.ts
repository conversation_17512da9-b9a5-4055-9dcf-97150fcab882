import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { AppThunkDispatch, Menu } from '..';
import { actions, getStore } from '..';
import type { SectionControlObject } from '../../component/control-objects';
import { getFieldTitle } from '../../component/field/carbon-helpers';
import { createStickerDialog } from '../../service/dialog-service';
import type { ScreenBase } from '../../service/screen-base';
import type { DialogType } from '../../types/dialogs';
import { triggerScreenEvent } from '../../utils/events';

export const navigationPanelMenuItemId = 'NavigationPanelMenuItem';
export const closeDialogMenuItemId = 'CloseDialogMenuItem';

export const getCloseDialogMenuItem = (
    dialogId: number,
    isDialogSticker: boolean,
    title = 'Close dialog',
    dialogType: DialogType = 'unknown',
): Menu => ({
    id: closeDialogMenuItemId,
    onClick: (): void => {
        const dispatch: AppThunkDispatch = getStore().dispatch;
        if (dialogType === 'table-sidebar') {
            dispatch(actions.closeTableSidebar(dialogId));
        } else {
            dispatch(actions.closeDialog(dialogId));
        }
    },
    title,
    alignedLeft: true,
    icon: 'cross',
    category: 'dialog',
    isDialogSticker,
    dialogId,
    dialogType,
});

export const getStickerMenuItem = (
    stickerId: string,
    icon: IconType,
    sections: SectionControlObject<ScreenBase>[],
    badgeContent?: string,
    title?: string,
    category?: string,
): Menu => ({
    icon,
    id: stickerId,
    onClick: (): void => {
        const section = sections[0];
        const sectionTitle = (section && getFieldTitle(stickerId, section, null)) || '';
        const store = getStore();
        const state = store.getState();
        const existingDialog = Object.values(state.activeDialogs).find(
            activeDialog => activeDialog.screenId === stickerId,
        );

        if (!existingDialog) {
            triggerScreenEvent(stickerId, 'onOpen');
            createStickerDialog(store, stickerId, 'info', sectionTitle, section, {});
        }
    },
    title: title || stickerId,
    category: category ? `sticker/${category}` : 'sticker',
    badgeContent,
});
