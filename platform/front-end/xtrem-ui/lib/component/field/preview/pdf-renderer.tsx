import type { PDFDocumentProxy } from 'pdfjs-dist';
import * as React from 'react';
import { Document, Page, pdfjs, Thumbnail } from 'react-pdf';
import type { OnDocumentLoadSuccess, OnItemClickArgs } from 'react-pdf/dist/cjs/shared/types';
import type { PreviewRenderProps } from './preview-types';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { calculatePageScrollTop, usePageIndex } from './preview-utils';
import { xtremConsole } from '../../../utils/console';
import { MD5 } from 'crypto-js';

pdfjs.GlobalWorkerOptions.workerSrc = new URL('pdf.worker.min.mjs', window.location.origin).toString();

const PAGES_SELECTOR = '.react-pdf__Page';

export function PdfRenderer({
    height,
    isThumbnailBarVisible,
    scale,
    scrollPageIndex,
    setCurrentPage,
    setMetadataContent,
    value,
}: PreviewRenderProps): React.ReactElement {
    const [document, setDocument] = React.useState<PDFDocumentProxy | null>(null);
    const bodyRef = React.useRef<HTMLDivElement>(null);

    usePageIndex(bodyRef, PAGES_SELECTOR, scrollPageIndex, setCurrentPage);

    const onThumbnailClick = React.useCallback(({ pageIndex }: OnItemClickArgs): void => {
        if (!bodyRef.current) {
            return;
        }

        const top = calculatePageScrollTop(bodyRef, PAGES_SELECTOR, pageIndex);
        bodyRef.current.scrollTo({ top, behavior: 'smooth' });
    }, []);

    const onFileLoaded: OnDocumentLoadSuccess = React.useCallback(
        async loadedDocument => {
            const metadata = await loadedDocument.getMetadata();
            const info: any = metadata?.info;

            setDocument(loadedDocument);
            setMetadataContent({
                pdfVersion: info?.PDFVersion,
                producingSoftware: info?.Producer,
                numberOfPages: loadedDocument.numPages,
            });
        },
        [setMetadataContent],
    );

    const onLoadError = React.useCallback((error: Error) => {
        xtremConsole.error('Error loading PDF file', error);
    }, []);

    const pages = React.useMemo(() => {
        const collector: React.JSX.Element[] = [];
        if (document) {
            for (let i = 0; i < document?.numPages; i += 1) {
                collector.push(<Page key={i} pageIndex={i} scale={scale} />);
            }
        }

        return collector;
    }, [document, scale]);

    const thumbnails = React.useMemo(() => {
        const collector: React.JSX.Element[] = [];
        if (document) {
            for (let i = 0; i < document?.numPages; i += 1) {
                collector.push(<Thumbnail key={i} pageIndex={i} width={150} onItemClick={onThumbnailClick} />);
            }
        }

        return collector;
    }, [document, onThumbnailClick]);

    const key = React.useMemo(() => MD5(value.url).toString(), [value.url]);

    return (
        <Document key={key} file={value.url} onLoadSuccess={onFileLoaded} onLoadError={onLoadError}>
            {!document && <LoaderBar m="32px" data-testid="e-preview-field-loader" />}
            {document && (
                <div className="e-preview-field-body" style={{ height: `${height}px` }}>
                    {isThumbnailBarVisible && <div className="e-preview-field-thumbnails">{thumbnails}</div>}
                    <div
                        data-testid="e-preview-field-document-body"
                        className="e-preview-field-document-body"
                        ref={bodyRef}
                    >
                        {pages}
                    </div>
                </div>
            )}
        </Document>
    );
}
