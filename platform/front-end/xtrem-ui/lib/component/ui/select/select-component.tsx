import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type {
    UseComboboxGetInputPropsOptions,
    UseComboboxProps,
    UseComboboxState,
    UseComboboxStateChange,
    UseComboboxStateChangeOptions,
    UseComboboxStateChangeTypes,
    UseMultipleSelectionStateChange,
} from 'downshift';
import { useCombobox, useMultipleSelection } from 'downshift';
import { difference, isEqual, isObject, noop } from 'lodash';
import React, { useEffect, useLayoutEffect, useMemo } from 'react';
import { localize } from '../../../service/i18n-service';
import { SoundApi } from '../../../service/sound-api';
import * as wrapperService from '../../../service/wrapper-service';
import { xtremConsole } from '../../../utils/console';
import { useTimer } from '../../../utils/hooks/effects/use-timer';
import type { ImageValue } from '../../field/image/image-types';
import { isBackspace, isEnter, isSpaceBar } from '../../../utils/keyboard-event-utils';
import type { CollectionItem } from '../../types';
import { SelectDropDown } from './select-dropdown';
import { SelectInput } from './select-input';
import { SELECT_EMPTY_VALUE } from '../../field/select/select-utils';
import type { SizeOptions } from 'carbon-react/esm/components/button/button.component';
import { useDeepCompareEffect, useDeepCompareMemo } from '@sage/xtrem-ui-components';

export interface SelectItem {
    _id?: string;
    id: string;
    value: string;
    image?: ImageValue;
    helperText?: string;
    displayedAs?: string;
    __collectionItem?: CollectionItem;
    pendoId?: string;
}

export type InputProps = React.ComponentProps<'input'>;

export interface SelectProps {
    allowClearOnTab?: boolean;
    allowSelectOnTab?: boolean;
    autoSelect?: boolean;
    borderColor?: string;
    node?: any;
    defaultIsOpen?: boolean;
    disabled?: boolean;
    disablePills?: boolean;
    disableOpenOnFocus?: boolean;
    elementId?: string;
    error?: string;
    info?: string;
    shouldRenderOptionsAbove?: boolean;
    warning?: string;
    escapeBehavior?: 'native' | 'revert';
    hasInputSearch?: boolean;
    hasHighlightMatchText?: boolean;
    fullWidth?: boolean;
    getItems: (searchText: string) => Promise<SelectItem[]>;
    getNewItem?: (searchText: string) => SelectItem | undefined;
    hasLookupIcon?: boolean;
    hasClearFieldButton?: boolean;
    helperText?: string;
    helperTextLink?: string;
    icon?: IconType;
    initialInputValue?: string;
    initialSelectedItems?: SelectItem[];
    inputId?: string;
    inputWidth?: string;
    isSortedAlphabetically?: boolean;
    isDropdownDisabled?: boolean;
    isLinkCreateNewText?: boolean;
    isLinkHelperText?: boolean;
    isMultiSelect?: boolean;
    isSoundDisabled?: boolean;
    isHelperTextHidden?: boolean;
    label?: string;
    lookupButtonRef?: React.RefObject<HTMLButtonElement>;
    lookupIconId?: string;
    closeIconClassName?: string;
    minLookupCharacters?: number;
    /** This property indicates that the select ui component is used that does not support helper text in the items array */
    noHelperTextInItem?: boolean;
    autoSelectPolicy?: 'always' | 'onBlur';
    onAutoSelect?: (value: CollectionItem | undefined) => void;
    onBlur?: () => void;
    // Fired whenever the selected item changes
    onChange?: (value: CollectionItem | undefined, isOrganicChange: boolean) => void;
    // Fired whenever an item is selected, even if it didn't change
    onSelected?: (value: SelectItem | null | undefined, selectionType: UseComboboxStateChangeTypes) => void;
    onHelperTextLinkClick?: () => void;
    onHighlightedChange?: (value?: SelectItem | null) => void;
    onInputChange?: (value: string, type?: UseComboboxStateChangeTypes) => void;
    onInputFocus?: () => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onLookupIconClick?: (event: React.MouseEvent<any>) => void;
    onSelectedItemsChange?: (selectedRecords: CollectionItem[]) => void;
    placeholder?: string;
    preventSelectionOnBlur?: boolean;
    readOnly?: boolean;
    // Items are refetched in case this prop changes
    refetchIfChanged?: any;
    screenId?: string;
    selectedItem?: SelectItem;
    selectedItems?: SelectItem[];
    shouldFilterItems?: boolean;
    size?: SizeOptions;
    testId?: string;
    variant?: 'plain' | 'carbon';
    isInTable?: boolean;
    createTunnelLinkText?: string;
    onCreateNewItemLinkClick?: () => void;
    onItemsFetched?: (
        args:
            | { state: 'success'; items: CollectionItem[] }
            | { state: 'error'; error: string }
            | { state: 'autoselect'; item: CollectionItem | null },
    ) => void;
}

const itemToString = (item: SelectItem | null): string => (item && item.value ? String(item.value) : '');

const isEqualSelectedItem = (first: SelectItem | null | undefined, second: SelectItem | null | undefined): boolean => {
    if (!isObject(first) || !isObject(second)) {
        return isEqual(first, second);
    }
    return Object.prototype.hasOwnProperty.call(first, '__collectionItem') &&
        Object.prototype.hasOwnProperty.call(second, '__collectionItem')
        ? first.__collectionItem?._id === second.__collectionItem?._id &&
              (first.value || null) === (second.value || null) &&
              (first.helperText || null) === (second.helperText || null)
        : first.id === second.id && first.value === second.value;
};

const FOCUS_TIMEOUT = 100;
const FETCH_TIMEOUT = 300;

export const Select = React.forwardRef<HTMLInputElement, SelectProps>(
    (
        {
            allowClearOnTab = false,
            allowSelectOnTab = false,
            autoSelect = false,
            autoSelectPolicy = 'onBlur',
            borderColor,
            closeIconClassName,
            createTunnelLinkText,
            defaultIsOpen = false,
            disabled = false,
            disableOpenOnFocus = false,
            disablePills = false,
            elementId,
            error: controlledError,
            escapeBehavior = 'native',
            fullWidth = true,
            getItems,
            getNewItem,
            hasClearFieldButton = false,
            hasHighlightMatchText = false,
            hasInputSearch = true,
            hasLookupIcon = false,
            helperText: controlledHelperText,
            helperTextLink,
            icon,
            info,
            initialInputValue: controlledInitialInputValue,
            initialSelectedItems = [],
            inputId,
            inputWidth,
            isDropdownDisabled = false,
            isHelperTextHidden = false,
            isInTable = false,
            isLinkCreateNewText,
            isLinkHelperText,
            isMultiSelect = false,
            isSortedAlphabetically = false,
            isSoundDisabled = false,
            label,
            lookupIconId,
            minLookupCharacters = 3,
            node,
            noHelperTextInItem = false,
            onAutoSelect,
            onBlur,
            onChange = noop,
            onCreateNewItemLinkClick,
            onHelperTextLinkClick,
            onHighlightedChange,
            onInputChange = noop,
            onInputFocus = noop,
            onItemsFetched,
            onKeyDown = noop,
            onLookupIconClick,
            onSelected = noop,
            onSelectedItemsChange = noop,
            placeholder = localize('@sage/xtrem-ui/please-select-placeholder', 'Please Select...'),
            preventSelectionOnBlur = false,
            readOnly = false,
            refetchIfChanged,
            screenId,
            selectedItem: controlledSelectedItem,
            selectedItems: controlledSelectedItems,
            shouldFilterItems = true,
            shouldRenderOptionsAbove = false,
            size,
            testId,
            variant = 'carbon',
            warning,
        },
        ref,
    ) => {
        const [items, setItems] = React.useState<SelectItem[]>([]);
        const [dropdownMaxHeight, setDropdownMaxHeight] = React.useState<number>(18);
        const [forceRenderOptionsAbove, setForceRenderOptionsAbove] = React.useState<boolean>(false);
        const [isLoading, setIsLoading] = React.useState<boolean>(false);
        const [isFetching, setIsFetching] = React.useState<boolean>(false);
        const [error, setError] = React.useState<string>('');
        const [helperText, setHelperText] = React.useState<string | undefined>(controlledHelperText);
        const [shouldAutoSelect, setShouldAutoSelect] = React.useState<boolean>(false);
        const [autoSelected, setAutoSelected] = React.useState<SelectItem | undefined | null>(undefined);
        const [highlighted, setHighlighted] = React.useState<SelectItem | undefined>(undefined);
        const [isInputFocused, setIsInputFocused] = React.useState<boolean>(false);
        const [isDirty, setIsDirty] = React.useState<boolean>(false);
        const fetchKey = React.useRef<string | null>(null);
        const [enter, setEnter] = React.useState<boolean>(false);
        const [initialSelected, setInitialSelected] = React.useState<SelectItem | undefined>(undefined);
        const [initialInputValue] = React.useState<string | undefined>(
            controlledInitialInputValue ?? (controlledSelectedItem && String(controlledSelectedItem.value)) ?? '',
        );
        const lookupButtonRef = React.useRef<HTMLButtonElement>(null);
        const lookupLinkRef = React.createRef<HTMLLinkElement>();
        const createTunnelLinkRef = React.createRef<HTMLLinkElement>();
        const inputRef = React.useRef<HTMLInputElement | null>(null);
        const [highlightText, setHighlightText] = React.useState<string>('');
        const highlightTimer = useTimer();
        const ulRef = React.useRef<HTMLUListElement | null>(null);
        const previousRefetchIfChanged = React.useRef(refetchIfChanged);

        const onSelectedItemChange = ({
            selectedItem: item,
            type,
        }: Partial<UseComboboxStateChange<SelectItem>>): void => {
            /**
             * 'onChange' of the parent will be called with '__collectionItem' in case the selected item's value
             * (i.e. what is being shown to the user) differs from what is being saved in the state (e.g. reference field)
             */
            const newValue =
                item && Object.prototype.hasOwnProperty.call(item, '__collectionItem')
                    ? item.__collectionItem
                    : (item ?? undefined);
            setInputValue(item?.value ? String(item.value) : '');
            setHelperText(item?.helperText);
            const isOrganicChange =
                enter ||
                type !== useCombobox.stateChangeTypes.FunctionSelectItem ||
                (autoSelect && autoSelected !== undefined);
            const isEqualToControlledSelected = isEqualSelectedItem(controlledSelectedItem ?? null, item ?? null);
            if (!isEqualToControlledSelected) {
                onChange(newValue, isOrganicChange);
            }
        };

        // Override Downshift's state reducer. Do not perform side-effects in this function.
        const stateReducer: UseComboboxProps<SelectItem>['stateReducer'] = (
            state: UseComboboxState<SelectItem>,
            actionAndChanges: UseComboboxStateChangeOptions<SelectItem>,
        ): Partial<UseComboboxState<SelectItem>> => {
            if (actionAndChanges.type === useCombobox.stateChangeTypes.InputBlur) {
                if (state.highlightedIndex === -1 && autoSelect) {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    const { selectedItem: _, ...changes } = actionAndChanges.changes;
                    // cancel any automatic selection, let auto-select handle it
                    setShouldAutoSelect(true);
                    const autoSelectedItem = autoSelected !== undefined ? autoSelected : state.selectedItem;
                    return {
                        ...changes,
                        isOpen: false,
                        selectedItem: autoSelectedItem,
                        ...(autoSelectPolicy === 'always' && { inputValue: autoSelectedItem?.value ?? '' }),
                        ...(autoSelectPolicy === 'onBlur' &&
                            autoSelectedItem && { inputValue: autoSelectedItem.value }),
                    };
                }

                // HasInputSearch is false when the inputValue can't be set (Dropdown)
                if (state.highlightedIndex === -1 && !preventSelectionOnBlur && hasInputSearch && inputValue === '') {
                    // cancel current selection & reset input value
                    return { ...actionAndChanges.changes, selectedItem: null, inputValue: '' };
                }

                if (state.highlightedIndex === -1 && !preventSelectionOnBlur && selectedItem?.value !== inputValue) {
                    // select last selected item
                    return {
                        ...actionAndChanges.changes,
                        inputValue: selectedItem ? String(selectedItem.value) : '',
                        selectedItem,
                    };
                }

                if (state.highlightedIndex !== -1) {
                    return { ...actionAndChanges.changes, selectedItem: highlighted };
                }
            }

            // custom behavior when 'Esc' key is hit
            if (
                actionAndChanges.type === useCombobox.stateChangeTypes.InputKeyDownEscape &&
                escapeBehavior === 'revert'
            ) {
                return {
                    ...actionAndChanges.changes,
                    selectedItem: initialSelected ?? null,
                    inputValue: initialSelected?.value ? String(initialSelected.value) : '',
                };
            }

            // custom behavior when 'Enter' key is hit
            if (
                state.highlightedIndex === -1 &&
                actionAndChanges.type === useCombobox.stateChangeTypes.InputKeyDownEnter &&
                inputValue === ''
            ) {
                return { ...actionAndChanges.changes, selectedItem: null, inputValue: '' };
            }

            // custom behavior when 'Tab' key is hit
            if (
                allowClearOnTab &&
                state.highlightedIndex === -1 &&
                actionAndChanges.type === useCombobox.stateChangeTypes.FunctionCloseMenu &&
                inputValue === ''
            ) {
                return { ...actionAndChanges.changes, selectedItem: null, inputValue: '' };
            }
            if (
                allowSelectOnTab &&
                state.highlightedIndex === -1 &&
                actionAndChanges.type === useCombobox.stateChangeTypes.FunctionCloseMenu &&
                Boolean(inputValue)
            ) {
                const matchingItems = getFilteredItems(inputValue, items);
                if (matchingItems.length === 1 && items[0]?.displayedAs?.toLowerCase() === inputValue.toLowerCase()) {
                    return { ...actionAndChanges.changes, selectedItem: items[0], inputValue: items[0].displayedAs };
                }

                if (typeof getNewItem === 'function') {
                    const createdItem = getNewItem(inputValue);
                    return { ...actionAndChanges.changes, selectedItem: createdItem, inputValue };
                }
            }

            return actionAndChanges.changes;
        };

        const onInputValueChange = ({ inputValue, type }: UseComboboxStateChange<SelectItem | null>): void => {
            onInputChange(inputValue ?? '', type);
            if (!isDirty) {
                setIsDirty(true);
            }
        };

        const onHighlightedIndexChange = ({
            highlightedIndex: currentHighlightedIndex,
        }: UseComboboxStateChange<SelectItem | null>): void => {
            if (currentHighlightedIndex !== undefined && currentHighlightedIndex > -1 && !isDropdownDisabled) {
                setHighlighted(items[currentHighlightedIndex]);
                onHighlightedChange?.(items[currentHighlightedIndex]);
            } else {
                onHighlightedChange?.(null);
            }
        };

        const { getSelectedItemProps, getDropdownProps, selectedItems, addSelectedItem, setSelectedItems } =
            useMultipleSelection<SelectItem>({
                initialSelectedItems,
                itemToString,
                onSelectedItemsChange: (changes: UseMultipleSelectionStateChange<SelectItem>) => {
                    const shouldTriggerOnChange = !isEqual(
                        (controlledSelectedItems ?? []).map(s => s.id),
                        (changes.selectedItems ?? []).map(s => s.id),
                    );
                    if (shouldTriggerOnChange) {
                        const selectedRecords = (changes.selectedItems ?? [])
                            .map(s =>
                                Object.prototype.hasOwnProperty.call(s, '__collectionItem') ? s.__collectionItem : s,
                            )
                            .filter(Boolean) as CollectionItem[];
                        onSelectedItemsChange(selectedRecords);
                    }
                },
            });

        const {
            getInputProps,
            getItemProps,
            getMenuProps,
            isOpen,
            highlightedIndex,
            openMenu,
            setInputValue,
            getComboboxProps,
            selectedItem,
            inputValue,
            selectItem,
            closeMenu,
            getToggleButtonProps,
            toggleMenu,
            setHighlightedIndex,
        } = useCombobox<SelectItem | null>({
            items,
            itemToString,
            circularNavigation: true,
            defaultIsOpen,
            initialInputValue,
            onInputValueChange,
            ...(!isMultiSelect && {
                onSelectedItemChange,
                onStateChange: (changes): void => {
                    if (
                        (changes.type === useCombobox.stateChangeTypes.InputKeyDownEnter ||
                            changes.type === useCombobox.stateChangeTypes.ItemClick ||
                            (changes.type === useCombobox.stateChangeTypes.InputBlur && !preventSelectionOnBlur) ||
                            changes.type === useCombobox.stateChangeTypes.FunctionSelectItem) &&
                        Object.prototype.hasOwnProperty.call(changes, 'selectedItem')
                    ) {
                        onSelected(changes.selectedItem, changes.type);
                    }
                    if (
                        changes.type === useCombobox.stateChangeTypes.InputBlur &&
                        !preventSelectionOnBlur &&
                        !disabled &&
                        !readOnly &&
                        onBlur &&
                        !isInTable
                    ) {
                        onBlur();
                    }
                },
                stateReducer,
                initialSelectedItem: controlledSelectedItem ?? null,
                onHighlightedIndexChange,
            }),
            ...(isMultiSelect && {
                onStateChange: ({ type, selectedItem: sel, inputValue: newInputValue }): void => {
                    if (type === useCombobox.stateChangeTypes.InputChange) {
                        setInputValue(newInputValue || '');
                        return;
                    }
                    if (type === useCombobox.stateChangeTypes.InputBlur) {
                        selectItem(null);
                        setInputValue('');
                        if (minLookupCharacters > 0) {
                            setItems([]);
                        }
                        return;
                    }
                    if (
                        (type === useCombobox.stateChangeTypes.InputKeyDownEnter ||
                            type === useCombobox.stateChangeTypes.ItemClick) &&
                        sel
                    ) {
                        if (isItemSelected({ id: sel.id, value: String(sel.value) })) {
                            removeItem(sel);
                        } else {
                            addSelectedItem(sel);
                        }
                        selectItem(null);
                    }
                },
                stateReducer: (state, { changes, type }): Partial<UseComboboxState<SelectItem | null>> => {
                    if (type === useCombobox.stateChangeTypes.ItemClick) {
                        return { ...changes, inputValue: '', isOpen: true };
                    }
                    if (type === useCombobox.stateChangeTypes.InputKeyDownEnter) {
                        return { ...changes, highlightedIndex: state.highlightedIndex, inputValue: '', isOpen: true };
                    }
                    if (
                        type === useCombobox.stateChangeTypes.InputBlur ||
                        type === useCombobox.stateChangeTypes.FunctionSelectItem
                    ) {
                        return { ...changes, inputValue: '' };
                    }
                    return changes;
                },
            }),
        });

        const isModifiedSelectItem = (match: SelectItem, current: SelectItem | null): boolean => {
            if (current) {
                return match._id !== current._id;
            }

            return true;
        };

        const getAutoSelectedItem = async (matchingItems: SelectItem[]): Promise<SelectItem | null> => {
            if (matchingItems.length === 1 && inputValue) {
                if (isModifiedSelectItem(matchingItems[0], selectedItem)) {
                    const soundApi = new SoundApi(isSoundDisabled);
                    await soundApi.success();
                }

                return matchingItems[0];
            }
            return null;
        };

        function fetchItems(isOnItemsFetchedEnabled = true): Promise<SelectItem[]> {
            fetchKey.current = inputValue;
            setIsFetching(true);
            setError('');
            return getItems(inputValue)
                .then(async (result: SelectItem[]) => {
                    const filteredItems = getFilteredItems(inputValue, result);
                    const exactMatches =
                        getNewItem !== undefined || filteredItems.length <= 1
                            ? filteredItems
                            : filteredItems.filter(f => String(f.value) === inputValue);
                    setItems(filteredItems);
                    let autoSelectedItem;
                    let shouldApplyAutoSelection = false;
                    if (autoSelect) {
                        autoSelectedItem = await getAutoSelectedItem(exactMatches);
                        shouldApplyAutoSelection = Boolean(
                            autoSelectedItem || (autoSelectedItem === null && filteredItems.length === 0),
                        );
                        if (shouldApplyAutoSelection) {
                            setAutoSelected(autoSelectedItem);
                            setShouldAutoSelect(true);
                        }
                        if (inputValue && !filteredItems.length) {
                            const soundApi = new SoundApi(isSoundDisabled);
                            await soundApi.error();
                        }
                    }
                    if (isOnItemsFetchedEnabled) {
                        if (shouldApplyAutoSelection) {
                            onItemsFetched?.({ state: 'autoselect', item: autoSelectedItem?.__collectionItem ?? null });
                        } else {
                            onItemsFetched?.({ state: 'success', items: filteredItems });
                        }
                    }

                    return filteredItems;
                })
                .catch(err => {
                    xtremConsole.error(`Could not fetch items due to the following error: ${err.message}`);
                    onItemsFetched?.({ state: 'error', error: err.message });
                    setError('Could not fetch items...');
                    return [];
                })
                .finally(() => {
                    setIsFetching(false);
                    setIsLoading(false);
                });
        }

        useDeepCompareEffect(() => {
            if (!isEqualSelectedItem(controlledSelectedItem ?? null, selectedItem)) {
                setHelperText(controlledSelectedItem?.helperText);
                setInputValue(controlledSelectedItem?.value ? String(controlledSelectedItem.value) : '');
                selectItem(controlledSelectedItem ?? null);
                if (controlledSelectedItem?.value !== inputValue) {
                    fetchKey.current = null;
                }
            }
        }, [controlledSelectedItem]);

        useDeepCompareEffect(() => {
            setSelectedItems(controlledSelectedItems ?? []);
        }, [controlledSelectedItems]);

        useEffect(() => {
            if (!isLoading && fetchKey.current === inputValue && enter) {
                if (items.length === 1 && items[0].value === inputValue) {
                    closeMenu();
                    selectItem(items[0]);
                }
                setEnter(false);
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [isLoading, enter, fetchKey.current, inputValue]);

        useEffect(() => {
            if (selectedItem && !noHelperTextInItem) {
                setHelperText(selectedItem.helperText);
            } else {
                setHelperText(controlledHelperText);
            }
        }, [controlledHelperText, selectedItem, noHelperTextInItem]);

        useEffect(() => {
            setInitialSelected(controlledSelectedItem);
            setHelperText(controlledSelectedItem?.helperText ?? controlledHelperText);
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const isForceRefetch = useDeepCompareMemo(
            () => !isEqual(previousRefetchIfChanged.current, refetchIfChanged),
            [refetchIfChanged, previousRefetchIfChanged.current],
        );

        // Fetch items whenever input value changes & is at least "minLookupCharacters" long
        useDeepCompareEffect(() => {
            let cleanup = noop;

            setAutoSelected(inputValue === '' ? null : undefined);

            if (
                !isForceRefetch &&
                (fetchKey.current === inputValue ||
                    (!isInputFocused && inputValue === selectedItem?.value) ||
                    disabled ||
                    readOnly)
            ) {
                return cleanup;
            }

            if (inputValue.length >= minLookupCharacters) {
                setIsLoading(true);
                const timeout = setTimeout(() => {
                    fetchItems(isInputFocused && inputValue !== selectedItem?.value).then(newItems => {
                        if (isForceRefetch) {
                            previousRefetchIfChanged.current = refetchIfChanged;
                            if (newItems.length === 0) {
                                setInputValue('');
                                selectItem(null);
                            }
                        }
                    });
                }, FETCH_TIMEOUT);
                cleanup = (): void => {
                    clearTimeout(timeout);
                };
            } else if (fetchKey.current !== inputValue && (!isMultiSelect || (isMultiSelect && inputValue !== ''))) {
                setItems([]);
                fetchKey.current = null;
            }
            return cleanup;
        }, [
            minLookupCharacters,
            inputValue,
            isInputFocused,
            disabled,
            readOnly,
            isMultiSelect,
            isForceRefetch,
            refetchIfChanged,
            selectedItem?.value,
        ]);

        const isAutoSelectPolicyEnabled = useMemo(
            () => autoSelectPolicy === 'always' || (autoSelectPolicy === 'onBlur' && !isInputFocused),
            [autoSelectPolicy, isInputFocused],
        );

        useEffect(() => {
            if (
                !isLoading &&
                isAutoSelectPolicyEnabled &&
                fetchKey.current === inputValue &&
                autoSelected !== undefined
            ) {
                if (shouldAutoSelect) {
                    onAutoSelect?.(autoSelected ?? undefined);
                    selectItem(autoSelected ?? null);
                }
                setShouldAutoSelect(false);
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [isLoading, enter, inputValue, shouldAutoSelect, autoSelected, isAutoSelectPolicyEnabled]);

        useEffect(() => {
            if (highlightText) {
                const matches = items.filter(item => {
                    return (
                        `${item.displayedAs || item.value}`.substring(0, highlightText.length).toLowerCase() ===
                        highlightText.toLowerCase()
                    );
                });

                if (matches.length) {
                    const index = items.findIndex(item => item.id === matches[0].id);

                    if (index >= 0) {
                        setHighlightedIndex(index);
                    }
                }
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [highlightText]);

        useLayoutEffect(() => {
            if (inputRef.current && ulRef.current && isOpen) {
                let parent = ulRef.current;
                while (parent && parent !== document.documentElement) {
                    // break if we are in class "e-page-body-container"
                    if (parent.classList.contains('e-page-body-container')) {
                        break;
                    }

                    if (window.getComputedStyle(parent).overflow === 'hidden') {
                        break;
                    }

                    if (parent.classList.contains('e-table-sidebar-section')) {
                        break;
                    }
                    parent = parent.parentElement as HTMLUListElement;
                }

                if (parent) {
                    let totalHeight = Math.min(items.length || 1, 8) * 36;
                    const inputRect = inputRef.current.getBoundingClientRect();
                    const parentRect = parent.getBoundingClientRect();
                    const spaceAvailableOnAbove = inputRect.top - parentRect.top - 8;
                    const spaceAvailableOnBelow = parentRect.bottom - inputRect.bottom - 8;
                    if (hasLookupIcon) {
                        totalHeight += 33;
                    }
                    if (onCreateNewItemLinkClick) {
                        totalHeight += 33;
                    }

                    const canRenderBelow = spaceAvailableOnBelow >= totalHeight;
                    // Here we check if the dropdown can be rendered below just fine
                    if (canRenderBelow) {
                        setDropdownMaxHeight(totalHeight);
                        return;
                    }

                    // If not, we check if it can be rendered above
                    const canRenderAbove = spaceAvailableOnAbove >= totalHeight;
                    if (canRenderAbove) {
                        setForceRenderOptionsAbove(true);
                        setDropdownMaxHeight(totalHeight);
                        return;
                    }

                    // If it cannot render neither above or below, we restrict the dropdown height and render where we have more space at
                    const shouldRenderAbove = spaceAvailableOnBelow < spaceAvailableOnAbove;
                    if (shouldRenderAbove) {
                        setForceRenderOptionsAbove(true);
                    }
                    setDropdownMaxHeight(Math.max(spaceAvailableOnBelow, spaceAvailableOnAbove));
                }
            }
            if (!isOpen) {
                setForceRenderOptionsAbove(false);
            }
        }, [hasLookupIcon, isOpen, items.length, onCreateNewItemLinkClick]);

        const handleInputBlur =
            (original?: (event: React.FocusEvent<HTMLInputElement>) => void) =>
            (event: React.FocusEvent<HTMLInputElement>): void => {
                const isTargetLookupLink = lookupLinkRef.current && event?.relatedTarget === lookupLinkRef.current;
                const isTargetTunnelLink =
                    createTunnelLinkRef.current && event?.relatedTarget === createTunnelLinkRef.current;
                const isTargetLookup = event?.relatedTarget?.id && event?.relatedTarget?.id === lookupIconId;
                const isTargetChevron =
                    event?.relatedTarget instanceof HTMLButtonElement &&
                    event?.relatedTarget?.classList.contains('e-ui-select-input-chevron');

                const shouldPreventPropagation =
                    isTargetLookupLink || isTargetTunnelLink || isTargetLookup || isTargetChevron;
                // prevents downshift's default behavior and avoids going through its reducer
                const shouldPreventDownshiftDefault = isTargetLookupLink || isTargetTunnelLink;

                if (shouldPreventPropagation) {
                    event.stopPropagation();
                }
                if (shouldPreventDownshiftDefault) {
                    (event.nativeEvent as any).preventDownshiftDefault = true;
                }
                if (
                    selectedItem === null &&
                    closeIconClassName &&
                    event.relatedTarget?.parentElement?.classList.contains(closeIconClassName)
                ) {
                    const lookupIcon = event.target.parentElement?.querySelector(`#${lookupIconId}`) as HTMLElement;
                    if (lookupIcon) {
                        lookupIcon.focus();
                    }
                }
                setIsInputFocused(false);
                original?.(event);

                if (
                    !preventSelectionOnBlur &&
                    event.relatedTarget?.querySelector?.(`[aria-controls="${defaultInputProps['aria-controls']}"]`) ==
                        null &&
                    onBlur &&
                    !isTargetLookup &&
                    !isTargetChevron &&
                    !isTargetLookupLink &&
                    !isTargetTunnelLink
                ) {
                    if (screenId && elementId) {
                        wrapperService.onBlur({
                            screenId,
                            elementId,
                        });
                    }
                    onBlur();
                }
            };

        const handleInputClick = (): void => {
            if (!isOpen && !disabled && !readOnly) {
                openMenu();
            }
        };

        const focusLookupButton = (): void => {
            lookupButtonRef?.current?.focus();
        };

        const highlightFirstListItem = (): void => {
            if (items && items.length > 0) {
                inputRef.current?.focus();
                setTimeout(() => {
                    inputRef.current?.setSelectionRange(inputValue.length, inputValue.length);
                });
                setHighlightedIndex(0);
            } else {
                inputRef.current?.focus();
            }
        };

        const highlightLastListItem = (): void => {
            if (items && items.length > 0) {
                inputRef.current?.focus();
                setTimeout(() => {
                    inputRef.current?.setSelectionRange(inputValue.length, inputValue.length);
                });
                setHighlightedIndex(items.length - 1);
            } else {
                inputRef.current?.focus();
            }
        };

        const getFilteredItems = (value: string, elements: SelectItem[]): SelectItem[] => {
            let filteredItems: SelectItem[] = !shouldFilterItems
                ? elements
                : elements
                      .filter(i => {
                          if (value === undefined || !hasInputSearch) {
                              return true;
                          }

                          return (
                              [i.displayedAs, i.value].filter(
                                  v =>
                                      v !== undefined &&
                                      v !== null &&
                                      (typeof v === 'string' || typeof v === 'number') &&
                                      String(v).toLowerCase().indexOf(value.toLowerCase()) !== -1,
                              ).length > 0
                          );
                      })
                      .map(e => {
                          return {
                              ...e,
                              // displayedAs: e.displayedAs ? mark(e.displayedAs, value) : e.displayedAs,
                          };
                      });
            if (isSortedAlphabetically) {
                filteredItems = filteredItems.sort((curr: SelectItem, next: SelectItem) => {
                    const currDisplay = `${curr.displayedAs || curr.value}`;
                    const nextDisplay = `${next.displayedAs || next.value}`;

                    return currDisplay.toLowerCase().localeCompare(nextDisplay.toLowerCase());
                });
            }

            if (getNewItem !== undefined && value !== '' && !filteredItems.find(item => item.value === value)) {
                const newItem = getNewItem(value);
                return newItem ? [newItem, ...filteredItems] : filteredItems;
            }

            return filteredItems;
        };

        const isItemSelected = ({ id, value }: { id: string; value: string }): boolean => {
            return isMultiSelect
                ? Boolean(selectedItems.find(s => s?.id === id))
                : selectedItem?.id === '' || selectedItem?.id === '-1'
                  ? value === selectedItem?.value
                  : selectedItem?.id === id;
        };

        const getMultiSelectValue = (): string => {
            const reducedItemText = selectedItems.reduce((prev, curr) => {
                if (prev) return `${prev}, ${curr.value}`;
                return String(curr.value);
            }, '');
            return `${reducedItemText}${inputValue}`;
        };

        const getDisplayValue = (): string => {
            if (!selectedItem || selectedItem.value === null) {
                return '';
            }

            return String(selectedItem.displayedAs || selectedItem.value);
        };

        const defaultInputProps: UseComboboxGetInputPropsOptions = isMultiSelect
            ? getInputProps({
                  ref,
                  ...getDropdownProps({
                      ref,
                      placeholder: selectedItems && selectedItems.length > 0 ? '' : placeholder,
                      ...(!hasInputSearch && { value: getMultiSelectValue() }),
                      readOnly,
                  }),
              })
            : hasInputSearch
              ? getInputProps({
                    ref,
                    ...getDropdownProps({
                        ref,
                        placeholder,
                        readOnly,
                    }),
                })
              : getInputProps({
                    ref,
                    ...getDropdownProps({
                        ref,
                        placeholder,
                        readOnly,
                        value: getDisplayValue(),
                    }),
                });

        const handleFocus: React.FocusEventHandler<HTMLInputElement> = event => {
            if (screenId && elementId) {
                wrapperService.onFocus({ screenId, elementId, title: label }, setInputValue);
            }
            if (!isInputFocused) {
                setIsInputFocused(true);
                onInputFocus();
                setTimeout(() => {
                    /**
                     * If the focus within the input first positioned on the dropdown chevron button
                     * then the menu gets opened twice, which will close it again.
                     *  */
                    const isToggleButtonFocussed =
                        event?.relatedTarget?.classList.contains('e-ui-select-inline-dropdown') ||
                        event?.relatedTarget?.classList.contains('e-ui-select-input-chevron');
                    if (!disableOpenOnFocus && !isOpen && !disabled && !readOnly && !isToggleButtonFocussed) {
                        openMenu();
                    }
                }, FOCUS_TIMEOUT);
            }
        };

        const isHighlightInput = (key: string): boolean => {
            return /^[a-zA-Z0-9]{1}$/.test(key) || (!!highlightText && key === ' ');
        };

        const onInputKeyDown: React.ComponentProps<'input'>['onKeyDown'] = e => {
            const isArrowDownOnLast = e.key === 'ArrowDown' && highlightedIndex === items.length - 1;
            const isArrowUpOnFirst = e.key === 'ArrowUp' && highlightedIndex === 0;
            if ((lookupLinkRef.current || createTunnelLinkRef.current) && (isArrowDownOnLast || isArrowUpOnFirst)) {
                e.preventDefault();
                e.stopPropagation();
                setHighlightedIndex(-1);
                if (isArrowDownOnLast) {
                    [lookupLinkRef.current, createTunnelLinkRef.current].find(b => b != null)?.focus();
                } else {
                    [createTunnelLinkRef.current, lookupLinkRef.current].find(b => b != null)?.focus();
                }

                return;
            }

            if (e.key === 'Escape' && (disabled || readOnly)) {
                e.stopPropagation();
                return;
            }

            // Handle highlight for dropdown & multi-dropdown lists
            if (!disabled && !isDropdownDisabled && !hasInputSearch && isHighlightInput(e.key)) {
                setHighlightText(highlightText + e.key);
                highlightTimer.start(() => {
                    setHighlightText('');
                }, 1000);
            } else if (!disabled && !isDropdownDisabled && !hasInputSearch && isBackspace(e)) {
                setHighlightText('');
                highlightTimer.abort();
            }

            // Allow empty selection by hitting 'Enter' key
            if (highlightedIndex === -1 && isEnter(e)) {
                if (inputValue === '') {
                    setInputValue('');
                    closeMenu();
                    selectItem(null);
                } else {
                    setEnter(true);
                }
            } else if (isMultiSelect && isBackspace(e) && inputValue === '') {
                const lastSelected = selectedItems[selectedItems.length - 1];
                if (lastSelected) {
                    removeItem(lastSelected);
                    return;
                }
            } else if (isMultiSelect && !hasInputSearch && isSpaceBar(e)) {
                e.stopPropagation();
                e.preventDefault();
                toggleMenu();
                return;
            }
            defaultInputProps.onKeyDown?.(e);

            onKeyDown(e);

            // Adding preventDefault to avoid form submission on 'Enter' key by Carbon component
            if (isEnter(e)) {
                e.preventDefault();
            }
        };

        const inputProps: InputProps = {
            ...defaultInputProps,
            ref: (newInputRef: HTMLInputElement) => {
                (defaultInputProps.ref as (instance: HTMLInputElement | null) => void)(newInputRef);
                inputRef.current = newInputRef;
            },
            onKeyDown: onInputKeyDown,
            onClick: handleInputClick,
            onFocus: handleFocus,
        };

        const wrapperProps = getComboboxProps({
            // Add "bold" class in case all items have been selected
            className:
                selectedItems.length > 0 &&
                items.length > 0 &&
                difference(
                    items.map(i => i.id),
                    selectedItems.map(s => s.id),
                ).length === 0
                    ? 'bold'
                    : undefined,
        });

        const addItem = (item: SelectItem): void => {
            if (isMultiSelect) {
                addSelectedItem(item);
                setInputValue('');
                selectItem(null);
            } else {
                selectItem(item);
            }
        };

        const removeItem = (item: SelectItem): void => {
            if (isMultiSelect) {
                const newSelectedItems = selectedItems.filter(s => s.id !== item.id);
                setSelectedItems(newSelectedItems);
                setInputValue('');
                selectItem(null);
            } else {
                selectItem(null);
            }
        };

        const displayNew =
            getNewItem !== undefined &&
            !isInputFocused &&
            !isLoading &&
            items[0]?.id === '-1' &&
            selectedItem?.id === '-1' &&
            inputValue === items[0]?.value &&
            inputValue === selectedItem?.value;

        const cleanInput = (): void => {
            setInputValue('');
            selectItem(null);
            closeMenu();

            setTimeout(() => {
                inputRef.current?.focus();
            }, 150);
        };

        const menuProps = getMenuProps(undefined, { suppressRefError: true });

        const checkIsLinkHelperText = (): boolean => {
            return (isLinkHelperText && !!inputProps.value && inputProps.value !== SELECT_EMPTY_VALUE) || false;
        };

        return (
            <>
                <SelectInput
                    borderColor={borderColor}
                    closeIconClassName={closeIconClassName}
                    closeMenu={closeMenu}
                    disabled={disabled}
                    disablePills={disablePills}
                    displayNew={displayNew}
                    error={controlledError || error}
                    helperText={helperText}
                    getSelectedItemProps={getSelectedItemProps}
                    handleBlur={handleInputBlur(inputProps.onBlur)}
                    handleSelectedItemRemove={removeItem}
                    hasInputSearch={hasInputSearch}
                    hasLookupIcon={hasLookupIcon || isDropdownDisabled}
                    helperTextLink={helperTextLink}
                    icon={icon}
                    image={!readOnly && !disabled && selectedItem?.image ? selectedItem.image : undefined}
                    info={info}
                    inputId={inputId}
                    inputProps={inputProps}
                    isMultiSelect={isMultiSelect}
                    label={label}
                    loading={isFetching}
                    lookupButtonRef={lookupButtonRef}
                    lookupIconId={lookupIconId}
                    onClearFieldButtonClick={hasClearFieldButton && !readOnly && !disabled ? cleanInput : undefined}
                    onLookupIconClick={onLookupIconClick}
                    ref={ref}
                    selectedRecords={selectedItems}
                    size={size}
                    testId={testId}
                    toggleButtonProps={getToggleButtonProps()}
                    variant={variant}
                    warning={warning}
                    width={inputWidth}
                    onHelperTextLinkClick={onHelperTextLinkClick}
                    isLinkHelperText={checkIsLinkHelperText()}
                    wrapperProps={wrapperProps}
                />

                {!isDropdownDisabled && !readOnly && (
                    <SelectDropDown
                        addSelectedItem={addItem}
                        createTunnelLinkRef={createTunnelLinkRef}
                        createTunnelLinkText={createTunnelLinkText}
                        focusLookupButton={focusLookupButton}
                        fullWidth={fullWidth}
                        inputRef={inputRef}
                        getItemProps={getItemProps}
                        hasHelperTextField={
                            (variant === 'carbon' && Boolean(helperText) && !isHelperTextHidden) ||
                            checkIsLinkHelperText()
                        }
                        hasHighlightMatchText={hasHighlightMatchText}
                        hasLookupIcon={hasLookupIcon}
                        highlightedIndex={highlightedIndex}
                        highlightFirstListItem={highlightFirstListItem}
                        highlightLastListItem={highlightLastListItem}
                        inputValue={inputValue}
                        isItemSelected={isItemSelected}
                        isLinkCreateNewText={isLinkCreateNewText}
                        isMultiSelect={isMultiSelect}
                        isOpen={!isDropdownDisabled && isOpen}
                        items={items}
                        loading={isLoading}
                        lookupLinkRef={lookupLinkRef}
                        maxHeight={dropdownMaxHeight}
                        menuProps={menuProps}
                        minLookupCharacters={minLookupCharacters}
                        node={node}
                        onCreateNewItemLinkClick={onCreateNewItemLinkClick}
                        onLookupIconClick={onLookupIconClick}
                        removeSelectedItem={removeItem}
                        screenId={screenId}
                        shouldRenderOptionsAbove={forceRenderOptionsAbove || shouldRenderOptionsAbove}
                        ulRef={ulRef}
                        variant={variant}
                        inputId={inputId}
                    />
                )}
            </>
        );
    },
);

Select.displayName = 'Select';
