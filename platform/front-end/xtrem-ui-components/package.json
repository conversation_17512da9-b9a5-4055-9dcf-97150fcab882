{"name": "@sage/xtrem-ui-components", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["README.md", "CHANGELOG.md", "build"], "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">18.0.0", "npm": ">9.0.0"}, "dependencies": {"@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-shared": "workspace:*", "carbon-react": "146.2.1", "draft-js": "^0.11.7", "lodash": "^4.17.21", "react": "^18.3.1", "react-is": "^18.3.1", "stream-browserify": "^3.0.0", "styled-components": "^5.3.11", "timers-browserify": "^2.0.12", "ts-essentials": "^10.0.0", "tslib": "^2.5.0", "uid": "^1.0.0", "usehooks-ts": "^2.6.0", "utility-types": "^3.4.1"}, "devDependencies": {"@chromatic-com/storybook": "^3.0.0", "@sage/xtrem-cli-transformers": "workspace:*", "@sage/xtrem-static-shared": "workspace:*", "@side/jest-runtime": "^1.1.0", "@storybook/addon-essentials": "^8.1.5", "@storybook/addon-interactions": "^8.1.5", "@storybook/addon-links": "^8.1.5", "@storybook/addon-onboarding": "^8.1.5", "@storybook/addon-styling-webpack": "^1.0.0", "@storybook/addon-webpack5-compiler-swc": "^1.0.3", "@storybook/blocks": "^8.1.5", "@storybook/react": "^8.1.5", "@storybook/react-webpack5": "^8.1.5", "@storybook/test": "^8.1.5", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.5", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.14.198", "@types/react": "^18.3.3", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.29", "babel-jest": "^29.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "esbuild-loader": "^2.16.0", "eslint": "^8.49.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "google-closure-compiler": "^20240317.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "2.0.0", "jest-styled-components": "^7.1.1", "jsdom": "^26.0.0", "prettier": "^3.3.3", "pretty-format": "29.7.0", "resolve-url-loader": "^5.0.0", "sass": "^1.55.0", "sass-loader": "^16.0.0", "source-map-support": "^0.5.12", "style-loader": "^4.0.0", "storybook": "^8.1.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-patch": "^3.0.2", "tsd": "^0.31.0", "typescript": "~5.8.0", "uuid": "^9.0.1"}, "bundledDependencies": ["@sage/design-tokens"], "scripts": {"build": "rm -rf build && storybook build && pnpm build:ts && pnpm mergeTranslationFiles && pnpm build:sass", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:sass": "sass lib/index.scss > build/xtrem-ui-components.css", "build:tests": "tsc -b tsconfig.test.json", "build:ts": "tspc -b -v .", "clean": "rm -rf build storybook-static junit-report* junit.xml automation/report/* .awcache .temp_cache docs tsconfig.tsbuildinfo", "lint": "eslint -c .eslintrc.js --ext .ts --ext .tsx lib", "lint:fix": "pnpm prettier:write && pnpm lint --fix", "mergeTranslationFiles": "node ../../cli/xtrem-cli-transformers/build/lib/transformers/merge-translation-files-current-dir", "prettier:check": "prettier --trailing-comma all --list-different \"lib/**/*.{ts,tsx}\"", "prettier:write": "prettier --trailing-comma all --write \"lib/**/*.{ts,tsx}\" package.json", "test": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui-components\" JEST_JUNIT_OUTPUT=\"./junit-report-ui-components.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=50% --config=jest.config.js", "test:ci": "pnpm build:tests && cross-env NODE_ENV=\"test\" NO_CONSOLE=1 JEST_SUITE_NAME=\"xtrem-ui-components\" JEST_JUNIT_OUTPUT_NAME=\"./junit-report-ui-components.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --config=jest.config.js --no-cache --coverage  --maxWorkers=3", "test:concurrent": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui-components\" JEST_JUNIT_OUTPUT=\"./junit-report-ui-components.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=4 --config=jest.config.js", "test:local": "cross-env DEBUG_PRINT_LIMIT=10 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui-components\" JEST_JUNIT_OUTPUT=\"./junit-report-ui-components.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=50% --config=jest.config.js", "test:watch": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui-components\" JEST_JUNIT_OUTPUT=\"./junit-report-ui-components.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --watchAll --config=jest.config.js", "start": "npm run storybook", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}}