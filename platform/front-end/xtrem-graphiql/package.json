{"name": "@sage/xtrem-graphiql", "description": "middleware to customize Xtrem graphiql welcome page", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-graphql-demo-page": "workspace:*", "accepts": "^1.3.8", "express": "^4.19.2"}, "devDependencies": {"@types/accepts": "^1.3.5", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "eslint": "^8.49.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "echo \"Error: no test specified for xtrem-graphiql\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-graphiql\" && exit 0", "xtrem-bytenode": "xtrem-bytenode"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}