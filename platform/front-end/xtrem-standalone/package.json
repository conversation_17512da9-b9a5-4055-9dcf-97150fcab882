{"name": "@sage/xtrem-standalone", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "README.md", "CHANGELOG.md"], "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@sage/design-tokens": "4.35.0", "@sageai/gms-chat-ui-react": "1.14.4", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-components": "workspace:*", "axios": "^1.8.4", "carbon-react": "146.2.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "date-fns": "^2.28.0", "draft-js": "^0.11.7", "i18n-js": "^4.0.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-is": "^18.3.1", "react-redux": "^9.0.0", "redux": "^4.0.4", "redux-responsive": "^4.3.8", "redux-thunk": "^2.3.0", "stream-browserify": "^3.0.0", "styled-components": "^5.3.11", "timers-browserify": "^2.0.12"}, "devDependencies": {"@sage/xtrem-cli-transformers": "workspace:*", "@sage/xtrem-static-shared": "workspace:*", "@swc/core": "1.2.173", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/chai": "^4.3.6", "@types/crypto-js": "^4.2.0", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.198", "@types/new-relic-browser": "^1.0.0", "@types/pendo-io-browser": "2.19.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.0.0", "@types/react-redux": "^7.1.5", "@types/redux-mock-store": "^1.0.0", "@types/styled-components": "^5.1.29", "@types/styled-system": "^5.1.13", "adjust-sourcemap-loader": "^5.0.0", "axe-core": "^4.7.1", "chai": "^4.3.10", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "eslint": "^8.49.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "google-closure-compiler": "^20240317.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "2.0.0", "jest-styled-components": "^7.1.1", "jsdom": "^26.0.0", "mini-css-extract-plugin": "^2.0.0", "prettier": "^3.3.3", "pretty-format": "29.7.0", "redux-mock-store": "^1.5.3", "resolve-url-loader": "^5.0.0", "sass": "^1.55.0", "sass-loader": "^16.0.0", "source-map-loader": "^5.0.0", "source-map-support": "^0.5.12", "style-loader": "^4.0.0", "svg-inline-loader": "^0.8.2", "terser-webpack-plugin": "^5.1.1", "ts-essentials": "^10.0.0", "ts-jest": "^29.1.1", "ts-loader": "^9.4.2", "typescript": "~5.8.0", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0", "webpack-notifier": "^1.7.0", "webpackbar": "^7.0.0"}, "scripts": {"build": "pnpm build:ts && pnpm build:webpack", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:dev": "pnpm build:ts && pnpm build:webpack:dev", "build:ts": "tsc -b -v .", "build:webpack": "webpack --config ./webpack/prod.js", "build:webpack:dev": "webpack --config ./webpack/dev.js", "clean": "rm -rf build junit-report* automation/report/* .awcache docs .temp_cache", "lint": "eslint -c .eslintrc.js --ext .ts --ext .tsx lib", "lint:fix": "pnpm prettier:write && pnpm lint --fix", "prettier:check": "prettier --list-different \"lib/**/*.{ts,tsx}\"", "prettier:write": "prettier --write \"lib/**/*.{ts,tsx}\"", "test": "cross-env NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-standalone\" JEST_JUNIT_OUTPUT=\"./junit-report-standalone.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --config=jest.json", "test:ci": "cross-env NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-standalone\" JEST_JUNIT_OUTPUT=\"./junit-report-standalone.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --coverage --config=jest.json --no-cache --runInBand", "watch": "tsc -d && webpack --watch --config ./webpack/dev.js"}}