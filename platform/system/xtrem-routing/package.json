{"name": "@sage/xtrem-routing", "description": "XTREM routing", "version": "54.0.58", "xtrem": {"isPlatform": true, "isSealed": true, "isService": true, "isHidden": true, "queue": "routing", "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-system": "workspace:*", "lodash": "^4.17.21", "nanoid": "^3.3.8"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-messaging-wrapper": "^3.1.2", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^8.49.0", "mocha": "^10.8.2", "sinon": "^19.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "xtrem lint", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --unit --graphql --layers=test", "test:ci": "xtrem test --unit --ci --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}