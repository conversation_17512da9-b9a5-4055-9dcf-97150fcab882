{"name": "@sage/xtrem-upload", "description": "XTREM upload", "version": "54.0.58", "xtrem": {"isPlatform": true, "hasListeners": true, "queue": "import-export", "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": ["xtrem-upload"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.714.0", "@aws-sdk/client-sso-oidc": "^3.714.0", "@aws-sdk/client-sts": "^3.714.0", "@aws-sdk/credential-provider-node": "^3.714.0", "@aws-sdk/credential-providers": "^3.714.0", "@aws-sdk/s3-request-presigner": "^3.714.0", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-file-storage": "^6.1.5", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-workflow": "workspace:*", "axios": "^1.8.4", "isbinaryfile": "^5.0.4", "lodash": "^4.17.21", "nanoid": "^3.3.8"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-upload-api": "workspace:*", "@sage/xtrem-workflow-api": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/sinon": "^17.0.1", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^8.49.0", "graphql": "16.1.0-experimental-stream-defer.6", "mocha": "^10.8.2", "sinon": "^19.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "xtrem lint", "load:test:data": "xtrem layers --load setup,test", "extract:test:data": "xtrem layers --extract test", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --unit --graphql --workflow --layers=test", "test:ci": "xtrem test --unit --graphql --workflow --ci --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}