{"name": "@sage/xtrem-synchronization", "description": "XTREM synchronization", "version": "54.0.58", "xtrem": {"isPlatform": true, "isService": true, "hasListeners": true, "queue": "interop", "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "graphql": "16.1.0-experimental-stream-defer.6", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-synchronization-api": "workspace:*", "@types/chai": "^4.3.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/semver": "^7.5.2", "@types/sinon": "^17.0.0", "chai": "^4.3.10", "eslint": "^8.49.0", "mocha": "^10.8.2", "sinon": "^19.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "initTenant": "xtrem tenant --init '********************************************************************************************************************************************************************************************************'", "lint": "xtrem lint", "load:test:data": "xtrem layers --load setup,test", "extract:test:data": "xtrem layers --extract test", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --unit --graphql --layers=test", "test:ci": "xtrem test --unit --ci --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}