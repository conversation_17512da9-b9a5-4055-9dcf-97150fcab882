{"name": "@sage/xtrem-data-management", "version": "54.0.58", "description": "Tools for data management", "main": "build/index.js", "typings": "build/package-definition.d.ts", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "author": "Sage", "license": "UNLICENSED", "bugs": {"url": "https://github.com/Sage-ERP-X3/xtrem-platform/issues"}, "homepage": "https://github.com/Sage-ERP-X3/xtrem-platform#readme", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-layers": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-postgres": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-toposort": "workspace:*", "csv-parse": "^5.4.0", "express": "^4.19.2", "glob": "^11.0.0", "lodash": "^4.17.21", "minimatch": "^10.0.0", "nanoid": "^3.3.8", "semver": "^7.6.3", "sinon": "^19.0.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/express": "5.0.0", "@types/lodash": "^4.14.198", "@types/minimatch": "^5.1.2", "@types/mocha": "^10.0.1", "@types/semver": "^7.5.2", "@types/sinon": "^17.0.0", "@types/supertest": "^6.0.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "dts-generator": "^3.0.0", "eslint": "^8.49.0", "mocha": "^10.8.2", "prettier": "^3.3.3", "supertest": "^7.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "copy:template": "copyfiles {lib,test}/**/template-rules.sql build", "dts-bundle": "xtrem-dts-bundle", "dts-gen": "dts-generator --name @sage/xtrem-core --project . --out build/xtrem-core.d.ts --main @sage/xtrem-core/index > dts-generator.log", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:fix": "eslint -c .eslintrc.js --fix --ext .ts lib test", "posttest:ci": "rm -rf build/lib/pages", "prebuild": "pnpm copy:template", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "test": "cross-env TZ=CET mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-data-management.xml JUNIT_REPORT_NAME='xtrem-data-management' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "test:ci:allDatabases": "pnpm test:ci", "test:coverage": "c8 --reporter=html mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:debug": "mocha  --inspect-brk --recursive \"test/**/*@(-|.)test.ts\" --exit", "xtrem-bytenode": "xtrem-bytenode"}}