{"name": "@sage/eslint-plugin-xtrem", "version": "54.0.58", "description": "Lint rules for Xtrem development", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/lib/index.js", "engines": {"node": ">=12.0.0"}, "peerDependencies": {"eslint": ">=7.12.1"}, "dependencies": {"@sage/xtrem-ts-to-sql": "workspace:*", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@typescript-eslint/utils": "^7.10.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^10.1.0", "@sage/eslint-plugin-redos": "^1.1.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-unused-imports": "^4.0.0", "lodash": "^4.17.21", "requireindex": "^1.0.0", "typescript": "~5.8.0"}, "devDependencies": {"@types/estree": "1.0.6", "@types/lodash": "^4.14.198", "c8": "^10.1.2", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -p .", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "mocha --recursive --exit \"test/lib/rules/*.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-eslint-plugin-xtrem.xml JUNIT_REPORT_NAME='eslint-plugin-xtrem' c8 --reporter=lcov --reporter=cobertura --reporter=json --reporter=text mocha  --recursive --exit \"test/lib/rules/*.ts\" --reporter mocha-jenkins-reporter"}}