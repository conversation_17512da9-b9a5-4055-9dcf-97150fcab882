{"name": "@sage/xtrem-dts-bundle", "description": "helper for migration to async/await", "version": "54.0.58", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "bin": {"xtrem-dts-bundle": "./bin/xtrem-dts-bundle"}, "dependencies": {"glob": "^11.0.0", "typescript": "~5.8.0"}, "devDependencies": {"@types/node": "^22.10.2", "eslint": "^8.49.0"}, "scripts": {"build": "pnpm clean && tsc -b -v .", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "xtrem-bytenode": "xtrem-bytenode", "xtrem-dts-bundle": "./bin/xtrem-dts-bundle"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}