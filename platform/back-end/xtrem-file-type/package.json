{"name": "@sage/xtrem-file-type", "description": "Wrapper of file-type ESM", "version": "54.0.58", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build"], "dependencies": {"file-type": "^20.0.0"}, "devDependencies": {"copyfiles": "^2.1.0"}, "scripts": {"build": "copyfiles lib/**/* build && copyfiles lib/* build && copyfiles package-definition.d.ts build && copyfiles index.js build", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "echo \"Error: no test specified for xtrem-file-type\" && exit 0", "test:ci": "echo \"Error: no test specified for -file-type\" && exit 0"}}