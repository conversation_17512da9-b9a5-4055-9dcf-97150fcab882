{"name": "@sage/xtrem-core", "description": "Xtrem framework core", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.714.0", "@aws-sdk/client-sso-oidc": "^3.714.0", "@aws-sdk/client-sts": "^3.714.0", "@aws-sdk/credential-provider-node": "^3.714.0", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-file-type": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-postgres": "workspace:*", "@sage/xtrem-secret": "^3.1.2", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-toposort": "workspace:*", "@sage/xtrem-ts-to-sql": "workspace:*", "archiver": "^7.0.0", "axios": "^1.8.4", "body-parser": "^2.0.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "content-type": "1.0.5", "crc-32": "^1.2.2", "dnscache": "^1.0.2", "dompurify": "3.2.4", "eslint": "^8.49.0", "express": "^4.19.2", "express-graphql": "0.12.0-experimental-stream-defer.1", "extract-zip": "^2.0.1", "fs-extra": "^11.2.0", "glob": "^11.0.0", "graphql": "16.1.0-experimental-stream-defer.6", "js-yaml": "^4.1.0", "jsdom": "^26.0.0", "json5": "^2.2.3", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "matcher": "^4.0.0", "mime-types": "^2.1.35", "minimatch": "^10.0.0", "mnemonist": "^0.40.0", "nanoid": "^3.3.8", "newrelic": "^12.0.0", "postgres-array": "^3.0.2", "prettier": "^3.3.3", "prom-client": "^15.1.2", "sass": "^1.56.0", "semver": "^7.6.3", "sinon": "^19.0.0", "typescript": "~5.8.0", "unzipper": "^0.12.3", "workerpool": "^9.1.0"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/archiver": "^6.0.0", "@types/body-parser": "^1.19.2", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/content-type": "^1.0.1", "@types/express": "5.0.0", "@types/fs-extra": "^11.0.4", "@types/js-yaml": "^4.0.6", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "9.0.8", "@types/lodash": "^4.14.198", "@types/mime-types": "2.1.4", "@types/minimatch": "^5.1.2", "@types/mocha": "^10.0.1", "@types/newrelic": "^9.4.0", "@types/node": "^22.10.2", "@types/semver": "^7.5.2", "@types/sinon": "^17.0.0", "@types/supertest": "^6.0.0", "@types/unzipper": "^0.10.11", "c8": "^10.1.2", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "dts-generator": "^3.0.0", "eslint": "^8.49.0", "mocha": "^10.8.2", "source-map-support": "^0.5.12", "supertest": "^7.0.0", "terser": "5.39.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle && ../../../scripts/i18n/sync-entries-with-base.js . && copyfiles -f lib/i18n/* build/lib/i18n", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\" -\"build/lib/runtime/system-properties.js\" -\"build/lib/pages/*.js\" -\"build/lib/page-extensions/*.js\" -\"build/lib/page-fragments/*.js\" -\"build/lib/stickers/*.js\" -\"build/lib/security/xss-worker.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "uglify:js": "terser --compress --mangle --source-map -o build/lib/security/xss-worker.js -- lib/security/xss-worker.js", "copy:fixtures": "copyfiles -f test/fixtures/sample-files/* build/test/fixtures/sample-files && copyfiles -f test/fixtures/pages/* build/lib/pages && copyfiles -f test/fixtures/stickers/* build/lib/stickers  && copyfiles -f test/fixtures/page-extensions/* build/lib/page-extensions && copyfiles -f test/fixtures/page-fragments/* build/lib/page-fragments", "dts-bundle": "xtrem-dts-bundle", "dts-gen": "dts-generator --name @sage/xtrem-core --project . --out build/xtrem-core.d.ts --main @sage/xtrem-core/index > dts-generator.log", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "lint:fix": "eslint -c .eslintrc.js --fix --ext .ts lib test", "postbuild": "pnpm uglify:js && pnpm copy:fixtures", "posttest:ci": "rm -rf build/lib/pages && rm -rf build/lib/page-extensions && rm -rf build/lib/stickers", "pretest": "pnpm copy:fixtures", "pretest:ci": "pnpm pretest", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "test": "cross-env TZ=CET mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter-option maxDiffSize=100000", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-core.xml JUNIT_REPORT_NAME='xtrem-core' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "test:ci:allDatabases": "pnpm test:ci", "test:coverage": "c8 --reporter=html mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:debug": "mocha  --inspect-brk --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:dev": "cross-env TZ=CET mocha --recursive --exit", "version": "tsc -v", "xtrem-bytenode": "xtrem-bytenode"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}