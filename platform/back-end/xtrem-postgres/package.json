{"name": "@sage/xtrem-postgres", "description": "Xtrem PostgreSQL driver", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "README.md", "CHANGELOG.md"], "type": "commonjs", "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "lodash": "^4.17.21", "pg": "^8.11.4", "pg-copy-streams": "^6.0.6", "pg-cursor": "^2.10.4", "pg-listen": "^1.7.0", "pg-pool": "^3.6.2", "pg-protocol": "^1.6.1", "prom-client": "^15.1.2"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/pg": "^8.10.2", "@types/pg-copy-streams": "^1.2.2", "@types/pg-pool": "^2.0.2", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "prettier": "^3.3.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:fix": "eslint --fix -c .eslintrc.js --ext .ts lib test", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "test": "mocha --trace-warnings --recursive --exit \"build/test/**/*@(-|.)test.js\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-core.xml JUNIT_REPORT_NAME='xtrem-postgres' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "xtrem-bytenode": "xtrem-bytenode"}}