{"name": "@sage/xtrem~showcase-sales", "description": "Umbrella project for Xtrem development", "version": "54.0.58", "license": "UNLICENSED", "scripts": {"build": "cd .. && XTREM_SCOPES='platform|showcase-sales|[a-z0-9-]+/cli' pnpm build", "build:cache": "turbo run build", "build:modified": "cd .. && pnpm build:modified", "extract:demo:data": "cd main/showcase-sales-main && pnpm extract:demo:data", "extract:layer:data": "cd main/showcase-sales-main && pnpm extract:layer:data", "extract:qa:data": "cd main/showcase-sales-main && pnpm extract:qa:data", "extract:setup:data": "cd main/showcase-sales-main && pnpm extract:setup:data", "extract:test:data": "cd main/showcase-sales-main && pnpm extract:test:data", "lint": "cd .. && XTREM_SCOPES='showcase-sales' pnpm lint", "load:demo:data": "cd main/showcase-sales-main && pnpm load:demo:data", "load:layer:data": "cd main/showcase-sales-main && pnpm load:layer:data", "load:qa:data": "cd main/showcase-sales-main && pnpm load:qa:data", "load:setup:data": "cd main/showcase-sales-main && pnpm load:setup:data", "load:test:data": "cd main/showcase-sales-main && pnpm load:test:data", "postgres:clean": "docker rm xtrem_postgres || exit 0", "postgres:reset": "pnpm postgres:stop && pnpm postgres:clean && pnpm postgres:setup", "postgres:setup": "docker run -p 5432:5432 -e POSTGRES_PASSWORD=secret -d --restart unless-stopped --name xtrem_postgres postgres:$(cat ../.pgdbrc)-alpine -c max_locks_per_transaction=256", "postgres:stop": "docker stop xtrem_postgres || exit 0", "schema:reset": "cd main/showcase-sales-main && pnpm schema:reset", "schema:upgrade:test": "cd main/showcase-sales-main && pnpm schema:upgrade:test", "sqs:clean": "cd .. && pnpm sqs:clean", "sqs:reset": "cd .. && pnpm sqs:reset", "sqs:setup": "cd .. && pnpm sqs:setup", "sqs:stop": "cd .. && pnpm sqs:stop", "start": "cd main/showcase-sales-main && npm start", "test": "cd .. && XTREM_SCOPES='showcase-sales' pnpm test", "test:functional": "../scripts/lerna-cache/test-functional.sh", "test:functional:ci": "../scripts/lerna-cache/test-functional.sh :ci", "test:smoke:ci": "cd .. && XTREM_CI=1 XTREM_SCOPES='showcase-sales' scripts/lerna-cache/test-integration-smoke.sh"}}