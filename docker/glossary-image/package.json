{"name": "@sage/glossary", "version": "54.0.58", "buildStamp": "2025-06-19T11:12:00.580Z", "description": "", "main": "node_modules/@sage/xtrem-glossary/build/index.js", "scripts": {"xtrem": "xtrem", "manage": "xtrem manage", "schema": "xtrem schema"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-glossary": "^54.0.58", "@sage/xtrem-cli-cloud": "^54.0.58", "@sage/xtrem-cli-main": "^54.0.58"}, "pnpm": {"overrides": {"@sage/design-tokens": "4.35.0", "@types/react": "^18.3.3", "@wdio/globals": "8.12.1", "d3-color": "^3.1.0", "graphql": "16.1.0-experimental-stream-defer.6", "react-dom": "^18.3.1", "react": "^18.3.1", "sinon": "^19.0.0", "styled-components": "^5.3.11", "typescript": "~5.8.0", "webdriverio": "8.12.1", "webpack": "^5.95.0", "puppeteer-core>ws": "^8.17.1", "@cucumber/cucumber>semver": "^7.5.2", "newrelic": "12.10.0"}, "peerDependencyRules": {"allowAny": ["redux"], "ignoreMissing": ["ckeditor5"]}, "patchedDependencies": {"carbon-react@146.2.1": "patches/<EMAIL>", "react-grid-layout": "patches/<EMAIL>", "@types/react-grid-layout": "patches/@<EMAIL>", "@ag-grid-community/core": "patches/@ag-grid-community__core.patch"}}, "packageManager": "pnpm@9.15.9"}