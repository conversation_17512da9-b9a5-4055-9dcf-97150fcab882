// Configuration file for xdev cli tool
{
    // Used by release guard GitHub action
    // see: https://github.com/Sage-ERP-X3/huracan/actions/workflows/release-guard.yml
    releaseGuard: {
        pipelineId: 1286,
        toleranceDays: 3,
    },
    releng: {
        pack: {
            devDependencyNames: [
                '@types/chai',
                '@types/chai-as-promised',
                '@types/mocha',
                '@types/node',
                'c8',
                'chai',
                'chai-as-promised',
                'eslint-config-airbnb',
                'eslint-config-airbnb-typescript',
                'eslint-config-prettier',
                'eslint-plugin-import',
                'eslint-plugin-jsx-a11y',
                'eslint-plugin-mocha',
                'eslint-plugin-react',
                'eslint-plugin-react-hooks',
                'eslint-plugin-unicorn',
                'mocha',
                'thread-loader',
                'tsconfig-paths',
                'ts-loader',
                'ts-node',
                'typescript',
            ],
        },
    },
}
