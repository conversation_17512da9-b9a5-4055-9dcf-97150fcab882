{"name": "@sage/shopfloor-main", "description": "XTREM Shopfloor main", "version": "54.0.58", "xtrem": {"isMain": true, "locals": ["de-DE", "en-GB", "en-US", "es-ES", "fr-FR", "pt-PT"], "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": [], "author": "Sage", "license": "UNLICENSED", "files": ["build", "data", "sql", "routing.json"], "dependencies": {"@sage/shopfloor-dashboard": "workspace:*", "@sage/shopfloor-tracking": "workspace:*", "@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-cloud": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-mailer": "workspace:*", "@sage/xtrem-reporting": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-upload": "workspace:*"}, "devDependencies": {"@sage/xtrem-cli-bundle-dev": "workspace:*"}, "scripts": {"build": "xtrem compile --skip-client --skip-cop --skip-dts --skip-api-client --skip-clean", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "extract:demo:data": "xtrem layers --extract demo", "extract:layer:data": "xtrem layers --extract $XTREM_LAYER", "extract:qa:data": "xtrem layers --extract qa", "extract:setup:data": "xtrem layers --extract setup", "extract:test:data": "xtrem layers --extract test", "load:demo:data": "xtrem layers --load setup,demo", "load:layer:data": "xtrem layers --load $XTREM_LAYER", "load:perf:data": "xtrem layers --load setup,master-data,perf", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "manage": "xtrem manage", "pg-anonymizer": "xtrem pg-anonymizer", "schema": "xtrem schema", "schema:create": "xtrem schema --create", "schema:reset": "xtrem schema --reset", "schema:restore": "xtrem schema --restore-from-s3", "schema:upgrade": "xtrem upgrade --run --dev", "schema:upgrade:test": "pnpm xtrem -- upgrade --test", "sqs:clean": "cd ../../.. && pnpm sqs:clean", "sqs:reset": "cd ../../.. && pnpm sqs:reset", "sqs:setup": "cd ../../.. && pnpm sqs:setup", "sqs:stop": "cd ../../.. && pnpm sqs:stop", "start": "xtrem start", "start:unsecuredevlogin": "UNSECURE_DEV_LOGIN=1 xtrem start", "tenant": "xtrem tenant", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "xtrem": "xtrem"}}