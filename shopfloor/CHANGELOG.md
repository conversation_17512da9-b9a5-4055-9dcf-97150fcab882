# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [54.0.58](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.58) (2025-06-19)

### Bug Fixes

### Features


## [54.0.57](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.57) (2025-06-16)

### Bug Fixes

### Features


## [54.0.56](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.56) (2025-06-12)

### Bug Fixes

### Features


## [54.0.55](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.55) (2025-06-05)

### Bug Fixes

### Features


## [54.0.54](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.54) (2025-06-03)

### Bug Fixes

### Features


## [54.0.53](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.53) (2025-05-21)

### Bug Fixes

### Features


## [54.0.52](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.52) (2025-05-20)

### Bug Fixes
* XAPPSF-1664 query fetches records using endCursor   ([#25864](https://github.com/Sage-ERP-X3/xtrem/issues/25864))   ([98eb524](https://github.com/Sage-ERP-X3/xtrem/commit/98eb524a3fb2925f654f62c55aef2346379fdd8c))

### Features


## [54.0.51](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.51) (2025-05-16)

### Bug Fixes

### Features


## [54.0.50](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.50) (2025-05-15)

### Bug Fixes

### Features


## [54.0.49](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.49) (2025-05-14)

### Bug Fixes

### Features


## [54.0.48](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.48) (2025-05-14)

### Bug Fixes
* **shopfloor:** XAPPSF-1692 refresh table after processStartTimeTracking   ([#25769](https://github.com/Sage-ERP-X3/xtrem/issues/25769))   ([755bd4b](https://github.com/Sage-ERP-X3/xtrem/commit/755bd4ba949ba7b9b1b6b1d259387afc0597d497))

### Features


## [54.0.47](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.47) (2025-05-12)

### Bug Fixes

### Features


## [54.0.46](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.46) (2025-05-07)

### Bug Fixes

### Features


## [54.0.45](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.45) (2025-05-07)

### Bug Fixes

### Features


## [54.0.44](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.44) (2025-05-07)

### Bug Fixes
* **shopfloor:** XAPPSF-1657 use operation date   ([#25675](https://github.com/Sage-ERP-X3/xtrem/issues/25675))   ([f3f37f3](https://github.com/Sage-ERP-X3/xtrem/commit/f3f37f327a232a5865496120dcf64d1fb2fa20ff))

### Features


## [54.0.43](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.43) (2025-05-06)

### Bug Fixes

### Features


## [54.0.42](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.42) (2025-05-06)

### Bug Fixes

### Features


## [54.0.41](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.41) (2025-05-06)

### Bug Fixes

### Features


## [54.0.40](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.40) (2025-05-06)

### Bug Fixes
* **shopfloor:** XAPPSF-1647 fix name   ([#25654](https://github.com/Sage-ERP-X3/xtrem/issues/25654))   ([0ff12ed](https://github.com/Sage-ERP-X3/xtrem/commit/0ff12edf879e0958b9947d581552ad8c5ee2bdf1))
* **shopfloor:** XAPPSF-1578 set flag to 1   ([#25606](https://github.com/Sage-ERP-X3/xtrem/issues/25606))   ([9744e7f](https://github.com/Sage-ERP-X3/xtrem/commit/9744e7f0274b3c50a585347409c382a93b81456d))
* **shopfloor:** XAPPSF-1647 change name   ([#25644](https://github.com/Sage-ERP-X3/xtrem/issues/25644))   ([429d2bb](https://github.com/Sage-ERP-X3/xtrem/commit/429d2bbcff0a42177a934c3decf57425f7f7bcf4))
* **shopfloor:** XAPPSF-1647 add integration tag   ([#25637](https://github.com/Sage-ERP-X3/xtrem/issues/25637))   ([ef6cb88](https://github.com/Sage-ERP-X3/xtrem/commit/ef6cb887dd658777b6c20f6f0ede4dc25fccdc58))

### Features


## [54.0.39](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.39) (2025-05-02)

### Bug Fixes
* **shopfloor:** XAPPSF-1601-hotfix-v54-unable-to-track   ([#25588](https://github.com/Sage-ERP-X3/xtrem/issues/25588))   ([d4129b7](https://github.com/Sage-ERP-X3/xtrem/commit/d4129b7c19cfa2946633defe735454c038022e01))

### Features


## [54.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.38) (2025-04-29)

### Bug Fixes
* **shopfloor:** XAPPSF-1576 set sys state script   ([#25537](https://github.com/Sage-ERP-X3/xtrem/issues/25537))   ([35231d8](https://github.com/Sage-ERP-X3/xtrem/commit/35231d826c563003acda474ac46c6f3e2e09ea1b))

### Features


## [54.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.37) (2025-04-28)

### Bug Fixes

### Features
* **pipelines:** XT-88570 - update shopfloor pipelines - 54 ([XT-88570](https://jira.sage.com/browse/XT-88570)) ([#25461](https://github.com/Sage-ERP-X3/xtrem/issues/25461))   ([e8c9098](https://github.com/Sage-ERP-X3/xtrem/commit/e8c90981f4d75232e85cd8b30587d4408c3fced6))


## [54.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.36) (2025-04-24)

### Bug Fixes

### Features


## [54.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.35) (2025-04-18)

### Bug Fixes
* **shopfloor:** XAPPSF-1570 mapping set to inactive   ([#25363](https://github.com/Sage-ERP-X3/xtrem/issues/25363))   ([81d160d](https://github.com/Sage-ERP-X3/xtrem/commit/81d160de8fee40c99463ff7959834e718b444169))

### Features


## [54.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.34) (2025-04-17)

### Bug Fixes

### Features


## [54.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.33) (2025-04-16)

### Bug Fixes

### Features


## [54.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.32) (2025-04-15)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1464 display work order status   ([#25282](https://github.com/Sage-ERP-X3/xtrem/issues/25282))   ([0277ecc](https://github.com/Sage-ERP-X3/xtrem/commit/0277ecc3e399a6e9b26d345120bfa764aec69573))


## [54.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.31) (2025-04-14)

### Bug Fixes

### Features
* **hopfloor:** XAPPSF-1530 delete sync data and UOM   ([#25276](https://github.com/Sage-ERP-X3/xtrem/issues/25276))   ([31656c8](https://github.com/Sage-ERP-X3/xtrem/commit/31656c81e1669d9b2b0fb26482c23f685ea9703a))


## [54.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.30) (2025-04-13)

### Bug Fixes

### Features


## [54.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.29) (2025-04-12)

### Bug Fixes

### Features


## [54.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.28) (2025-04-11)

### Bug Fixes
* **shopfloor:** XAPPSF-1532 tracking sidebar looping when opened second time   ([#25265](https://github.com/Sage-ERP-X3/xtrem/issues/25265))   ([329aa15](https://github.com/Sage-ERP-X3/xtrem/commit/329aa15296fb0fdc0ba400e822450fc8382127a9))

### Features


## [54.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.27) (2025-04-10)

### Bug Fixes

### Features


## [54.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.26) (2025-04-09)

### Bug Fixes
* **shopfloor:** XAPPSF-1512 remove upgrade script example will now be…   ([#25227](https://github.com/Sage-ERP-X3/xtrem/issues/25227))   ([956b3d9](https://github.com/Sage-ERP-X3/xtrem/commit/956b3d9844c6bce3b881b54e176c8013f06afdcf))
* XT-999999 Commit generated files ([XT-999999](https://jira.sage.com/browse/XT-999999)) ([#25225](https://github.com/Sage-ERP-X3/xtrem/issues/25225))   ([f75bf89](https://github.com/Sage-ERP-X3/xtrem/commit/f75bf897fc4a2527cfc8c416867e702cf32ea10d))

### Features


## [54.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.25) (2025-04-08)

### Bug Fixes

### Features


## [54.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.24) (2025-04-07)

### Bug Fixes
* **shopfloor:** APPSF-1495 set isActive example upgrade scripts   ([#25101](https://github.com/Sage-ERP-X3/xtrem/issues/25101))   ([991ac95](https://github.com/Sage-ERP-X3/xtrem/commit/991ac95f65c1b59d9806d1d5624805ed100ba69e))
* **shopfloor:** add user-extension in Shopfloor to make billing role   ([#25004](https://github.com/Sage-ERP-X3/xtrem/issues/25004))   ([25bd5f0](https://github.com/Sage-ERP-X3/xtrem/commit/25bd5f05fb1d44f975aa3ef6983feea9965b8aca))

### Features


## [54.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.23) (2025-04-06)

### Bug Fixes

### Features


## [54.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.22) (2025-04-05)

### Bug Fixes

### Features


## [54.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.21) (2025-04-04)

### Bug Fixes

### Features
* **shopfloor:** APPSF-1447 Units of Time added to detail page   ([#25028](https://github.com/Sage-ERP-X3/xtrem/issues/25028))   ([164aff7](https://github.com/Sage-ERP-X3/xtrem/commit/164aff7a655276bc1c967f20d78d3adb8754e6bd))


## [54.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.20) (2025-04-03)

### Bug Fixes

### Features
* **print-engine:** report assignment - XT-90349 ([XT-90349](https://jira.sage.com/browse/XT-90349)) ([#24750](https://github.com/Sage-ERP-X3/xtrem/issues/24750))   ([12ab113](https://github.com/Sage-ERP-X3/xtrem/commit/12ab113cbf726cf19ef85d3e3b6f35282c7578d5))


## [54.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.19) (2025-04-02)

### Bug Fixes

### Features


## [54.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.18) (2025-04-02)

### Bug Fixes

### Features


## [54.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.17) (2025-04-01)

### Bug Fixes

### Features


## [54.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.16) (2025-03-31)

### Bug Fixes

### Features


## [54.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.15) (2025-03-31)

### Bug Fixes
* **shopfloor:** XAPPSF-1455 forced mapping file update   ([#24996](https://github.com/Sage-ERP-X3/xtrem/issues/24996))   ([aa863d3](https://github.com/Sage-ERP-X3/xtrem/commit/aa863d3570b5b17b3ead98be3ea14660dd101da8))

### Features


## [54.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.14) (2025-03-30)

### Bug Fixes

### Features


## [54.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.13) (2025-03-29)

### Bug Fixes

### Features


## [54.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.12) (2025-03-28)

### Bug Fixes
* **shopfloor:** XAPPSF-1395 resource status translated   ([#24973](https://github.com/Sage-ERP-X3/xtrem/issues/24973))   ([7a003b2](https://github.com/Sage-ERP-X3/xtrem/commit/7a003b250bdb529a73a2ee48b02b985a69a45eb8))

### Features
* **shopfloor:** XAPPSF-1353 add stop and start actions added to sidebar   ([#24818](https://github.com/Sage-ERP-X3/xtrem/issues/24818))   ([0a8acfc](https://github.com/Sage-ERP-X3/xtrem/commit/0a8acfcf207bf9af692ee422873e66e046282627))


## [54.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.11) (2025-03-27)

### Bug Fixes

### Features


## [54.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.10) (2025-03-27)

### Bug Fixes

### Features


## [54.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.9) (2025-03-27)

### Bug Fixes

### Features


## [54.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.8) (2025-03-26)

### Bug Fixes

### Features
* **notes-node:** XT-88048 - Implement the Note node ([XT-88048](https://jira.sage.com/browse/XT-88048)) ([#24674](https://github.com/Sage-ERP-X3/xtrem/issues/24674))   ([d2ffc07](https://github.com/Sage-ERP-X3/xtrem/commit/d2ffc07e0aac7e7cefdd4f5ecbae95986091069c))


## [54.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.7) (2025-03-25)

### Bug Fixes

### Features


## [54.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.6) (2025-03-24)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1385  display operation times in time format   ([#24793](https://github.com/Sage-ERP-X3/xtrem/issues/24793))   ([ab711e0](https://github.com/Sage-ERP-X3/xtrem/commit/ab711e045ee4cb74213a592983645a64ef28618e))
* **manufer:** XAPPSF-1018 Run time   ([#24862](https://github.com/Sage-ERP-X3/xtrem/issues/24862))   ([3e4cf04](https://github.com/Sage-ERP-X3/xtrem/commit/3e4cf048f12e42d673da94a9406a4da495cfc652))
* **shopfloor:** XAPPSF-1375 widget display work order name   ([#24839](https://github.com/Sage-ERP-X3/xtrem/issues/24839))   ([d22cafa](https://github.com/Sage-ERP-X3/xtrem/commit/d22cafaab184e83ab636aed6863d5525e6a48dab))


## [54.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.5) (2025-03-23)

### Bug Fixes

### Features


## [54.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.4) (2025-03-22)

### Bug Fixes

### Features


## [54.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.3) (2025-03-21)

### Bug Fixes
* **shopfloor:** XAPPSF-1424 added Portuguese locales   ([#24780](https://github.com/Sage-ERP-X3/xtrem/issues/24780))   ([8e68e7b](https://github.com/Sage-ERP-X3/xtrem/commit/8e68e7b4646af03444e8f82b69ead2c1f61dbf49))

### Features
* **shopfloor:** XAPPSF-1361 work order filters   ([#24830](https://github.com/Sage-ERP-X3/xtrem/issues/24830))   ([184b8bb](https://github.com/Sage-ERP-X3/xtrem/commit/184b8bb5a522e35f2d50e0997d1a4e02358c6803))


## [54.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.2) (2025-03-20)

### Bug Fixes

### Features


## [54.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.1) (2025-03-20)

### Bug Fixes

### Features


## [54.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@54.0.0) (2025-03-20)

### Bug Fixes

### Features


## [53.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.29) (2025-03-19)

### Bug Fixes

### Features
* **xtremSystem:** XT-89637 - shopfloor device token and operator ([XT-89637](https://jira.sage.com/browse/XT-89637)) ([#24658](https://github.com/Sage-ERP-X3/xtrem/issues/24658))   ([ab8b117](https://github.com/Sage-ERP-X3/xtrem/commit/ab8b1170419a9b5f71cfd4f2eb2696d358406d67))


## [53.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.28) (2025-03-18)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1398 comment out see all button code   ([#24721](https://github.com/Sage-ERP-X3/xtrem/issues/24721))   ([d96d503](https://github.com/Sage-ERP-X3/xtrem/commit/d96d503d759af32176272bbde08e8336b3b022f9))


## [53.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.27) (2025-03-17)

### Bug Fixes

### Features


## [53.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.26) (2025-03-17)

### Bug Fixes
* **mailer:** XT-90946 rename EmailReplyTo node with SenderEmailSetting ([XT-90946](https://jira.sage.com/browse/XT-90946)) ([#24662](https://github.com/Sage-ERP-X3/xtrem/issues/24662))   ([12c952e](https://github.com/Sage-ERP-X3/xtrem/commit/12c952e63440de8e9c44120b5e20b63b782a5b12))

### Features
* **shopfloor:** XAPPSF-1314 disable tracking by resource type   ([#24698](https://github.com/Sage-ERP-X3/xtrem/issues/24698))   ([f135639](https://github.com/Sage-ERP-X3/xtrem/commit/f135639a8fd55a94d7406142fdbfc58e45aa8c42))


## [53.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.25) (2025-03-16)

### Bug Fixes

### Features


## [53.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.24) (2025-03-15)

### Bug Fixes

### Features


## [53.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.23) (2025-03-14)

### Bug Fixes

### Features


## [53.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.22) (2025-03-13)

### Bug Fixes

### Features
* **xtrem-cli-atp:** nested grid filter XT-86987 ([XT-86987](https://jira.sage.com/browse/XT-86987)) ([#23797](https://github.com/Sage-ERP-X3/xtrem/issues/23797))   ([20bc939](https://github.com/Sage-ERP-X3/xtrem/commit/20bc939eb99412034046ced52bc2b87523c2b54c))
* **shopfloor:** XAPPSF-1331 extend see all page with new work order detail panel   ([#24651](https://github.com/Sage-ERP-X3/xtrem/issues/24651))   ([adc7eab](https://github.com/Sage-ERP-X3/xtrem/commit/adc7eab5f0e3f6ff30b79677a6809fecb7650620))
* XT-85377 Deletion of no-misued promises ([XT-85377](https://jira.sage.com/browse/XT-85377)) ([#24610](https://github.com/Sage-ERP-X3/xtrem/issues/24610))   ([54d5900](https://github.com/Sage-ERP-X3/xtrem/commit/54d590076205a64038a3bfeb2296e83f03848aa0))


## [53.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.21) (2025-03-13)

### Bug Fixes

### Features


## [53.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.20) (2025-03-12)

### Bug Fixes

### Features


## [53.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.19) (2025-03-12)

### Bug Fixes

### Features
* close editor after successful save XT-87814 ([XT-87814](https://jira.sage.com/browse/XT-87814)) ([#24493](https://github.com/Sage-ERP-X3/xtrem/issues/24493))   ([d54f636](https://github.com/Sage-ERP-X3/xtrem/commit/d54f6368b6513e09a588abd34b0faef5742426e4))


## [53.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.18) (2025-03-11)

### Bug Fixes
* **shopfloor:** XAPPSF-1346 made change to file to generate PR   ([#24595](https://github.com/Sage-ERP-X3/xtrem/issues/24595))   ([dff9558](https://github.com/Sage-ERP-X3/xtrem/commit/dff955872a15751d7ff4cfc0a33b8b4967ffc905))

### Features


## [53.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.17) (2025-03-10)

### Bug Fixes

### Features


## [53.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.16) (2025-03-09)

### Bug Fixes

### Features


## [53.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.15) (2025-03-08)

### Bug Fixes

### Features


## [53.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.14) (2025-03-07)

### Bug Fixes
* **deps:** update dependency typescript to v5.8.2   ([#24407](https://github.com/Sage-ERP-X3/xtrem/issues/24407))   ([ef61f2f](https://github.com/Sage-ERP-X3/xtrem/commit/ef61f2ff5202e48ebf85d90690bf9a1052afc70c))

### Features


## [53.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.13) (2025-03-06)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1354 remove widget from app   ([#24475](https://github.com/Sage-ERP-X3/xtrem/issues/24475))   ([1912df0](https://github.com/Sage-ERP-X3/xtrem/commit/1912df0965be5d99fbe2c3a53c5dc5c6c6e97b7d))


## [53.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.12) (2025-03-05)

### Bug Fixes

### Features


## [53.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.11) (2025-03-04)

### Bug Fixes

### Features


## [53.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.10) (2025-03-03)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-447 add to do, in progress, and done pages   ([#24413](https://github.com/Sage-ERP-X3/xtrem/issues/24413))   ([915dd9e](https://github.com/Sage-ERP-X3/xtrem/commit/915dd9ed9aaa462f5c4fa01db27df242c5c73438))


## [53.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.9) (2025-03-02)

### Bug Fixes

### Features


## [53.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.8) (2025-03-01)

### Bug Fixes

### Features


## [53.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.7) (2025-02-28)

### Bug Fixes

### Features
* XT-90111 workflow - Attachments in mails ([XT-90111](https://jira.sage.com/browse/XT-90111)) ([#24311](https://github.com/Sage-ERP-X3/xtrem/issues/24311))   ([6946c77](https://github.com/Sage-ERP-X3/xtrem/commit/6946c771048f89335d40a05924bc93633c84bfb3))


## [53.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.6) (2025-02-27)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1295 update X3 WO Operation enum mapping file   ([#24357](https://github.com/Sage-ERP-X3/xtrem/issues/24357))   ([e00fa44](https://github.com/Sage-ERP-X3/xtrem/commit/e00fa44b0687202966f3e5ba0abb7a0c2b1d8c72))
* **shopfloor:** XAPPSF-1271set operation to completed  sfc mutations   ([#24333](https://github.com/Sage-ERP-X3/xtrem/issues/24333))   ([5a41f12](https://github.com/Sage-ERP-X3/xtrem/commit/5a41f121ccece0b8bb3079441b2650ef2d544669))


## [53.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.5) (2025-02-26)

### Bug Fixes

### Features


## [53.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.4) (2025-02-25)

### Bug Fixes

### Features


## [53.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.3) (2025-02-24)

### Bug Fixes
* **shopfloor:** XAPPSF-1241 incorrect unit of time in SFC - SDMO   ([#24148](https://github.com/Sage-ERP-X3/xtrem/issues/24148))   ([25c0701](https://github.com/Sage-ERP-X3/xtrem/commit/25c0701cc7b2b706ca8cb325df49f64559306a8f))
* **shopfloor:** XAPPSF-1301 message text changed   ([#24223](https://github.com/Sage-ERP-X3/xtrem/issues/24223))   ([ae13dce](https://github.com/Sage-ERP-X3/xtrem/commit/ae13dce5c82a2e98a601351e63acff8952014751))

### Features


## [53.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.2) (2025-02-20)

### Bug Fixes

### Features


## [53.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.1) (2025-02-20)

### Bug Fixes

### Features


## [53.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@53.0.0) (2025-02-20)

### Bug Fixes

### Features


## [52.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.29) (2025-02-19)

### Bug Fixes
* **shopfloor:** XAPPSF-1298 fix tracking index   ([#24217](https://github.com/Sage-ERP-X3/xtrem/issues/24217))   ([9028dd6](https://github.com/Sage-ERP-X3/xtrem/commit/9028dd6f66e3824dae2b37abc280674c42cdf34d))

### Features


## [52.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.28) (2025-02-18)

### Bug Fixes

### Features


## [52.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.27) (2025-02-17)

### Bug Fixes

### Features
* change dashboard columns   ([#24094](https://github.com/Sage-ERP-X3/xtrem/issues/24094))   ([298b8d2](https://github.com/Sage-ERP-X3/xtrem/commit/298b8d269b6455732f3b9a0d94e77b0089543e90))


## [52.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.26) (2025-02-16)

### Bug Fixes

### Features


## [52.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.25) (2025-02-15)

### Bug Fixes

### Features


## [52.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.24) (2025-02-14)

### Bug Fixes

### Features
* **manufer:** XAPPSF-1017 Setup time   ([#24129](https://github.com/Sage-ERP-X3/xtrem/issues/24129))   ([ab8e869](https://github.com/Sage-ERP-X3/xtrem/commit/ab8e86962acbf7549f4ac0e60b3897509d6540bf))


## [52.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.23) (2025-02-12)

### Bug Fixes

### Features
* redos eslint plugin XT-89040 ([XT-89040](https://jira.sage.com/browse/XT-89040)) ([#24054](https://github.com/Sage-ERP-X3/xtrem/issues/24054))   ([6641e00](https://github.com/Sage-ERP-X3/xtrem/commit/6641e00faabfe33acf8a0f2d2b9fae49beb4b5f7))
* **shopfloor:** XAPPSF-507 completed operation SFC only   ([#24042](https://github.com/Sage-ERP-X3/xtrem/issues/24042))   ([8626687](https://github.com/Sage-ERP-X3/xtrem/commit/86266871cd5b9082477274ff788b1b4cbd1b85c8))


## [52.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.22) (2025-02-11)

### Bug Fixes

### Features


## [52.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.21) (2025-02-10)

### Bug Fixes

### Features
* **pipelines:** XT-88382- shopfloor sdmo pipelines ([XT-88382](https://jira.sage.com/browse/XT-88382)) ([#23968](https://github.com/Sage-ERP-X3/xtrem/issues/23968))   ([478957b](https://github.com/Sage-ERP-X3/xtrem/commit/478957b91cdb82bc6ba31cf85d45f0c7ff3317c8))


## [52.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.20) (2025-02-09)

### Bug Fixes

### Features


## [52.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.19) (2025-02-08)

### Bug Fixes

### Features


## [52.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.18) (2025-02-07)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1224 extend operationr status types and update filters   ([#23993](https://github.com/Sage-ERP-X3/xtrem/issues/23993))   ([122d751](https://github.com/Sage-ERP-X3/xtrem/commit/122d7513d7876d235e469b1637b02a341e27f2a1))


## [52.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.17) (2025-02-06)

### Bug Fixes
* **shopfloor:** XAPPSF-1198 site sync fails   ([#23825](https://github.com/Sage-ERP-X3/xtrem/issues/23825))   ([489ea68](https://github.com/Sage-ERP-X3/xtrem/commit/489ea68619f6cb321fa10ae856a561ba6ce7857d))

### Features
* **shopfloor:** XAPPSF-1153 operation status mapping   ([#23935](https://github.com/Sage-ERP-X3/xtrem/issues/23935))   ([1514a63](https://github.com/Sage-ERP-X3/xtrem/commit/1514a63597204e2806bcc935bc64171d85595266))


## [52.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.16) (2025-02-05)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1212 remove depreciatd tracking and operator …   ([#23930](https://github.com/Sage-ERP-X3/xtrem/issues/23930))   ([f5f462b](https://github.com/Sage-ERP-X3/xtrem/commit/f5f462b43dda0ee765a2488de5f2622eba5c767a))


## [52.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.15) (2025-02-04)

### Bug Fixes
* XT-88557 lower mocha version ([XT-88557](https://jira.sage.com/browse/XT-88557)) ([#23860](https://github.com/Sage-ERP-X3/xtrem/issues/23860))   ([e7a8c25](https://github.com/Sage-ERP-X3/xtrem/commit/e7a8c25a690ee560879f4299be1f70ad71cbc0b8))

### Features


## [52.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.14) (2025-02-03)

### Bug Fixes

### Features


## [52.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.13) (2025-02-01)

### Bug Fixes

### Features


## [52.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.12) (2025-01-31)

### Bug Fixes
* **routing:** publish routing.json files XT-88588 ([XT-88588](https://jira.sage.com/browse/XT-88588)) ([#23857](https://github.com/Sage-ERP-X3/xtrem/issues/23857))   ([ce8c03d](https://github.com/Sage-ERP-X3/xtrem/commit/ce8c03de13497073cc4231f6ffef21e54c9b726d))

### Features


## [52.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.11) (2025-01-30)

### Bug Fixes

### Features
* **xtrem-cli-atp:** xt-85680 integration testing capability to run graph ql queries and verify the response   ([#23598](https://github.com/Sage-ERP-X3/xtrem/issues/23598))   ([fbbf2e6](https://github.com/Sage-ERP-X3/xtrem/commit/fbbf2e6868cd04b96330ddaebcde9fdd33be7f8d))


## [52.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.10) (2025-01-29)

### Bug Fixes

### Features


## [52.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.9) (2025-01-29)

### Bug Fixes

### Features


## [52.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.8) (2025-01-28)

### Bug Fixes

### Features
* add responsive dashboards   ([#23612](https://github.com/Sage-ERP-X3/xtrem/issues/23612))   ([a0ef60b](https://github.com/Sage-ERP-X3/xtrem/commit/a0ef60b8bb09e86ef5fbc0f07aa94e37b41e6087))


## [52.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.7) (2025-01-28)

### Bug Fixes

### Features


## [52.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.6) (2025-01-27)

### Bug Fixes
* **shopfloor:** XAPPSF-1174 from resources widget record times displays errors   ([#23768](https://github.com/Sage-ERP-X3/xtrem/issues/23768))   ([d3155d2](https://github.com/Sage-ERP-X3/xtrem/commit/d3155d2eece07d4d584a971ea69698fc022e00e5))

### Features


## [52.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.5) (2025-01-26)

### Bug Fixes

### Features


## [52.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.4) (2025-01-25)

### Bug Fixes

### Features


## [52.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.3) (2025-01-24)

### Bug Fixes
* **xtrem-cli-atp:** XT-86960 navigation Panel-clear the search field ([XT-86960](https://jira.sage.com/browse/XT-86960)) ([#23624](https://github.com/Sage-ERP-X3/xtrem/issues/23624))   ([1199b5c](https://github.com/Sage-ERP-X3/xtrem/commit/1199b5cb5c89f5072e8127910e1583f70f12022d))
* **shopfloor:** XAPPSF-1171 display detail panel looping when operation started   ([#23684](https://github.com/Sage-ERP-X3/xtrem/issues/23684))   ([db8c082](https://github.com/Sage-ERP-X3/xtrem/commit/db8c08210b706f1fc2a586ac8f391669d8b3fb6d))

### Features
* **shopfloor:** XAPPSF-445-add-link-todays-work   ([#23660](https://github.com/Sage-ERP-X3/xtrem/issues/23660))   ([4d6be2e](https://github.com/Sage-ERP-X3/xtrem/commit/4d6be2ed9defcfdbfe37fd5acb0ed01a10127322))


## [52.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.2) (2025-01-23)

### Bug Fixes

### Features


## [52.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.1) (2025-01-23)

### Bug Fixes

### Features


## [52.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@52.0.0) (2025-01-23)

### Bug Fixes

### Features


## [51.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.35) (2025-01-22)

### Bug Fixes

### Features


## [51.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.34) (2025-01-21)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-444 todays work   ([#23640](https://github.com/Sage-ERP-X3/xtrem/issues/23640))   ([cf3236a](https://github.com/Sage-ERP-X3/xtrem/commit/cf3236a530a234ef968130690a847a89338c3b80))
* **shopfloor:** XAPPSF-986 sdmo shopfloor mutation   ([#23626](https://github.com/Sage-ERP-X3/xtrem/issues/23626))   ([66e7045](https://github.com/Sage-ERP-X3/xtrem/commit/66e70453ca0d8784033b615fdd6269f2dc44ec17))


## [51.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.33) (2025-01-20)

### Bug Fixes

### Features
* **mailer:** XT-87115 create EmailReplyTo node ([XT-87115](https://jira.sage.com/browse/XT-87115)) ([#23578](https://github.com/Sage-ERP-X3/xtrem/issues/23578))   ([531081d](https://github.com/Sage-ERP-X3/xtrem/commit/531081d564871b8091b8cfde29f3e1f8ecf1beff))


## [51.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.32) (2025-01-20)

### Bug Fixes

### Features


## [51.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.31) (2025-01-18)

### Bug Fixes

### Features


## [51.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.30) (2025-01-17)

### Bug Fixes
* XAPPSF-1109 sql reference using natural key   ([#23594](https://github.com/Sage-ERP-X3/xtrem/issues/23594))   ([51ba4a3](https://github.com/Sage-ERP-X3/xtrem/commit/51ba4a303d44e7a415d6efc61727f8f74b644f1b))

### Features
* **xtrem-cli-atp:** time period interaction XT-85535 ([XT-85535](https://jira.sage.com/browse/XT-85535)) ([#23563](https://github.com/Sage-ERP-X3/xtrem/issues/23563))   ([78c6dbb](https://github.com/Sage-ERP-X3/xtrem/commit/78c6dbb1c6aae05de2d9d373d229145457251dd4))


## [51.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.29) (2025-01-16)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-267 search operation   ([#23582](https://github.com/Sage-ERP-X3/xtrem/issues/23582))   ([4a3ed05](https://github.com/Sage-ERP-X3/xtrem/commit/4a3ed05049f26ccc0412bd67be0a7b778b8c27e8))


## [51.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.28) (2025-01-15)

### Bug Fixes
* **xtrem-cli-atp:** XT-86602 selection of a template dashboard ([XT-86602](https://jira.sage.com/browse/XT-86602)) ([#23407](https://github.com/Sage-ERP-X3/xtrem/issues/23407))   ([c359b1d](https://github.com/Sage-ERP-X3/xtrem/commit/c359b1dfb338012dfab3904a1a8dab0fd35d39c3))

### Features
* XT-87326 create tag node ([XT-87326](https://jira.sage.com/browse/XT-87326)) ([#23541](https://github.com/Sage-ERP-X3/xtrem/issues/23541))   ([b034dba](https://github.com/Sage-ERP-X3/xtrem/commit/b034dbafdbca782c452e73d2ea752466ad660442))
* **xtrem-cli-atp:** XT-85552_shopfloor-static-content-field ([XT-85552](https://jira.sage.com/browse/XT-85552)) ([#23306](https://github.com/Sage-ERP-X3/xtrem/issues/23306))   ([ce6c158](https://github.com/Sage-ERP-X3/xtrem/commit/ce6c158f0174d51c6916fdb0984b698834284c53))


## [51.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.27) (2025-01-14)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1119 map SDMO operator to labourResource node   ([#23530](https://github.com/Sage-ERP-X3/xtrem/issues/23530))   ([879f9b5](https://github.com/Sage-ERP-X3/xtrem/commit/879f9b5f6e85f2b4f6b08de75ef9906eb8c4dcb9))
* **shopfloor:** XAPPSF-266  Work Order Detail list display all or mine operations   ([#23505](https://github.com/Sage-ERP-X3/xtrem/issues/23505))   ([c6a6c4e](https://github.com/Sage-ERP-X3/xtrem/commit/c6a6c4e9e400b47b178d6fdfd79cd6243297d680))


## [51.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.26) (2025-01-13)

### Bug Fixes
* XT-87121 fix upgrade of uploaded files ([XT-87121](https://jira.sage.com/browse/XT-87121)) ([#23506](https://github.com/Sage-ERP-X3/xtrem/issues/23506))   ([e0da0d2](https://github.com/Sage-ERP-X3/xtrem/commit/e0da0d2ba83664269199b45bf987839a8837f11b))

### Features


## [51.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.25) (2025-01-12)

### Bug Fixes

### Features


## [51.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.24) (2025-01-11)

### Bug Fixes

### Features


## [51.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.23) (2025-01-10)

### Bug Fixes
* **interop:** XAPPSF-1029 update sys app node   ([#22850](https://github.com/Sage-ERP-X3/xtrem/issues/22850))   ([1f2ef7f](https://github.com/Sage-ERP-X3/xtrem/commit/1f2ef7f464e2ffca85bdae167e3b56cf9194ca14))

### Features


## [51.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.22) (2025-01-09)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-1093 filter out planned X3 work orders   ([#23441](https://github.com/Sage-ERP-X3/xtrem/issues/23441))   ([45a7dea](https://github.com/Sage-ERP-X3/xtrem/commit/45a7dea1edda7e6b303b21dce82d80b8f809a4d8))


## [51.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.21) (2025-01-08)

### Bug Fixes
* **deps:** update dependency mocha to v11   ([#23267](https://github.com/Sage-ERP-X3/xtrem/issues/23267))   ([aad520e](https://github.com/Sage-ERP-X3/xtrem/commit/aad520e4f1226e18c486fe256ea4730dffc4bd91))
* **shopfloor:** XAPPSF-1080 Do not display pending work orders from SDMO   ([#23411](https://github.com/Sage-ERP-X3/xtrem/issues/23411))   ([f2f5ede](https://github.com/Sage-ERP-X3/xtrem/commit/f2f5edea5edb14c102deb7dbaf17b447a0d6b0ef))
* **shopfloor/:** XAPPSF-1079 SDMO  work order end date incorrect   ([#23375](https://github.com/Sage-ERP-X3/xtrem/issues/23375))   ([2d3d416](https://github.com/Sage-ERP-X3/xtrem/commit/2d3d4162fcfcdbe080e48109eb730c0516823d7d))

### Features


## [51.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.20) (2025-01-07)

### Bug Fixes

### Features
* **apps:** cleanup xtrem cli deps XT-85855 ([XT-85855](https://jira.sage.com/browse/XT-85855)) ([#23141](https://github.com/Sage-ERP-X3/xtrem/issues/23141))   ([6c0cdeb](https://github.com/Sage-ERP-X3/xtrem/commit/6c0cdeb31ec2c8c41eacc8703c2d1c56fbb991a0))


## [51.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.19) (2025-01-06)

### Bug Fixes

### Features
* **xtrem-mailer:** XT-84454 making onboarding welcome message multi-app capable ([XT-84454](https://jira.sage.com/browse/XT-84454)) ([#23280](https://github.com/Sage-ERP-X3/xtrem/issues/23280))   ([b5d60d5](https://github.com/Sage-ERP-X3/xtrem/commit/b5d60d521ac95f687b103e596525d510e85c243c))


## [51.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.18) (2025-01-05)

### Bug Fixes

### Features


## [51.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.17) (2025-01-04)

### Bug Fixes

### Features


## [51.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.16) (2025-01-03)

### Bug Fixes

### Features
* **shopfloor-atp:** XT-85538 - Add capability to control time is adding up ([XT-85538](https://jira.sage.com/browse/XT-85538)) ([#23283](https://github.com/Sage-ERP-X3/xtrem/issues/23283))   ([cd62db7](https://github.com/Sage-ERP-X3/xtrem/commit/cd62db7f55bb4f100157dee49f71ab0a2816ae76))
* **dashboard:** Dashboard as setup node XT-85553 ([XT-85553](https://jira.sage.com/browse/XT-85553)) ([#22999](https://github.com/Sage-ERP-X3/xtrem/issues/22999))   ([3405d4d](https://github.com/Sage-ERP-X3/xtrem/commit/3405d4dfe410afac65f748b49febd45740628fa7))


## [51.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.15) (2025-01-02)

### Bug Fixes

### Features


## [51.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.14) (2025-01-01)

### Bug Fixes

### Features


## [51.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.13) (2024-12-31)

### Bug Fixes

### Features


## [51.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.12) (2024-12-29)

### Bug Fixes

### Features


## [51.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.11) (2024-12-28)

### Bug Fixes

### Features


## [51.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.10) (2024-12-27)

### Bug Fixes

### Features


## [51.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.9) (2024-12-26)

### Bug Fixes

### Features


## [51.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.8) (2024-12-25)

### Bug Fixes

### Features


## [51.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.7) (2024-12-24)

### Bug Fixes

### Features


## [51.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.6) (2024-12-23)

### Bug Fixes

### Features


## [51.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.5) (2024-12-22)

### Bug Fixes

### Features


## [51.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.4) (2024-12-21)

### Bug Fixes

### Features


## [51.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.3) (2024-12-20)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-985 update mapping   ([#23243](https://github.com/Sage-ERP-X3/xtrem/issues/23243))   ([6c3c0d9](https://github.com/Sage-ERP-X3/xtrem/commit/6c3c0d921aa105fdf0f4bf9aa764569ae39b0a99))
* **xtrem-cli-dev:** XT-85780 widget size ([XT-85780](https://jira.sage.com/browse/XT-85780)) ([#23174](https://github.com/Sage-ERP-X3/xtrem/issues/23174))   ([d1ff129](https://github.com/Sage-ERP-X3/xtrem/commit/d1ff129f62cb6d513af88bfe4cb485b287dffb70))


## [51.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.2) (2024-12-19)

### Bug Fixes
* **shopfloor:** XAPPSF-1061 remove depreicated property   ([#23188](https://github.com/Sage-ERP-X3/xtrem/issues/23188))   ([321cfa1](https://github.com/Sage-ERP-X3/xtrem/commit/321cfa17e3821fea4c4fd881708440cb3e38da8c))

### Features
* **manufer:** XAPPSF-1016 Clock in/Clock out + added resource widget to prerequisite test   ([#23164](https://github.com/Sage-ERP-X3/xtrem/issues/23164))   ([463e22b](https://github.com/Sage-ERP-X3/xtrem/commit/463e22b4dd974cd8e486fa773d43bc02609104d5))


## [51.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.1) (2024-12-19)

### Bug Fixes

### Features


## [51.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@51.0.0) (2024-12-19)

### Bug Fixes

### Features


## [50.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.30) (2024-12-18)

### Bug Fixes

### Features
* XT-83511 Enable more entities in workflow ([XT-83511](https://jira.sage.com/browse/XT-83511)) ([#23187](https://github.com/Sage-ERP-X3/xtrem/issues/23187))   ([a71ff7a](https://github.com/Sage-ERP-X3/xtrem/commit/a71ff7afb373f7c8b7d2b03b1db52f4003e7f528))


## [50.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.29) (2024-12-17)

### Bug Fixes

### Features


## [50.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.28) (2024-12-16)

### Bug Fixes

### Features


## [50.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.27) (2024-12-15)

### Bug Fixes

### Features


## [50.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.26) (2024-12-14)

### Bug Fixes

### Features


## [50.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.25) (2024-12-13)

### Bug Fixes

### Features


## [50.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.24) (2024-12-12)

### Bug Fixes

### Features


## [50.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.23) (2024-12-11)

### Bug Fixes

### Features


## [50.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.22) (2024-12-10)

### Bug Fixes

### Features


## [50.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.21) (2024-12-09)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-853 validate SDMO internal mapping definitions   ([#22943](https://github.com/Sage-ERP-X3/xtrem/issues/22943))   ([03694f1](https://github.com/Sage-ERP-X3/xtrem/commit/03694f11cd0a2b131c8c1c1e63cf6d47b47a8e5f))


## [50.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.20) (2024-12-08)

### Bug Fixes

### Features


## [50.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.19) (2024-12-07)

### Bug Fixes

### Features


## [50.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.18) (2024-12-06)

### Bug Fixes

### Features


## [50.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.17) (2024-12-05)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-505-display-instructions   ([#22838](https://github.com/Sage-ERP-X3/xtrem/issues/22838))   ([032a38e](https://github.com/Sage-ERP-X3/xtrem/commit/032a38e83c24a4f20f6bf0aa71c262c7c976985f))


## [50.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.16) (2024-12-04)

### Bug Fixes

### Features
* **shopfloor:** XT-79856 - add shopfloor prerequisites tests package ([XT-79856](https://jira.sage.com/browse/XT-79856)) ([#22820](https://github.com/Sage-ERP-X3/xtrem/issues/22820))   ([e398e75](https://github.com/Sage-ERP-X3/xtrem/commit/e398e75303ece003cb12fb8304c7e252c112e607))


## [50.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.15) (2024-12-03)

### Bug Fixes
* **shopfloor:** XAPPSF-983 operations missing   ([#22799](https://github.com/Sage-ERP-X3/xtrem/issues/22799))   ([b28e8cf](https://github.com/Sage-ERP-X3/xtrem/commit/b28e8cfd04c9480eb4745e1aa3c62d827376a34e))

### Features
* XT-84069 Merge UploadedFile and Attachment nodes ([XT-84069](https://jira.sage.com/browse/XT-84069)) ([#22586](https://github.com/Sage-ERP-X3/xtrem/issues/22586))   ([96b4a23](https://github.com/Sage-ERP-X3/xtrem/commit/96b4a235ca3ab67015f65c89726341f1e792bc7a))


## [50.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.14) (2024-12-02)

### Bug Fixes

### Features


## [50.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.13) (2024-12-01)

### Bug Fixes

### Features


## [50.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.12) (2024-11-30)

### Bug Fixes

### Features


## [50.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.11) (2024-11-29)

### Bug Fixes

### Features


## [50.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.10) (2024-11-29)

### Bug Fixes

### Features


## [50.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.9) (2024-11-28)

### Bug Fixes

### Features


## [50.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.8) (2024-11-27)

### Bug Fixes

### Features


## [50.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.7) (2024-11-26)

### Bug Fixes
* **shopfloor:** XAPPSF-961 mapped data not translated into browser language   ([#22545](https://github.com/Sage-ERP-X3/xtrem/issues/22545))   ([0953b73](https://github.com/Sage-ERP-X3/xtrem/commit/0953b73d4bfcd98126545dbe51411c72c72f6dc4))

### Features


## [50.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.6) (2024-11-26)

### Bug Fixes

### Features


## [50.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.5) (2024-11-25)

### Bug Fixes

### Features
* **shopfloor:** XT-81888-  shopfloor functional tests ([XT-81888](https://jira.sage.com/browse/XT-81888)) ([#22235](https://github.com/Sage-ERP-X3/xtrem/issues/22235))   ([a824c39](https://github.com/Sage-ERP-X3/xtrem/commit/a824c39ce77885531ceea26ae6cb8753dc4c8e32))


## [50.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.4) (2024-11-24)

### Bug Fixes

### Features


## [50.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.3) (2024-11-23)

### Bug Fixes

### Features


## [50.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.2) (2024-11-22)

### Bug Fixes

### Features


## [50.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.1) (2024-11-22)

### Bug Fixes

### Features


## [50.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@50.0.0) (2024-11-22)

### Bug Fixes

### Features


## [49.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.30) (2024-11-22)

### Bug Fixes

### Features


## [49.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.29) (2024-11-21)

### Bug Fixes

### Features


## [49.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.28) (2024-11-20)

### Bug Fixes

### Features


## [49.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.27) (2024-11-19)

### Bug Fixes

### Features
* XT-82766 Data mapping page enhancement ([XT-82766](https://jira.sage.com/browse/XT-82766)) ([#22440](https://github.com/Sage-ERP-X3/xtrem/issues/22440))   ([42bb01d](https://github.com/Sage-ERP-X3/xtrem/commit/42bb01dc3f877d77041d87ba838e3f7e6d4b65ea))
* **shopfloor:** XAPPSF-914-Display-instructions-node-mappings-part2   ([#22160](https://github.com/Sage-ERP-X3/xtrem/issues/22160))   ([f4b5fbd](https://github.com/Sage-ERP-X3/xtrem/commit/f4b5fbda05226079b5a62a1892be7aa352160708))


## [49.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.26) (2024-11-18)

### Bug Fixes

### Features


## [49.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.25) (2024-11-17)

### Bug Fixes

### Features


## [49.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.24) (2024-11-16)

### Bug Fixes

### Features


## [49.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.23) (2024-11-15)

### Bug Fixes

### Features
* **shopfloor:** XT-82510 fix mapping ids ([XT-82510](https://jira.sage.com/browse/XT-82510)) ([#22391](https://github.com/Sage-ERP-X3/xtrem/issues/22391))   ([eb1651c](https://github.com/Sage-ERP-X3/xtrem/commit/eb1651cc4eb7628428fa2d81e362e80068fa6178))


## [49.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.22) (2024-11-14)

### Bug Fixes
* **shopfloor:** XAPPSF-952 incorrect unit of time displayed on operation   ([#22357](https://github.com/Sage-ERP-X3/xtrem/issues/22357))   ([cae44cf](https://github.com/Sage-ERP-X3/xtrem/commit/cae44cf316aa15ce6bf6ab07b96bde6837332033))

### Features


## [49.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.21) (2024-11-13)

### Bug Fixes

### Features


## [49.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.20) (2024-11-12)

### Bug Fixes
* **shopfloor:** XAPPSF-959 fix currupted test data   ([#22294](https://github.com/Sage-ERP-X3/xtrem/issues/22294))   ([76cd405](https://github.com/Sage-ERP-X3/xtrem/commit/76cd405841c618d1ae4bfb21e685ee3671f8286e))

### Features


## [49.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.19) (2024-11-11)

### Bug Fixes

### Features


## [49.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.18) (2024-11-10)

### Bug Fixes

### Features


## [49.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.17) (2024-11-09)

### Bug Fixes

### Features


## [49.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.16) (2024-11-08)

### Bug Fixes

### Features


## [49.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.15) (2024-11-07)

### Bug Fixes

### Features


## [49.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.14) (2024-11-06)

### Bug Fixes

### Features


## [49.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.13) (2024-11-05)

### Bug Fixes
* **shopfloor:** XAPPSF-947 fix dates passed to page   ([#22162](https://github.com/Sage-ERP-X3/xtrem/issues/22162))   ([4bc8913](https://github.com/Sage-ERP-X3/xtrem/commit/4bc8913b99fa0fe9aebd0654de27e8775a39701a))

### Features


## [49.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.12) (2024-11-04)

### Bug Fixes

### Features
* **shopfloor:** XT-82510 update version number ([XT-82510](https://jira.sage.com/browse/XT-82510)) ([#22127](https://github.com/Sage-ERP-X3/xtrem/issues/22127))   ([9fcd498](https://github.com/Sage-ERP-X3/xtrem/commit/9fcd498579cce9b5f509b08148f5a2eab8949944))


## [49.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.11) (2024-11-03)

### Bug Fixes

### Features


## [49.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.10) (2024-11-02)

### Bug Fixes

### Features


## [49.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.9) (2024-11-01)

### Bug Fixes

### Features


## [49.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.8) (2024-10-31)

### Bug Fixes
* **shopfloor:** XT-82323 fix date filter ([XT-82323](https://jira.sage.com/browse/XT-82323)) ([#22030](https://github.com/Sage-ERP-X3/xtrem/issues/22030))   ([341ae17](https://github.com/Sage-ERP-X3/xtrem/commit/341ae17132a23a263a1fde043d2fb0d39ae8641b))

### Features


## [49.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.7) (2024-10-30)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-915 add shopfloor dashboard manager   ([#21922](https://github.com/Sage-ERP-X3/xtrem/issues/21922))   ([47fb21d](https://github.com/Sage-ERP-X3/xtrem/commit/47fb21d1c4b13ca15f71f8ae554fe48f853a05d5))
* **shopfloor:** XAPPSF-896 tracking pages common functions   ([#21977](https://github.com/Sage-ERP-X3/xtrem/issues/21977))   ([9f3ea7f](https://github.com/Sage-ERP-X3/xtrem/commit/9f3ea7f404aa6d2ec2796f6864c64f3e6b10d61b))


## [49.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.6) (2024-10-29)

### Bug Fixes

### Features
* XT-82152 add log messages to async mutation tracking query ([XT-82152](https://jira.sage.com/browse/XT-82152)) ([#21938](https://github.com/Sage-ERP-X3/xtrem/issues/21938))   ([2ddeabb](https://github.com/Sage-ERP-X3/xtrem/commit/2ddeabbc9609cbdbc9833b43e02440d3993e309e))


## [49.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.5) (2024-10-28)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-499 add resource status to sidebars   ([#21919](https://github.com/Sage-ERP-X3/xtrem/issues/21919))   ([08351c5](https://github.com/Sage-ERP-X3/xtrem/commit/08351c5c042f4ac8eea155f2faf9c9fbc3a97be7))


## [49.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.4) (2024-10-27)

### Bug Fixes

### Features


## [49.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.3) (2024-10-26)

### Bug Fixes

### Features
* **scheduler:** XT-81386 add full sync job ([XT-81386](https://jira.sage.com/browse/XT-81386)) ([#21843](https://github.com/Sage-ERP-X3/xtrem/issues/21843))   ([fb0beab](https://github.com/Sage-ERP-X3/xtrem/commit/fb0beab8d4eb36014d6fdea6f5ff84eeab2a6dd8))


## [49.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.2) (2024-10-24)

### Bug Fixes
* **shopfloor:** XT-81683 refactor op resource ([XT-81683](https://jira.sage.com/browse/XT-81683)) ([#21867](https://github.com/Sage-ERP-X3/xtrem/issues/21867))   ([67346b0](https://github.com/Sage-ERP-X3/xtrem/commit/67346b06dab7fbeaef7aa4fb8bd80bce2d80df84))

### Features


## [49.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.1) (2024-10-24)

### Bug Fixes

### Features


## [49.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@49.0.0) (2024-10-24)

### Bug Fixes

### Features


## [48.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.37) (2024-10-23)

### Bug Fixes
* XT-81683 id filters ([XT-81683](https://jira.sage.com/browse/XT-81683)) ([#21826](https://github.com/Sage-ERP-X3/xtrem/issues/21826))   ([8990344](https://github.com/Sage-ERP-X3/xtrem/commit/89903449a19f0a1aa9ebb222bf3dce504ed53e58))

### Features
* support for table widget row actions to the platform XT-73450 ([XT-73450](https://jira.sage.com/browse/XT-73450)) ([#21846](https://github.com/Sage-ERP-X3/xtrem/issues/21846))   ([feb9f4e](https://github.com/Sage-ERP-X3/xtrem/commit/feb9f4e9ad5594a7ec2f769ee0e8d10f372ee533))


## [48.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.36) (2024-10-22)

### Bug Fixes

### Features
* **interop:** XT-80900 add sync button ([XT-80900](https://jira.sage.com/browse/XT-80900)) ([#21822](https://github.com/Sage-ERP-X3/xtrem/issues/21822))   ([b4a89bc](https://github.com/Sage-ERP-X3/xtrem/commit/b4a89bcf49aa30eec6f9ad0c5ac830b9b8dd6e26))
* **shoppfloor:** XAPPSF-905 add time tracking page   ([#21818](https://github.com/Sage-ERP-X3/xtrem/issues/21818))   ([d3585c8](https://github.com/Sage-ERP-X3/xtrem/commit/d3585c8db606fe5c6b4d951beddc7df0148b7569))
* **interop:** XT-79074 add vendor ([XT-79074](https://jira.sage.com/browse/XT-79074)) ([#21807](https://github.com/Sage-ERP-X3/xtrem/issues/21807))   ([6cb8ddd](https://github.com/Sage-ERP-X3/xtrem/commit/6cb8ddd3dcfc6466d8a72d8d0c7f8f7d6f418f3d))


## [48.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.35) (2024-10-21)

### Bug Fixes
* **shopfloor:** XAPPSF-894 fix tracking counter reduce calls to isOperatorId   ([#21793](https://github.com/Sage-ERP-X3/xtrem/issues/21793))   ([9dd5f5c](https://github.com/Sage-ERP-X3/xtrem/commit/9dd5f5cc7271f04050e55a1660a14aae0c5c241a))

### Features
* **shopfloor:** XAPPSF-880 update resource widget   ([#21747](https://github.com/Sage-ERP-X3/xtrem/issues/21747))   ([dd30b26](https://github.com/Sage-ERP-X3/xtrem/commit/dd30b26853da9a23d3ca5172b50358a06c14fc87))
* XT-81377 SysNodeTransformation refactoring ([XT-81377](https://jira.sage.com/browse/XT-81377)) ([#21775](https://github.com/Sage-ERP-X3/xtrem/issues/21775))   ([eebb5e7](https://github.com/Sage-ERP-X3/xtrem/commit/eebb5e7972d00accd265626037f15c26ab498723))


## [48.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.34) (2024-10-20)

### Bug Fixes

### Features


## [48.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.33) (2024-10-19)

### Bug Fixes

### Features


## [48.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.32) (2024-10-18)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-842 tracking resource page and format start end date   ([#21761](https://github.com/Sage-ERP-X3/xtrem/issues/21761))   ([2d45390](https://github.com/Sage-ERP-X3/xtrem/commit/2d45390fe23622aa07f42410cb4037416d5ce86e))


## [48.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.31) (2024-10-17)

### Bug Fixes

### Features
* XT-81377 compute SysApp version ([XT-81377](https://jira.sage.com/browse/XT-81377)) ([#21745](https://github.com/Sage-ERP-X3/xtrem/issues/21745))   ([bf5ffe1](https://github.com/Sage-ERP-X3/xtrem/commit/bf5ffe1b042ac3a8004765c798d88bf879835d5f))
* **shopfloor:** XAPPSF-802-Filter-shop-floor-X3-import-data-site-isManufacturing   ([#21723](https://github.com/Sage-ERP-X3/xtrem/issues/21723))   ([8f4ebfb](https://github.com/Sage-ERP-X3/xtrem/commit/8f4ebfb9b2199674aff28786d96651472f7aeb2f))
* **shopfloor:** XAPPSF-842 common functions refactor   ([#21729](https://github.com/Sage-ERP-X3/xtrem/issues/21729))   ([3821ce4](https://github.com/Sage-ERP-X3/xtrem/commit/3821ce46f4a76cb48a280aa16fe5b21f123f9adb))


## [48.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.30) (2024-10-16)

### Bug Fixes
* **shopfloor:** XT-81327 enum mappings ([XT-81327](https://jira.sage.com/browse/XT-81327)) ([#21707](https://github.com/Sage-ERP-X3/xtrem/issues/21707))   ([0232542](https://github.com/Sage-ERP-X3/xtrem/commit/02325421dcece17992e558b5cb99cd8900eb39f5))

### Features
* **shopfloor:** XAPPSF-804 clock-out on resource and work order widget.   ([#21716](https://github.com/Sage-ERP-X3/xtrem/issues/21716))   ([1e482c0](https://github.com/Sage-ERP-X3/xtrem/commit/1e482c082c54f1d3a1362c516d94e202ca1c7f7a))
* **shopfloor:** XAPPSF-825 add operator side bar and move functions to common location   ([#21687](https://github.com/Sage-ERP-X3/xtrem/issues/21687))   ([5399140](https://github.com/Sage-ERP-X3/xtrem/commit/53991402326a0c82166a8b344b5971472d605bf5))


## [48.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.29) (2024-10-15)

### Bug Fixes
* XT-81242 manually filter and order by computed mapped properties only ([XT-81242](https://jira.sage.com/browse/XT-81242)) ([#21670](https://github.com/Sage-ERP-X3/xtrem/issues/21670))   ([c157755](https://github.com/Sage-ERP-X3/xtrem/commit/c15775557fd5c9e57a3e2bf51c4fe8353721580d))

### Features
* **shopfloor:** XT-81240 cache local nodes ([XT-81240](https://jira.sage.com/browse/XT-81240)) ([#21686](https://github.com/Sage-ERP-X3/xtrem/issues/21686))   ([cb46edc](https://github.com/Sage-ERP-X3/xtrem/commit/cb46edc80640a607cf79152002a89dc8815a04a2))
* **shopfloor:** XT-81240 cache local nodes ([XT-81240](https://jira.sage.com/browse/XT-81240)) ([#21684](https://github.com/Sage-ERP-X3/xtrem/issues/21684))   ([1961682](https://github.com/Sage-ERP-X3/xtrem/commit/1961682ff7637cbf74bdd8fa32e01975d41cf458))
* **shopfloor:** XAPPSF-498 Part 2   ([#21678](https://github.com/Sage-ERP-X3/xtrem/issues/21678))   ([4c084a5](https://github.com/Sage-ERP-X3/xtrem/commit/4c084a52718bc36d2bbec495a2bb5666b25d98ca))
* **purchasing:** XT-81090 fix widgets ([XT-81090](https://jira.sage.com/browse/XT-81090)) ([#21655](https://github.com/Sage-ERP-X3/xtrem/issues/21655))   ([8d51725](https://github.com/Sage-ERP-X3/xtrem/commit/8d51725f43efd1e3a2bb37c3ac029ab3d4949138))


## [48.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.28) (2024-10-14)

### Bug Fixes

### Features
* add contact card widget XT-73229 ([XT-73229](https://jira.sage.com/browse/XT-73229)) ([#21553](https://github.com/Sage-ERP-X3/xtrem/issues/21553))   ([c8f6113](https://github.com/Sage-ERP-X3/xtrem/commit/c8f6113a873429f7c5c10559da564409300d7a33))
* **shopfloor:** XAPPSF-700 resource node spike   ([#21460](https://github.com/Sage-ERP-X3/xtrem/issues/21460))   ([8c4416b](https://github.com/Sage-ERP-X3/xtrem/commit/8c4416b6bc179cb37e1c9b2cfd03beb45ec4afb0))


## [48.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.27) (2024-10-13)

### Bug Fixes

### Features


## [48.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.26) (2024-10-12)

### Bug Fixes

### Features


## [48.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.25) (2024-10-11)

### Bug Fixes
* **shopfloor:** XAPPSF-786 resource value for queries corrected.   ([#21611](https://github.com/Sage-ERP-X3/xtrem/issues/21611))   ([c16afe0](https://github.com/Sage-ERP-X3/xtrem/commit/c16afe07c1b1377648f29d1c45190504dc173a8d))

### Features
* **shopfloor:** XAPPSF-805 remove redundant menu options   ([#21593](https://github.com/Sage-ERP-X3/xtrem/issues/21593))   ([ad44fa6](https://github.com/Sage-ERP-X3/xtrem/commit/ad44fa6b4fac9905725a2a088b8ff1ab7cfe0dc2))


## [48.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.24) (2024-10-10)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-802-XAPPSF-802-Filter-shop-floor-import-data-new-status_draft   ([#21596](https://github.com/Sage-ERP-X3/xtrem/issues/21596))   ([78ec959](https://github.com/Sage-ERP-X3/xtrem/commit/78ec95958c33b1e32635696030086b0a626162cd))
* **shopfloor:** XAPPSF-498 add estimated time side bar   ([#21547](https://github.com/Sage-ERP-X3/xtrem/issues/21547))   ([b16d99b](https://github.com/Sage-ERP-X3/xtrem/commit/b16d99b0202a4dc5d73445a9261dc97b59a7db18))
* **shopfloor:** XAPPSF-669-Operation-done-fix-decimal-places-completed-rejected-quantities   ([#21570](https://github.com/Sage-ERP-X3/xtrem/issues/21570))   ([2670f16](https://github.com/Sage-ERP-X3/xtrem/commit/2670f16296f753e5889f9a5a45a9efbd7dc85e0a))
* **interop:** XT-80918 fix duplicate ([XT-80918](https://jira.sage.com/browse/XT-80918)) ([#21564](https://github.com/Sage-ERP-X3/xtrem/issues/21564))   ([4b0af82](https://github.com/Sage-ERP-X3/xtrem/commit/4b0af829d83d6442c8b7ced09749956e2b7d33b5))
* **shopfloor:** XAPPSF-786 Synced paused tracking detail   ([#21555](https://github.com/Sage-ERP-X3/xtrem/issues/21555))   ([59365be](https://github.com/Sage-ERP-X3/xtrem/commit/59365be153ad03d24aac3137203bee347ae92185))


## [48.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.23) (2024-10-09)

### Bug Fixes

### Features


## [48.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.22) (2024-10-08)

### Bug Fixes

### Features


## [48.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.21) (2024-10-07)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-737-different-operator   ([#21413](https://github.com/Sage-ERP-X3/xtrem/issues/21413))   ([8c761e8](https://github.com/Sage-ERP-X3/xtrem/commit/8c761e835ae6bff5e5f997df717d85e1ee7e7072))


## [48.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.20) (2024-10-06)

### Bug Fixes

### Features


## [48.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.19) (2024-10-05)

### Bug Fixes

### Features


## [48.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.18) (2024-10-04)

### Bug Fixes

### Features


## [48.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.17) (2024-10-03)

### Bug Fixes

### Features


## [48.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.16) (2024-10-02)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-497 detail panel operation details   ([#21404](https://github.com/Sage-ERP-X3/xtrem/issues/21404))   ([96a3157](https://github.com/Sage-ERP-X3/xtrem/commit/96a31570ed8cc785ffd1245c687c0ac0d46d0f41))


## [48.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.15) (2024-10-01)

### Bug Fixes
* XT-79845 fix parsing of order by in array utils ([XT-79845](https://jira.sage.com/browse/XT-79845)) ([#21355](https://github.com/Sage-ERP-X3/xtrem/issues/21355))   ([c5bbdc7](https://github.com/Sage-ERP-X3/xtrem/commit/c5bbdc713c994420b074f8792c947b112f6c5ca6))

### Features
* **shopfloor:** XAPPSF-496 add work order operation detail panel   ([#21370](https://github.com/Sage-ERP-X3/xtrem/issues/21370))   ([25d3894](https://github.com/Sage-ERP-X3/xtrem/commit/25d3894f31b5ed410cee82461d31a0d47f2f2511))


## [48.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.14) (2024-09-30)

### Bug Fixes

### Features


## [48.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.13) (2024-09-29)

### Bug Fixes

### Features


## [48.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.12) (2024-09-29)

### Bug Fixes

### Features


## [48.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.11) (2024-09-28)

### Bug Fixes

### Features


## [48.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.10) (2024-09-27)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-725 clock out warning if incomplete events exist   ([#21292](https://github.com/Sage-ERP-X3/xtrem/issues/21292))   ([f576389](https://github.com/Sage-ERP-X3/xtrem/commit/f576389461f421df4c7d605851f8dd087feb8759))


## [48.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.9) (2024-09-26)

### Bug Fixes

### Features
* **shopfloor:** XT-79043 test mutations ([XT-79043](https://jira.sage.com/browse/XT-79043)) ([#21163](https://github.com/Sage-ERP-X3/xtrem/issues/21163))   ([0239c6d](https://github.com/Sage-ERP-X3/xtrem/commit/0239c6d660a0340bc6386f5e9561646771a6db45))


## [48.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.8) (2024-09-25)

### Bug Fixes
* XT-78516 alias Operation in generation of api.d.ts ([XT-78516](https://jira.sage.com/browse/XT-78516)) ([#21185](https://github.com/Sage-ERP-X3/xtrem/issues/21185))   ([f0a7ac2](https://github.com/Sage-ERP-X3/xtrem/commit/f0a7ac2f0add2c3e60343b7c90d1c49e8ab2b2b7))

### Features
* **shopfloor:** XAPPSF-666-Reengineer-time-tracking-sync-header-detail-mutation   ([#21271](https://github.com/Sage-ERP-X3/xtrem/issues/21271))   ([b51e6e7](https://github.com/Sage-ERP-X3/xtrem/commit/b51e6e7934dd73fc373a98b43fad2d7ee399f602))
* **shopfloor:** XAPPSF-711   ([#21258](https://github.com/Sage-ERP-X3/xtrem/issues/21258))   ([69d7026](https://github.com/Sage-ERP-X3/xtrem/commit/69d7026bd46ff6f42eae2e06e21051b475d534e0))
* **shopfloor:** XAPPSF-730 Reworked handling of clock in/out button.   ([#21238](https://github.com/Sage-ERP-X3/xtrem/issues/21238))   ([762910d](https://github.com/Sage-ERP-X3/xtrem/commit/762910d85a0e7a1006dd4743faffc28bd0b7e728))


## [48.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.7) (2024-09-24)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-740 add site to tracking header and sync nodes   ([#21230](https://github.com/Sage-ERP-X3/xtrem/issues/21230))   ([6bc489f](https://github.com/Sage-ERP-X3/xtrem/commit/6bc489f7b19360f798ea24e97960e7bfd2a1fc6c))


## [48.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.6) (2024-09-23)

### Bug Fixes

### Features


## [48.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.5) (2024-09-22)

### Bug Fixes

### Features


## [48.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.4) (2024-09-21)

### Bug Fixes

### Features


## [48.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.3) (2024-09-20)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-671 re-structure time tracking sync nodes.   ([#21160](https://github.com/Sage-ERP-X3/xtrem/issues/21160))   ([1bdfd37](https://github.com/Sage-ERP-X3/xtrem/commit/1bdfd371dbc4f7aa73546aec33a1cbc678623387))


## [48.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.2) (2024-09-19)

### Bug Fixes

### Features
* XAPPSF-357 sync scheduling   ([#20499](https://github.com/Sage-ERP-X3/xtrem/issues/20499))   ([4901cd6](https://github.com/Sage-ERP-X3/xtrem/commit/4901cd625e5b7639232cbcfede7cb1b3340ef44a))


## [48.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.1) (2024-09-19)

### Bug Fixes

### Features


## [48.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@48.0.0) (2024-09-19)

### Bug Fixes

### Features


## [47.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.30) (2024-09-19)

### Bug Fixes

### Features


## [47.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.29) (2024-09-18)

### Bug Fixes

### Features
* **interop:** XT-79042 add duplication ([XT-79042](https://jira.sage.com/browse/XT-79042)) ([#21124](https://github.com/Sage-ERP-X3/xtrem/issues/21124))   ([6e24be2](https://github.com/Sage-ERP-X3/xtrem/commit/6e24be2956abd6772575e7450168bfb61870c821))


## [47.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.28) (2024-09-17)

### Bug Fixes
* XT-78652 Modify filter to include extension properties ([XT-78652](https://jira.sage.com/browse/XT-78652)) ([#21110](https://github.com/Sage-ERP-X3/xtrem/issues/21110))   ([e466d9d](https://github.com/Sage-ERP-X3/xtrem/commit/e466d9df5db1711ffadee95bdf1a64b7d0df6544))

### Features
* **shopfloor:** XAPPSF-670-Time-tracking-header-incorporate-check-isSynced-property   ([#21123](https://github.com/Sage-ERP-X3/xtrem/issues/21123))   ([1f159f5](https://github.com/Sage-ERP-X3/xtrem/commit/1f159f5c578143779ff01843dae393986364038d))
* **shopfloor:** XAPPSF-667 stop time treatment for setup   ([#21086](https://github.com/Sage-ERP-X3/xtrem/issues/21086))   ([a8316f7](https://github.com/Sage-ERP-X3/xtrem/commit/a8316f7a5566baabe8e0f0e78d744e6a0d7bcfb4))


## [47.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.27) (2024-09-16)

### Bug Fixes

### Features


## [47.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.26) (2024-09-15)

### Bug Fixes

### Features


## [47.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.25) (2024-09-14)

### Bug Fixes

### Features


## [47.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.24) (2024-09-13)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-668-add-sync-reference-timeTrackingHeader-node   ([#20975](https://github.com/Sage-ERP-X3/xtrem/issues/20975))   ([6acb1cb](https://github.com/Sage-ERP-X3/xtrem/commit/6acb1cbbdc5d56b3e9190896e0564fefa14c04a0))
* XT-78333 add xtrem-auditing dependency to xtrem-workflow ([XT-78333](https://jira.sage.com/browse/XT-78333)) ([#21032](https://github.com/Sage-ERP-X3/xtrem/issues/21032))   ([fbabb1c](https://github.com/Sage-ERP-X3/xtrem/commit/fbabb1c07c37e03426c6391ef284ccbd7ccd61d6))
* **interop:** XAPPSF-353 update connector pages   ([#21000](https://github.com/Sage-ERP-X3/xtrem/issues/21000))   ([eef2e6e](https://github.com/Sage-ERP-X3/xtrem/commit/eef2e6e523da0be6dc3e88ae0c6712dff191b3d1))


## [47.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.23) (2024-09-12)

### Bug Fixes

### Features


## [47.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.22) (2024-09-11)

### Bug Fixes

### Features


## [47.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.21) (2024-09-10)

### Bug Fixes
* **shopfloor:** XAPPSF-667 taking the stop time right from the click   ([#20954](https://github.com/Sage-ERP-X3/xtrem/issues/20954))   ([9ae13b3](https://github.com/Sage-ERP-X3/xtrem/commit/9ae13b3d02619061fb2b64c3b3e56ff351dfa42f))

### Features
* **shopfloor:** XT-76340 update shopfloor x3 ([XT-76340](https://jira.sage.com/browse/XT-76340)) ([#20891](https://github.com/Sage-ERP-X3/xtrem/issues/20891))   ([2c4ed37](https://github.com/Sage-ERP-X3/xtrem/commit/2c4ed37f4405b49e3aa9e0347804a15b8a34000d))


## [47.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.20) (2024-09-09)

### Bug Fixes

### Features


## [47.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.19) (2024-09-08)

### Bug Fixes

### Features


## [47.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.18) (2024-09-07)

### Bug Fixes

### Features


## [47.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.17) (2024-09-06)

### Bug Fixes

### Features


## [47.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.16) (2024-09-05)

### Bug Fixes

### Features


## [47.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.15) (2024-09-04)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-645-clock-out-incorporate-sync-transfer-time-tracking   ([#20831](https://github.com/Sage-ERP-X3/xtrem/issues/20831))   ([9b61072](https://github.com/Sage-ERP-X3/xtrem/commit/9b610726237b38245e71dabff15ee0c638fbc5dd))


## [47.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.14) (2024-09-03)

### Bug Fixes

### Features


## [47.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.13) (2024-09-02)

### Bug Fixes

### Features


## [47.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.12) (2024-09-01)

### Bug Fixes

### Features


## [47.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.11) (2024-08-31)

### Bug Fixes

### Features


## [47.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.10) (2024-08-30)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-539-tracking-operator-refactoring   ([#20705](https://github.com/Sage-ERP-X3/xtrem/issues/20705))   ([d9dbbe7](https://github.com/Sage-ERP-X3/xtrem/commit/d9dbbe74b11654eba53fb561a2dc98d1856ba89c))


## [47.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.9) (2024-08-29)

### Bug Fixes

### Features


## [47.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.8) (2024-08-28)

### Bug Fixes

### Features


## [47.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.7) (2024-08-27)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-528 Generate new sync tracking document   ([#20631](https://github.com/Sage-ERP-X3/xtrem/issues/20631))   ([054bbd8](https://github.com/Sage-ERP-X3/xtrem/commit/054bbd88477c3a3249eeaa8451659c4ab553f0df))


## [47.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.6) (2024-08-26)

### Bug Fixes

### Features


## [47.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.5) (2024-08-25)

### Bug Fixes

### Features


## [47.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.4) (2024-08-24)

### Bug Fixes

### Features


## [47.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.3) (2024-08-23)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-527-clock-out-operator-disconnect-logged-in-user   ([#20530](https://github.com/Sage-ERP-X3/xtrem/issues/20530))   ([e587587](https://github.com/Sage-ERP-X3/xtrem/commit/e5875875496df25b6d84b1e670d30c9296cd4f26))


## [47.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.2) (2024-08-22)

### Bug Fixes

### Features


## [47.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.1) (2024-08-22)

### Bug Fixes

### Features


## [47.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@47.0.0) (2024-08-22)

### Bug Fixes

### Features


## [46.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.38) (2024-08-22)

### Bug Fixes

### Features


## [46.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.37) (2024-08-21)

### Bug Fixes

### Features


## [46.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.36) (2024-08-20)

### Bug Fixes

### Features


## [46.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.35) (2024-08-18)

### Bug Fixes

### Features


## [46.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.34) (2024-08-17)

### Bug Fixes

### Features
* XT-76972 first brew of workflow test framework ([XT-76972](https://jira.sage.com/browse/XT-76972)) ([#20423](https://github.com/Sage-ERP-X3/xtrem/issues/20423))   ([9d32bf5](https://github.com/Sage-ERP-X3/xtrem/commit/9d32bf5a86e33c4b8dde61958f4d2b5ed81204d0))


## [46.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.33) (2024-08-16)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-630 quantities scale.   ([#20422](https://github.com/Sage-ERP-X3/xtrem/issues/20422))   ([698c2a5](https://github.com/Sage-ERP-X3/xtrem/commit/698c2a5ec06c84a1910606cedab7c2057488ce6a))
* XT-76691 review and improve entity-created config page ([XT-76691](https://jira.sage.com/browse/XT-76691)) ([#20374](https://github.com/Sage-ERP-X3/xtrem/issues/20374))   ([56fe177](https://github.com/Sage-ERP-X3/xtrem/commit/56fe177e209a06f59b8c0089c37d6a12a8b0d9cd))
* **shopfloor:** XAPPSF-43 completed quantity   ([#20404](https://github.com/Sage-ERP-X3/xtrem/issues/20404))   ([d8aeea2](https://github.com/Sage-ERP-X3/xtrem/commit/d8aeea2b6b6e30c4dec1660d41e3cfea9afaa38b))


## [46.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.32) (2024-08-15)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-526-clock-out-button-clockin-status-node-tracking   ([#20400](https://github.com/Sage-ERP-X3/xtrem/issues/20400))   ([fbdbb31](https://github.com/Sage-ERP-X3/xtrem/commit/fbdbb31717054c7c230d2d94b4d914eac0b28b5c))


## [46.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.31) (2024-08-14)

### Bug Fixes

### Features


## [46.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.30) (2024-08-13)

### Bug Fixes

### Features


## [46.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.29) (2024-08-12)

### Bug Fixes

### Features


## [46.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.28) (2024-08-11)

### Bug Fixes

### Features


## [46.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.27) (2024-08-10)

### Bug Fixes

### Features


## [46.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.26) (2024-08-09)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-486 tracking stop times   ([#20295](https://github.com/Sage-ERP-X3/xtrem/issues/20295))   ([1ecb01c](https://github.com/Sage-ERP-X3/xtrem/commit/1ecb01cdabcdcf96a762dfa33692bf441b2249b1))
* **shopfloor:** XAPPSF-468-clock-in-new-operator-node-create-mutation   ([#20310](https://github.com/Sage-ERP-X3/xtrem/issues/20310))   ([f489d50](https://github.com/Sage-ERP-X3/xtrem/commit/f489d507546bb94ee64d6fa29aac47afadde49b2))
* **shopfloor:** XAPPSF-551 Create tracking sync header and detail nodes   ([#20301](https://github.com/Sage-ERP-X3/xtrem/issues/20301))   ([dfe6b3c](https://github.com/Sage-ERP-X3/xtrem/commit/dfe6b3c922b302c66f01ed731790e9ea88c9fb30))


## [46.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.25) (2024-08-08)

### Bug Fixes

### Features


## [46.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.24) (2024-08-07)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-469-clock-in-request-operator   ([#20278](https://github.com/Sage-ERP-X3/xtrem/issues/20278))   ([1cd1279](https://github.com/Sage-ERP-X3/xtrem/commit/1cd127983e56e2990822883484d0550a98cfadc3))


## [46.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.23) (2024-08-06)

### Bug Fixes

### Features
* XT-76250 Workflow ([XT-76250](https://jira.sage.com/browse/XT-76250)) ([#16287](https://github.com/Sage-ERP-X3/xtrem/issues/16287))   ([1d756ab](https://github.com/Sage-ERP-X3/xtrem/commit/1d756ab8ebbebdf7dad1b15e30dd129efffcfd25))


## [46.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.22) (2024-08-05)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-557 add isSynced property and extended unit test   ([#20254](https://github.com/Sage-ERP-X3/xtrem/issues/20254))   ([02b0eaf](https://github.com/Sage-ERP-X3/xtrem/commit/02b0eaff8dec7c37ef3474c6f69742a05caccddf))


## [46.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.21) (2024-08-05)

### Bug Fixes

### Features


## [46.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.20) (2024-08-04)

### Bug Fixes

### Features


## [46.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.19) (2024-08-03)

### Bug Fixes

### Features


## [46.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.18) (2024-08-02)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-533 re engineering start tracking   ([#20210](https://github.com/Sage-ERP-X3/xtrem/issues/20210))   ([6db7ec4](https://github.com/Sage-ERP-X3/xtrem/commit/6db7ec482e2837965c83b055607983b39dc5e357))


## [46.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.17) (2024-08-02)

### Bug Fixes

### Features


## [46.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.16) (2024-07-31)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-525 set SDMO mappings to inactive   ([#20209](https://github.com/Sage-ERP-X3/xtrem/issues/20209))   ([2282057](https://github.com/Sage-ERP-X3/xtrem/commit/2282057c09042a5a0b703987adda0ff147deb0eb))


## [46.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.15) (2024-07-31)

### Bug Fixes

### Features
* XT-74720 refactor reference and collection mapping ([XT-74720](https://jira.sage.com/browse/XT-74720)) ([#20166](https://github.com/Sage-ERP-X3/xtrem/issues/20166))   ([abc9170](https://github.com/Sage-ERP-X3/xtrem/commit/abc9170b29d78b7bfd38cd66c7e7f332afe8ce31))


## [46.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.14) (2024-07-30)

### Bug Fixes

### Features
* **xtrem-cli-atp:** XT-74594 allure report local reporting ([XT-74594](https://jira.sage.com/browse/XT-74594)) ([#20060](https://github.com/Sage-ERP-X3/xtrem/issues/20060))   ([136b2cf](https://github.com/Sage-ERP-X3/xtrem/commit/136b2cf1866102aa2422fa23932b398046b56452))


## [46.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.13) (2024-07-29)

### Bug Fixes

### Features


## [46.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.12) (2024-07-28)

### Bug Fixes

### Features


## [46.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.11) (2024-07-27)

### Bug Fixes

### Features


## [46.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.10) (2024-07-26)

### Bug Fixes

### Features
* **shopfloor:** XT-75765 add / update mapping setup data ([XT-75765](https://jira.sage.com/browse/XT-75765)) ([#20149](https://github.com/Sage-ERP-X3/xtrem/issues/20149))   ([631eeb3](https://github.com/Sage-ERP-X3/xtrem/commit/631eeb3dec55dd2bf822824eaae5d3af7047ffda))
* **shopfloor:** XAPPSF-44 Tracking start times   ([#20087](https://github.com/Sage-ERP-X3/xtrem/issues/20087))   ([befb4ed](https://github.com/Sage-ERP-X3/xtrem/commit/befb4ed96eae5c488b19682b7884ddca85e3ef2f))


## [46.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.9) (2024-07-25)

### Bug Fixes

### Features


## [46.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.8) (2024-07-24)

### Bug Fixes

### Features
* **shopfloor:** XT-75597 create mapping data ([XT-75597](https://jira.sage.com/browse/XT-75597)) ([#20071](https://github.com/Sage-ERP-X3/xtrem/issues/20071))   ([c51a79c](https://github.com/Sage-ERP-X3/xtrem/commit/c51a79c982b52b1ea257ee9a7a83940efe013065))
* **shopfloor:** XT-74347 enum mapping page ([XT-74347](https://jira.sage.com/browse/XT-74347)) ([#19831](https://github.com/Sage-ERP-X3/xtrem/issues/19831))   ([7d8365d](https://github.com/Sage-ERP-X3/xtrem/commit/7d8365d85860e8f1afd98b7dc7b1100f18d21552))


## [46.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.7) (2024-07-23)

### Bug Fixes

### Features


## [46.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.6) (2024-07-22)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-465-enhance-operator-node   ([#19888](https://github.com/Sage-ERP-X3/xtrem/issues/19888))   ([a87f640](https://github.com/Sage-ERP-X3/xtrem/commit/a87f640dda6256664d8747edb321cf6e15582bab))


## [46.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.5) (2024-07-21)

### Bug Fixes

### Features


## [46.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.4) (2024-07-20)

### Bug Fixes

### Features


## [46.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.3) (2024-07-19)

### Bug Fixes

### Features
* **pipelines:** XT-72499 - add  shopfloor ST pipelines V2 ([XT-72499](https://jira.sage.com/browse/XT-72499)) ([#19620](https://github.com/Sage-ERP-X3/xtrem/issues/19620))   ([c5712fa](https://github.com/Sage-ERP-X3/xtrem/commit/c5712fa036c14dab277d0ec1837034f6de319de4))


## [46.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.2) (2024-07-18)

### Bug Fixes

### Features


## [46.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.1) (2024-07-18)

### Bug Fixes

### Features


## [46.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@46.0.0) (2024-07-18)

### Bug Fixes

### Features


## [45.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.30) (2024-07-17)

### Bug Fixes

### Features


## [45.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.29) (2024-07-16)

### Bug Fixes

### Features


## [45.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.28) (2024-07-15)

### Bug Fixes

### Features


## [45.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.27) (2024-07-15)

### Bug Fixes

### Features


## [45.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.26) (2024-07-14)

### Bug Fixes

### Features


## [45.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.25) (2024-07-13)

### Bug Fixes

### Features


## [45.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.24) (2024-07-12)

### Bug Fixes

### Features
* XT-72634 extend node structure to implement mapping ([XT-72634](https://jira.sage.com/browse/XT-72634)) ([#19794](https://github.com/Sage-ERP-X3/xtrem/issues/19794))   ([49588d9](https://github.com/Sage-ERP-X3/xtrem/commit/49588d94bcc055a30bf20be80309c2ea0690ac30))


## [45.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.23) (2024-07-11)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-509-statuses-localized   ([#19827](https://github.com/Sage-ERP-X3/xtrem/issues/19827))   ([a3183c7](https://github.com/Sage-ERP-X3/xtrem/commit/a3183c7180c700fd4a44a5a148bf2ed762a875a9))
* **shopfloor:** XAPPSF-472-Tracking-detail-page-WorkOrderOperationResource-progressStatus   ([#19812](https://github.com/Sage-ERP-X3/xtrem/issues/19812))   ([099cc72](https://github.com/Sage-ERP-X3/xtrem/commit/099cc72eefe5aaac0291b556fd9320c3be2ed46a))


## [45.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.22) (2024-07-10)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-470 workOrderOperationProgressStatus changes.   ([#19797](https://github.com/Sage-ERP-X3/xtrem/issues/19797))   ([ff3cee2](https://github.com/Sage-ERP-X3/xtrem/commit/ff3cee2cf7d46a7a84c935d3950a1baf18521412))
* **shopfloor:** XAPPSF-471-WorkOrderOperationResource-new-property-progressStatus   ([#19792](https://github.com/Sage-ERP-X3/xtrem/issues/19792))   ([2052cd0](https://github.com/Sage-ERP-X3/xtrem/commit/2052cd0e7565299032a6c6a2f553616bd2fb83dd))


## [45.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.21) (2024-07-09)

### Bug Fixes

### Features
* XT-72453 interop operation references in parameters and results ([XT-72453](https://jira.sage.com/browse/XT-72453)) ([#19745](https://github.com/Sage-ERP-X3/xtrem/issues/19745))   ([9fdcbef](https://github.com/Sage-ERP-X3/xtrem/commit/9fdcbef41f43e848e7384791c6041a49278422b7))
* **main:** change eslintrc-base same as xtrem/ eslint-truth XT-74269 ([XT-74269](https://jira.sage.com/browse/XT-74269)) ([#19781](https://github.com/Sage-ERP-X3/xtrem/issues/19781))   ([bac91b0](https://github.com/Sage-ERP-X3/xtrem/commit/bac91b03c220498e059fc9481ec5722369836b0d))


## [45.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.20) (2024-07-08)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-73-machine-header-page-computed-data   ([#19713](https://github.com/Sage-ERP-X3/xtrem/issues/19713))   ([bc79f2f](https://github.com/Sage-ERP-X3/xtrem/commit/bc79f2f98d11050123caee82f0e9e8d7961b74a9))


## [45.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.19) (2024-07-07)

### Bug Fixes

### Features


## [45.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.18) (2024-07-06)

### Bug Fixes

### Features


## [45.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.17) (2024-07-05)

### Bug Fixes

### Features
* proxy renaming XT-72346 ([XT-72346](https://jira.sage.com/browse/XT-72346)) ([#19724](https://github.com/Sage-ERP-X3/xtrem/issues/19724))   ([f95366c](https://github.com/Sage-ERP-X3/xtrem/commit/f95366c3eb53c4d55f939ada90fb922048a1dcdd))


## [45.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.16) (2024-07-04)

### Bug Fixes

### Features
* **queues:** sort routing to avoid diff XT-72346 ([XT-72346](https://jira.sage.com/browse/XT-72346)) ([#19718](https://github.com/Sage-ERP-X3/xtrem/issues/19718))   ([6871c73](https://github.com/Sage-ERP-X3/xtrem/commit/6871c73b2e6e1124e1036467320619a3b0cad41a))
* XT-69536 generate api.d.ts files with _factory property ([XT-69536](https://jira.sage.com/browse/XT-69536)) ([#19715](https://github.com/Sage-ERP-X3/xtrem/issues/19715))   ([be8ec04](https://github.com/Sage-ERP-X3/xtrem/commit/be8ec04c82b99358c72487dfc609f9dcd4a88f16))


## [45.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.15) (2024-07-03)

### Bug Fixes
* **shopfloor:** XAPPSF-345 fix menu item   ([#19691](https://github.com/Sage-ERP-X3/xtrem/issues/19691))   ([74c7b96](https://github.com/Sage-ERP-X3/xtrem/commit/74c7b96b5352d891e855d69bc7ab81625b666af1))

### Features
* **queues:** regen queue conf XT-72236 ([XT-72236](https://jira.sage.com/browse/XT-72236)) ([#19677](https://github.com/Sage-ERP-X3/xtrem/issues/19677))   ([c530021](https://github.com/Sage-ERP-X3/xtrem/commit/c53002183312eb6983c30cfdebe68d007f80743d))
* **shopfloor:** XAPPSF-386 date filter to work order operation list   ([#19685](https://github.com/Sage-ERP-X3/xtrem/issues/19685))   ([b341f1d](https://github.com/Sage-ERP-X3/xtrem/commit/b341f1d461515b64c2274a66d5f277126fc469f1))


## [45.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.14) (2024-07-02)

### Bug Fixes

### Features


## [45.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.13) (2024-07-01)

### Bug Fixes

### Features


## [45.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.12) (2024-06-30)

### Bug Fixes

### Features


## [45.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.11) (2024-06-29)

### Bug Fixes

### Features


## [45.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.10) (2024-06-28)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-437-workorder-header-page-computed-data   ([#19652](https://github.com/Sage-ERP-X3/xtrem/issues/19652))   ([2badeca](https://github.com/Sage-ERP-X3/xtrem/commit/2badecad06b1b0873f2b0412b1214c8227937a5a))
* **shopfloor:** XAPPSF-431 missing cursor added.   ([#19647](https://github.com/Sage-ERP-X3/xtrem/issues/19647))   ([6148411](https://github.com/Sage-ERP-X3/xtrem/commit/61484110644d61c0e309d1af2f2aeb0bf4be9400))


## [45.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.9) (2024-06-27)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-71-workorder-header-page-computed-data   ([#19629](https://github.com/Sage-ERP-X3/xtrem/issues/19629))   ([18f1fef](https://github.com/Sage-ERP-X3/xtrem/commit/18f1fef5d5eb4e4c974724685f59499d99331775))


## [45.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.8) (2024-06-26)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-26-detail-page-operations-list   ([#19611](https://github.com/Sage-ERP-X3/xtrem/issues/19611))   ([4f370db](https://github.com/Sage-ERP-X3/xtrem/commit/4f370db15c6ef1ed5defa891d94ef033a2548be8))


## [45.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.7) (2024-06-25)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-406 add work order operation properties for t…   ([#19566](https://github.com/Sage-ERP-X3/xtrem/issues/19566))   ([47c6ba8](https://github.com/Sage-ERP-X3/xtrem/commit/47c6ba8850ef31c316f71ae11099cda30afacd94))


## [45.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.6) (2024-06-24)

### Bug Fixes

### Features


## [45.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.5) (2024-06-23)

### Bug Fixes

### Features


## [45.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.4) (2024-06-22)

### Bug Fixes

### Features


## [45.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.3) (2024-06-21)

### Bug Fixes

### Features
* XT-72289 remote metadata caching ([XT-72289](https://jira.sage.com/browse/XT-72289)) ([#19550](https://github.com/Sage-ERP-X3/xtrem/issues/19550))   ([8d8fecd](https://github.com/Sage-ERP-X3/xtrem/commit/8d8fecd222e862aff943bfa37fb6d4fe85544730))
* **shopfloor:** XAPPSF-385 Add work order operation list widget   ([#19551](https://github.com/Sage-ERP-X3/xtrem/issues/19551))   ([3597d4c](https://github.com/Sage-ERP-X3/xtrem/commit/3597d4c7cf8a73827a51a785c995c0b7d2473870))
* **shopfloor:** XAPPSF-348 build pages   ([#19523](https://github.com/Sage-ERP-X3/xtrem/issues/19523))   ([e60cbfa](https://github.com/Sage-ERP-X3/xtrem/commit/e60cbfa2bd46f5e692300ceb8fb6557276f367bc))


## [45.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.2) (2024-06-20)

### Bug Fixes

### Features


## [45.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.1) (2024-06-20)

### Bug Fixes

### Features


## [45.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@45.0.0) (2024-06-20)

### Bug Fixes

### Features


## [44.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.31) (2024-06-19)

### Bug Fixes

### Features
* XT-72289 dataType metadata and clean up ([XT-72289](https://jira.sage.com/browse/XT-72289)) ([#19495](https://github.com/Sage-ERP-X3/xtrem/issues/19495))   ([c22242c](https://github.com/Sage-ERP-X3/xtrem/commit/c22242c51fa318de6a43b873cc530c06b35da327))


## [44.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.30) (2024-06-18)

### Bug Fixes

### Features


## [44.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.29) (2024-06-18)

### Bug Fixes

### Features


## [44.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.28) (2024-06-17)

### Bug Fixes

### Features


## [44.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.27) (2024-06-16)

### Bug Fixes

### Features


## [44.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.26) (2024-06-15)

### Bug Fixes

### Features


## [44.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.25) (2024-06-14)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-72 page header machine   ([#19465](https://github.com/Sage-ERP-X3/xtrem/issues/19465))   ([54781d4](https://github.com/Sage-ERP-X3/xtrem/commit/54781d4fee3249011a94bd972d388e6d0a6e6674))
* **shopfloor:** XAPPSF-25-AB-Work-order-header-page-display-static-data   ([#19460](https://github.com/Sage-ERP-X3/xtrem/issues/19460))   ([71d5ce3](https://github.com/Sage-ERP-X3/xtrem/commit/71d5ce34fc5111b1c348294446243fb115b789c8))


## [44.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.24) (2024-06-13)

### Bug Fixes

### Features


## [44.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.23) (2024-06-12)

### Bug Fixes

### Features
* **)hopfloor:** XAPPSF-359 deliver interim full access role   ([#19429](https://github.com/Sage-ERP-X3/xtrem/issues/19429))   ([2f6f303](https://github.com/Sage-ERP-X3/xtrem/commit/2f6f303652faf1c8bdc83ea36d685a3287586725))


## [44.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.22) (2024-06-11)

### Bug Fixes

### Features


## [44.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.21) (2024-06-10)

### Bug Fixes

### Features


## [44.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.20) (2024-06-09)

### Bug Fixes

### Features


## [44.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.19) (2024-06-08)

### Bug Fixes

### Features


## [44.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.18) (2024-06-07)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-326 refactor nodes to define permissions   ([#19380](https://github.com/Sage-ERP-X3/xtrem/issues/19380))   ([76bfac0](https://github.com/Sage-ERP-X3/xtrem/commit/76bfac07103aac3561a581e2b253c91f385946bf))


## [44.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.17) (2024-06-06)

### Bug Fixes

### Features


## [44.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.16) (2024-06-05)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-34 dummy dashboard creation.   ([#19319](https://github.com/Sage-ERP-X3/xtrem/issues/19319))   ([5c85444](https://github.com/Sage-ERP-X3/xtrem/commit/5c85444b52d50555443fec57dba96b152be4d30e))


## [44.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.15) (2024-06-04)

### Bug Fixes

### Features


## [44.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.14) (2024-06-03)

### Bug Fixes

### Features
* changelog page   ([#19259](https://github.com/Sage-ERP-X3/xtrem/issues/19259))   ([18a49bd](https://github.com/Sage-ERP-X3/xtrem/commit/18a49bdd72f45e23dfc6875d1bdfd6f75188c6cb))


## [44.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.13) (2024-06-02)

### Bug Fixes

### Features


## [44.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.12) (2024-06-01)

### Bug Fixes

### Features


## [44.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.11) (2024-05-31)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-197-AB-Workorder-Component-OperationResource-node-improvements   ([#19273](https://github.com/Sage-ERP-X3/xtrem/issues/19273))   ([5e40ff2](https://github.com/Sage-ERP-X3/xtrem/commit/5e40ff2d5419edbfdb30cdfe3497c5713e4512a6))
* **shopfloor:** XAPPSF-194 improve tracking header and detail node   ([#19245](https://github.com/Sage-ERP-X3/xtrem/issues/19245))   ([be449c2](https://github.com/Sage-ERP-X3/xtrem/commit/be449c22cad87375234b7fdd519f47e2537468f2))


## [44.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.10) (2024-05-30)

### Bug Fixes

### Features


## [44.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.9) (2024-05-30)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-192-193-196-AB-workorder-node-improvements   ([#19193](https://github.com/Sage-ERP-X3/xtrem/issues/19193))   ([e7ff6e0](https://github.com/Sage-ERP-X3/xtrem/commit/e7ff6e0f6722ff1c7991220d207eb8dcdb31e8ee))


## [44.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.8) (2024-05-29)

### Bug Fixes

### Features


## [44.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.7) (2024-05-29)

### Bug Fixes
* **shopfloor:** XT-71524 match permission with node ([XT-71524](https://jira.sage.com/browse/XT-71524)) ([#19224](https://github.com/Sage-ERP-X3/xtrem/issues/19224))   ([41102c2](https://github.com/Sage-ERP-X3/xtrem/commit/41102c2cc1de99d196a878d519476ded1317b4c9))

### Features


## [44.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.6) (2024-05-27)

### Bug Fixes

### Features
* **bizapps:** manage custom field mapping XT-66875 ([XT-66875](https://jira.sage.com/browse/XT-66875)) ([#19136](https://github.com/Sage-ERP-X3/xtrem/issues/19136))   ([e1642b2](https://github.com/Sage-ERP-X3/xtrem/commit/e1642b2637291da2a1c646d599b4b15ed177943b))


## [44.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.5) (2024-05-26)

### Bug Fixes

### Features


## [44.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.4) (2024-05-25)

### Bug Fixes

### Features


## [44.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.3) (2024-05-24)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-191  improve operator uom and resource  nodes   ([#19164](https://github.com/Sage-ERP-X3/xtrem/issues/19164))   ([3be4eae](https://github.com/Sage-ERP-X3/xtrem/commit/3be4eae21377d98b178cf88b7a2b8c88f54f46f8))


## [44.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.2) (2024-05-23)

### Bug Fixes

### Features


## [44.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.1) (2024-05-23)

### Bug Fixes

### Features


## [44.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@44.0.0) (2024-05-23)

### Bug Fixes

### Features


## [43.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.34) (2024-05-22)

### Bug Fixes

### Features
* **xtrem-System:** XT-63636 System authorization refactoring ([XT-63636](https://jira.sage.com/browse/XT-63636)) ([#18930](https://github.com/Sage-ERP-X3/xtrem/issues/18930))   ([5fb5982](https://github.com/Sage-ERP-X3/xtrem/commit/5fb59822828e6ed8db1f5c67b1024b86996e6521))
* **xtrem-Authorization:** XT-63280 Authorization refactoring ([XT-63280](https://jira.sage.com/browse/XT-63280)) ([#18927](https://github.com/Sage-ERP-X3/xtrem/issues/18927))   ([52f8857](https://github.com/Sage-ERP-X3/xtrem/commit/52f8857ea898506006d4fa06c947350efe92db9f))


## [43.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.33) (2024-05-21)

### Bug Fixes

### Features
* page fragments   ([#17857](https://github.com/Sage-ERP-X3/xtrem/issues/17857))   ([d0ab9bd](https://github.com/Sage-ERP-X3/xtrem/commit/d0ab9bdf858079bd649dde9f09082d36a5f7ee57))
* **shopfloor:** XAPPSF-189 Update pipelines   ([#19095](https://github.com/Sage-ERP-X3/xtrem/issues/19095))   ([ea69677](https://github.com/Sage-ERP-X3/xtrem/commit/ea6967709e7aeddd73a4ba6415c162685b24939c))


## [43.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.32) (2024-05-20)

### Bug Fixes

### Features


## [43.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.31) (2024-05-19)

### Bug Fixes

### Features


## [43.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.30) (2024-05-18)

### Bug Fixes

### Features


## [43.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.29) (2024-05-17)

### Bug Fixes

### Features
* XT-70071 interop enum mapping ([XT-70071](https://jira.sage.com/browse/XT-70071)) ([#19072](https://github.com/Sage-ERP-X3/xtrem/issues/19072))   ([923d3c8](https://github.com/Sage-ERP-X3/xtrem/commit/923d3c8b7e86de7c8784ca81c28840694db38ac3))


## [43.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.28) (2024-05-16)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-45-AB-workOrderOperationResource-node   ([#19036](https://github.com/Sage-ERP-X3/xtrem/issues/19036))   ([f51a2bb](https://github.com/Sage-ERP-X3/xtrem/commit/f51a2bb9c2f16a9fb247fcc4dc34edccc4684176))


## [43.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.27) (2024-05-15)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-37 create tracking detail node   ([#18997](https://github.com/Sage-ERP-X3/xtrem/issues/18997))   ([db30c69](https://github.com/Sage-ERP-X3/xtrem/commit/db30c69bee29c2b2fcaf160880e5dd57ebff534a))


## [43.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.26) (2024-05-14)

### Bug Fixes

### Features


## [43.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.25) (2024-05-13)

### Bug Fixes

### Features
* **shopfloor:** XAPPSF-38 Tracking Header Node   ([#18964](https://github.com/Sage-ERP-X3/xtrem/issues/18964))   ([f56ad1c](https://github.com/Sage-ERP-X3/xtrem/commit/f56ad1cb10802dd83019cd58745b82ff377742c0))


## [43.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.24) (2024-05-12)

### Bug Fixes

### Features


## [43.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.23) (2024-05-11)

### Bug Fixes

### Features


## [43.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.22) (2024-05-10)

### Bug Fixes

### Features
* **shopfloor:** Xt-69452-node-work-order-operation   ([#18898](https://github.com/Sage-ERP-X3/xtrem/issues/18898))   ([22bc049](https://github.com/Sage-ERP-X3/xtrem/commit/22bc049dd93403c31f281730d5aa577a82bce79d))


## [43.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.21) (2024-05-09)

### Bug Fixes

### Features
* **shopfloor:** XAPP-33 Create  wo component node   ([#18928](https://github.com/Sage-ERP-X3/xtrem/issues/18928))   ([97159ce](https://github.com/Sage-ERP-X3/xtrem/commit/97159cee5c645f0bcfe3741e75d1a38a518a8011))
* **shopfloor:** XAPPSF-36-AB-workOrderItem-node   ([#18929](https://github.com/Sage-ERP-X3/xtrem/issues/18929))   ([9c961b3](https://github.com/Sage-ERP-X3/xtrem/commit/9c961b3c4285551b15f1289cc396f5151bf28d28))


## [43.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.20) (2024-05-08)

### Bug Fixes

### Features


## [43.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.19) (2024-05-07)

### Bug Fixes

### Features
* **mfg-manuf-er-jusyn:** XAPPSF-35-unit-of-measure-node   ([#18844](https://github.com/Sage-ERP-X3/xtrem/issues/18844))   ([f17696e](https://github.com/Sage-ERP-X3/xtrem/commit/f17696e635e6e9c96d447117b0635983a659996e))


## [43.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.18) (2024-05-06)

### Bug Fixes

### Features


## [43.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.17) (2024-05-06)

### Bug Fixes

### Features
* **shopfloor:** XT-69827 resource node v2 ([XT-69827](https://jira.sage.com/browse/XT-69827)) ([#18854](https://github.com/Sage-ERP-X3/xtrem/issues/18854))   ([4ca6c3c](https://github.com/Sage-ERP-X3/xtrem/commit/4ca6c3cc4cc7f716a4c54c2d23e70e3e1b6e15cf))
* **shopfloor:** XT-69443-workorder-node ([XT-69443](https://jira.sage.com/browse/XT-69443)) ([#18807](https://github.com/Sage-ERP-X3/xtrem/issues/18807))   ([b6741e2](https://github.com/Sage-ERP-X3/xtrem/commit/b6741e2a7248c8ae87b7bf5194749bd68ade1711))


## [43.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.16) (2024-05-02)

### Bug Fixes

### Features


## [43.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.15) (2024-05-01)

### Bug Fixes

### Features


## [43.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.14) (2024-04-30)

### Bug Fixes

### Features
* XT-69683 move SysApp node to xtrem-interop ([XT-69683](https://jira.sage.com/browse/XT-69683)) ([#18772](https://github.com/Sage-ERP-X3/xtrem/issues/18772))   ([0fc4115](https://github.com/Sage-ERP-X3/xtrem/commit/0fc4115447c9364c2fdd1e8444f14587d1e84118))
* **shopfloor:** XT-69810 create operator node ([XT-69810](https://jira.sage.com/browse/XT-69810)) ([#18794](https://github.com/Sage-ERP-X3/xtrem/issues/18794))   ([02a29a5](https://github.com/Sage-ERP-X3/xtrem/commit/02a29a51f2918766054b0db27c88ed10c42378e0))


## [43.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.13) (2024-04-29)

### Bug Fixes

### Features


## [43.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.12) (2024-04-28)

### Bug Fixes

### Features


## [43.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.11) (2024-04-27)

### Bug Fixes

### Features


## [43.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.10) (2024-04-26)

### Bug Fixes

### Features
* **load-data:** ignore 777777777777777777777 directory XT-99999 ([XT-99999](https://jira.sage.com/browse/XT-99999)) ([#18733](https://github.com/Sage-ERP-X3/xtrem/issues/18733))   ([cc5ef47](https://github.com/Sage-ERP-X3/xtrem/commit/cc5ef472cd9eb699f7998570267415f84462afd4))


## [43.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.9) (2024-04-25)

### Bug Fixes

### Features


## [43.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.8) (2024-04-24)

### Bug Fixes

### Features
* **shopfloor:** XT-69699 Add parent menu ([XT-69699](https://jira.sage.com/browse/XT-69699)) ([#18690](https://github.com/Sage-ERP-X3/xtrem/issues/18690))   ([854ba75](https://github.com/Sage-ERP-X3/xtrem/commit/854ba751a436fd21f41c1c87392ffed292125b47))


## [43.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.7) (2024-04-23)

### Bug Fixes

### Features


## [43.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.6) (2024-04-22)

### Bug Fixes

### Features


## [43.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.5) (2024-04-21)

### Bug Fixes

### Features


## [43.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.4) (2024-04-20)

### Bug Fixes

### Features


## [43.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.3) (2024-04-19)

### Bug Fixes

### Features
* store client settings on the server   ([#18599](https://github.com/Sage-ERP-X3/xtrem/issues/18599))   ([ac27cac](https://github.com/Sage-ERP-X3/xtrem/commit/ac27cac958118119ee9c9362628da90ab4a5e37c))


## [43.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.2) (2024-04-18)

### Bug Fixes
* XT-69422 add files attribute to all main packages package.json ([XT-69422](https://jira.sage.com/browse/XT-69422)) ([#18595](https://github.com/Sage-ERP-X3/xtrem/issues/18595))   ([92f4833](https://github.com/Sage-ERP-X3/xtrem/commit/92f483369b1280bf9e9f48fe1f22d97823693fd0))

### Features


## [43.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.1) (2024-04-18)

### Bug Fixes

### Features


## [43.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@43.0.0) (2024-04-18)

### Bug Fixes

### Features


## [42.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.36) (2024-04-17)

### Bug Fixes

### Features
* api package not deleted before gen XT-65511 ([XT-65511](https://jira.sage.com/browse/XT-65511)) ([#17488](https://github.com/Sage-ERP-X3/xtrem/issues/17488))   ([60cf139](https://github.com/Sage-ERP-X3/xtrem/commit/60cf1394afc568c3d438243a69de7dccbd40218b))


## [42.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.35) (2024-04-16)

### Bug Fixes

### Features
* **shopfloor:** XT-68665 Environment preparation test node and page ([XT-68665](https://jira.sage.com/browse/XT-68665)) ([#18539](https://github.com/Sage-ERP-X3/xtrem/issues/18539))   ([0403216](https://github.com/Sage-ERP-X3/xtrem/commit/04032162891d98f409e0ee099a557119e283058c))


## [42.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.34) (2024-04-15)

### Bug Fixes

### Features
* **shopfloor:** XT-68519 MANFER Reset shopfloor package ([XT-68519](https://jira.sage.com/browse/XT-68519)) ([#18472](https://github.com/Sage-ERP-X3/xtrem/issues/18472))   ([ceffc52](https://github.com/Sage-ERP-X3/xtrem/commit/ceffc524546c3767dbe7d579ecd1a8bafbb75df0))


## [42.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.33) (2024-04-14)

### Bug Fixes

### Features


## [42.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.32) (2024-04-13)

### Bug Fixes

### Features


## [42.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.31) (2024-04-12)

### Bug Fixes

### Features
* **attachment:** add test data loader XT-68487 ([XT-68487](https://jira.sage.com/browse/XT-68487)) ([#18417](https://github.com/Sage-ERP-X3/xtrem/issues/18417))   ([8232870](https://github.com/Sage-ERP-X3/xtrem/commit/823287082d89e9cdbbafecf06db2f39d8763a69c))


## [42.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.30) (2024-04-11)

### Bug Fixes

### Features


## [42.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.29) (2024-04-10)

### Bug Fixes

### Features


## [42.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.28) (2024-04-09)

### Bug Fixes

### Features


## [42.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.27) (2024-04-08)

### Bug Fixes

### Features


## [42.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.26) (2024-04-07)

### Bug Fixes

### Features


## [42.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.25) (2024-04-06)

### Bug Fixes

### Features


## [42.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.24) (2024-04-05)

### Bug Fixes

### Features


## [42.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.23) (2024-04-04)

### Bug Fixes

### Features


## [42.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.22) (2024-04-03)

### Bug Fixes

### Features


## [42.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.21) (2024-04-02)

### Bug Fixes

### Features


## [42.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.20) (2024-04-01)

### Bug Fixes

### Features


## [42.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.19) (2024-03-31)

### Bug Fixes

### Features


## [42.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.18) (2024-03-30)

### Bug Fixes

### Features


## [42.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.17) (2024-03-29)

### Bug Fixes

### Features


## [42.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.16) (2024-03-28)

### Bug Fixes
* XT-67974 add asyncExport params i18n ([XT-67974](https://jira.sage.com/browse/XT-67974)) ([#18206](https://github.com/Sage-ERP-X3/xtrem/issues/18206))   ([69ef7ec](https://github.com/Sage-ERP-X3/xtrem/commit/69ef7ec0d7761473fa058ddb9a4fc46dd961d21a))

### Features


## [42.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.15) (2024-03-27)

### Bug Fixes

### Features


## [42.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.14) (2024-03-26)

### Bug Fixes

### Features


## [42.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.13) (2024-03-25)

### Bug Fixes
* XT-99999 Fix issue with imports ([XT-99999](https://jira.sage.com/browse/XT-99999)) ([#18130](https://github.com/Sage-ERP-X3/xtrem/issues/18130))   ([3076f1a](https://github.com/Sage-ERP-X3/xtrem/commit/3076f1ad800410071c8014a6a5b5da7ac9b1b139))

### Features


## [42.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.12) (2024-03-24)

### Bug Fixes

### Features


## [42.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.11) (2024-03-23)

### Bug Fixes

### Features


## [42.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.10) (2024-03-22)

### Bug Fixes

### Features


## [42.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.9) (2024-03-21)

### Bug Fixes

### Features


## [42.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.8) (2024-03-20)

### Bug Fixes

### Features


## [42.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.7) (2024-03-19)

### Bug Fixes

### Features


## [42.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.6) (2024-03-18)

### Bug Fixes

### Features


## [42.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.5) (2024-03-17)

### Bug Fixes

### Features


## [42.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.4) (2024-03-16)

### Bug Fixes

### Features


## [42.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.3) (2024-03-15)

### Bug Fixes

### Features


## [42.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.2) (2024-03-14)

### Bug Fixes

### Features


## [42.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.1) (2024-03-14)

### Bug Fixes

### Features


## [42.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@42.0.0) (2024-03-14)

### Bug Fixes

### Features


## [41.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.25) (2024-03-13)

### Bug Fixes

### Features


## [41.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.24) (2024-03-12)

### Bug Fixes

### Features


## [41.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.23) (2024-03-11)

### Bug Fixes

### Features


## [41.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.22) (2024-03-11)

### Bug Fixes

### Features


## [41.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.21) (2024-03-10)

### Bug Fixes

### Features


## [41.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.20) (2024-03-09)

### Bug Fixes

### Features


## [41.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.19) (2024-03-08)

### Bug Fixes

### Features


## [41.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.18) (2024-03-07)

### Bug Fixes

### Features


## [41.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.17) (2024-03-07)

### Bug Fixes

### Features


## [41.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.16) (2024-03-06)

### Bug Fixes

### Features
* XT-66624 improved the synchronization transform page ([XT-66624](https://jira.sage.com/browse/XT-66624)) ([#17655](https://github.com/Sage-ERP-X3/xtrem/issues/17655))   ([81bbc1e](https://github.com/Sage-ERP-X3/xtrem/commit/81bbc1e1e24867911a75aa19fe3eadffd4a76a17))
* XT-64328 attachment access rights ([XT-64328](https://jira.sage.com/browse/XT-64328)) ([#17638](https://github.com/Sage-ERP-X3/xtrem/issues/17638))   ([da50c08](https://github.com/Sage-ERP-X3/xtrem/commit/da50c0812de182f544b17526f6e84e196be8ae67))


## [41.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.15) (2024-03-05)

### Bug Fixes

### Features


## [41.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.14) (2024-03-04)

### Bug Fixes

### Features


## [41.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.13) (2024-03-03)

### Bug Fixes

### Features


## [41.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.12) (2024-03-02)

### Bug Fixes

### Features


## [41.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.11) (2024-03-01)

### Bug Fixes

### Features
* XT-66120 synchronize showcase apps ([XT-66120](https://jira.sage.com/browse/XT-66120)) ([#17449](https://github.com/Sage-ERP-X3/xtrem/issues/17449))   ([e408cfe](https://github.com/Sage-ERP-X3/xtrem/commit/e408cfe41d14c8433c3d9003c97854d6ec8f2dd9))


## [41.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.10) (2024-02-29)

### Bug Fixes

### Features


## [41.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.9) (2024-02-29)

### Bug Fixes

### Features
* **attachment:** garbage collector XT-64288 ([XT-64288](https://jira.sage.com/browse/XT-64288)) ([#17313](https://github.com/Sage-ERP-X3/xtrem/issues/17313))   ([d5f284a](https://github.com/Sage-ERP-X3/xtrem/commit/d5f284a4ebe9fafc45eacc0aaceef75321baf410))


## [41.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.8) (2024-02-28)

### Bug Fixes

### Features
* **xtrem-core,xtrem-shared:** XT-64400 getDataType metadata query refactoring ([XT-64400](https://jira.sage.com/browse/XT-64400)) ([#17116](https://github.com/Sage-ERP-X3/xtrem/issues/17116))   ([3e9164d](https://github.com/Sage-ERP-X3/xtrem/commit/3e9164d335a7598f69d2661f2d524c8323db3b81))


## [41.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.7) (2024-02-27)

### Bug Fixes

### Features
* XT-66139 adapt generation of elasticmq.conf files for biz apps ([XT-66139](https://jira.sage.com/browse/XT-66139)) ([#17408](https://github.com/Sage-ERP-X3/xtrem/issues/17408))   ([fc256bc](https://github.com/Sage-ERP-X3/xtrem/commit/fc256bc3d3c1e0cd9891ec17ecead78e096701c2))


## [41.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.6) (2024-02-26)

### Bug Fixes

### Features


## [41.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.5) (2024-02-26)

### Bug Fixes

### Features
* **businessEntity_refactoring:** authorization package   ([#17291](https://github.com/Sage-ERP-X3/xtrem/issues/17291))   ([718a6d9](https://github.com/Sage-ERP-X3/xtrem/commit/718a6d9f12922de589213cf7f538b2513a32fa5a))


## [41.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.4) (2024-02-26)

### Bug Fixes

### Features


## [41.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.3) (2024-02-23)

### Bug Fixes

### Features


## [41.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.2) (2024-02-22)

### Bug Fixes

### Features


## [41.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.1) (2024-02-22)

### Bug Fixes

### Features


## [41.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@41.0.0) (2024-02-22)

### Bug Fixes

### Features


## [40.0.40](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.40) (2024-02-21)

### Bug Fixes

### Features


## [40.0.39](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.39) (2024-02-20)

### Bug Fixes

### Features


## [40.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.38) (2024-02-20)

### Bug Fixes

### Features


## [40.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.37) (2024-02-20)

### Bug Fixes

### Features


## [40.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.36) (2024-02-19)

### Bug Fixes

### Features


## [40.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.35) (2024-02-18)

### Bug Fixes

### Features


## [40.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.34) (2024-02-17)

### Bug Fixes

### Features


## [40.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.33) (2024-02-17)

### Bug Fixes

### Features


## [40.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.32) (2024-02-17)

### Bug Fixes

### Features


## [40.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.31) (2024-02-16)

### Bug Fixes

### Features


## [40.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.30) (2024-02-15)

### Bug Fixes

### Features


## [40.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.29) (2024-02-14)

### Bug Fixes

### Features


## [40.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.28) (2024-02-13)

### Bug Fixes

### Features


## [40.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.27) (2024-02-12)

### Bug Fixes

### Features


## [40.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.26) (2024-02-11)

### Bug Fixes

### Features


## [40.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.25) (2024-02-10)

### Bug Fixes

### Features


## [40.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.24) (2024-02-09)

### Bug Fixes

### Features
* XT-64763 timezone API ([XT-64763](https://jira.sage.com/browse/XT-64763)) ([#16929](https://github.com/Sage-ERP-X3/xtrem/issues/16929))   ([bc955cb](https://github.com/Sage-ERP-X3/xtrem/commit/bc955cb656de8e4fb472713ebfc16e4208837807))


## [40.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.23) (2024-02-08)

### Bug Fixes

### Features


## [40.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.22) (2024-02-07)

### Bug Fixes

### Features


## [40.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.21) (2024-02-06)

### Bug Fixes

### Features
* XT-63829 Publish create and update user ([XT-63829](https://jira.sage.com/browse/XT-63829)) ([#16790](https://github.com/Sage-ERP-X3/xtrem/issues/16790))   ([2ce940e](https://github.com/Sage-ERP-X3/xtrem/commit/2ce940e9a21d46bb17b9315c253bb1f94a1f487a))


## [40.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.20) (2024-02-05)

### Bug Fixes

### Features


## [40.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.19) (2024-02-04)

### Bug Fixes

### Features


## [40.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.18) (2024-02-03)

### Bug Fixes

### Features


## [40.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.17) (2024-02-02)

### Bug Fixes

### Features
* **attachment:** add attachment and attachment-association nodes XT-63352 ([XT-63352](https://jira.sage.com/browse/XT-63352)) ([#16760](https://github.com/Sage-ERP-X3/xtrem/issues/16760))   ([9b35c29](https://github.com/Sage-ERP-X3/xtrem/commit/9b35c29421524f412760ebae27079212350b185a))


## [40.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.16) (2024-02-01)

### Bug Fixes

### Features


## [40.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.15) (2024-01-31)

### Bug Fixes

### Features
* postgres 15 support XT-62818 ([XT-62818](https://jira.sage.com/browse/XT-62818)) ([#16695](https://github.com/Sage-ERP-X3/xtrem/issues/16695))   ([fa8a20b](https://github.com/Sage-ERP-X3/xtrem/commit/fa8a20bf2830c0c9f72be9fa43055d27aca4bf35))


## [40.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.14) (2024-01-30)

### Bug Fixes

### Features


## [40.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.13) (2024-01-29)

### Bug Fixes

### Features


## [40.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.12) (2024-01-28)

### Bug Fixes

### Features


## [40.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.11) (2024-01-27)

### Bug Fixes

### Features


## [40.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.10) (2024-01-26)

### Bug Fixes

### Features


## [40.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.9) (2024-01-25)

### Bug Fixes

### Features
* loading xtrem pages and dashboard for x3 poc X3-302515 X3-288551 ([X3-302515](https://jira.sage.com/browse/X3-302515); [X3-288551](https://jira.sage.com/browse/X3-288551)) ([#15842](https://github.com/Sage-ERP-X3/xtrem/issues/15842))   ([a2e5cc3](https://github.com/Sage-ERP-X3/xtrem/commit/a2e5cc388820b8b16fa7903ef27fb67059ae9800))


## [40.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.8) (2024-01-24)

### Bug Fixes

### Features


## [40.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.7) (2024-01-23)

### Bug Fixes

### Features


## [40.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.6) (2024-01-22)

### Bug Fixes

### Features


## [40.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.5) (2024-01-21)

### Bug Fixes

### Features


## [40.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.4) (2024-01-20)

### Bug Fixes

### Features


## [40.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.3) (2024-01-19)

### Bug Fixes

### Features


## [40.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.2) (2024-01-18)

### Bug Fixes

### Features


## [40.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.1) (2024-01-18)

### Bug Fixes

### Features


## [40.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@40.0.0) (2024-01-18)

### Bug Fixes

### Features


## [39.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.31) (2024-01-17)

### Bug Fixes

### Features


## [39.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.30) (2024-01-17)

### Bug Fixes

### Features
* XT-62941 XT-62940 XT-62943 XT-60445 XT-60446 inter-app graphql calls ([XT-62941](https://jira.sage.com/browse/XT-62941); [XT-62940](https://jira.sage.com/browse/XT-62940); [XT-62943](https://jira.sage.com/browse/XT-62943); [XT-60445](https://jira.sage.com/browse/XT-60445); [XT-60446](https://jira.sage.com/browse/XT-60446)) ([#16487](https://github.com/Sage-ERP-X3/xtrem/issues/16487))   ([5823375](https://github.com/Sage-ERP-X3/xtrem/commit/58233758170f4f4f4d20db9941854ef36471e17d))


## [39.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.29) (2024-01-16)

### Bug Fixes

### Features


## [39.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.28) (2024-01-15)

### Bug Fixes
* typing of package config XT-62831 ([XT-62831](https://jira.sage.com/browse/XT-62831)) ([#16466](https://github.com/Sage-ERP-X3/xtrem/issues/16466))   ([5e2c5a2](https://github.com/Sage-ERP-X3/xtrem/commit/5e2c5a28a780c9ba3f815d99cd2e64cc458d038e))

### Features


## [39.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.27) (2024-01-14)

### Bug Fixes

### Features


## [39.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.26) (2024-01-13)

### Bug Fixes

### Features


## [39.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.25) (2024-01-12)

### Bug Fixes

### Features


## [39.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.24) (2024-01-12)

### Bug Fixes

### Features


## [39.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.23) (2024-01-11)

### Bug Fixes

### Features


## [39.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.22) (2024-01-10)

### Bug Fixes

### Features


## [39.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.21) (2024-01-09)

### Bug Fixes

### Features


## [39.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.20) (2024-01-08)

### Bug Fixes

### Features
* redos linting XT-62323 ([XT-62323](https://jira.sage.com/browse/XT-62323)) ([#16307](https://github.com/Sage-ERP-X3/xtrem/issues/16307))   ([490fddc](https://github.com/Sage-ERP-X3/xtrem/commit/490fddc3bbfde3f07a621805c09aa38fe224703a))


## [39.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.19) (2024-01-07)

### Bug Fixes

### Features


## [39.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.18) (2024-01-06)

### Bug Fixes

### Features
* XT-62396 MetaApp node ([XT-62396](https://jira.sage.com/browse/XT-62396)) ([#16327](https://github.com/Sage-ERP-X3/xtrem/issues/16327))   ([3289854](https://github.com/Sage-ERP-X3/xtrem/commit/328985495dbb9f01459a709f241cd62915700af5))


## [39.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.17) (2024-01-05)

### Bug Fixes

### Features


## [39.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.16) (2024-01-04)

### Bug Fixes

### Features


## [39.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.15) (2024-01-04)

### Bug Fixes

### Features


## [39.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.14) (2024-01-02)

### Bug Fixes

### Features


## [39.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.13) (2024-01-01)

### Bug Fixes

### Features


## [39.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.12) (2023-12-31)

### Bug Fixes

### Features


## [39.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.11) (2023-12-30)

### Bug Fixes

### Features


## [39.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.10) (2023-12-29)

### Bug Fixes

### Features


## [39.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.9) (2023-12-28)

### Bug Fixes

### Features


## [39.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.8) (2023-12-27)

### Bug Fixes

### Features


## [39.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.7) (2023-12-26)

### Bug Fixes

### Features


## [39.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.6) (2023-12-25)

### Bug Fixes

### Features


## [39.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.5) (2023-12-24)

### Bug Fixes

### Features


## [39.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.4) (2023-12-23)

### Bug Fixes

### Features


## [39.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.3) (2023-12-22)

### Bug Fixes

### Features
* XT-61848 remove abstract node and add sub node to routing.json and add asyncExport to subnode ([XT-61848](https://jira.sage.com/browse/XT-61848)) ([#16199](https://github.com/Sage-ERP-X3/xtrem/issues/16199))   ([88a0371](https://github.com/Sage-ERP-X3/xtrem/commit/88a037130a4430f23a8c4e20827764a11d45f5e7))


## [39.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.2) (2023-12-21)

### Bug Fixes

### Features


## [39.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.1) (2023-12-21)

### Bug Fixes

### Features


## [39.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@39.0.0) (2023-12-21)

### Bug Fixes

### Features


## [38.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.37) (2023-12-21)

### Bug Fixes

### Features


## [38.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.36) (2023-12-20)

### Bug Fixes

### Features
* XT-56278 MetaNodeDataType -> MetaDataType renaming ([XT-56278](https://jira.sage.com/browse/XT-56278)) ([#16169](https://github.com/Sage-ERP-X3/xtrem/issues/16169))   ([8de92e5](https://github.com/Sage-ERP-X3/xtrem/commit/8de92e5971a54f4535e0f05c65da75165085fd2d))


## [38.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.35) (2023-12-20)

### Bug Fixes

### Features


## [38.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.34) (2023-12-19)

### Bug Fixes

### Features


## [38.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.33) (2023-12-18)

### Bug Fixes

### Features


## [38.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.32) (2023-12-17)

### Bug Fixes

### Features


## [38.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.31) (2023-12-16)

### Bug Fixes

### Features


## [38.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.30) (2023-12-15)

### Bug Fixes

### Features


## [38.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.29) (2023-12-15)

### Bug Fixes
* vulnerable axios vesion upgrade   ([#16076](https://github.com/Sage-ERP-X3/xtrem/issues/16076))   ([f4f4eec](https://github.com/Sage-ERP-X3/xtrem/commit/f4f4eec3fa01b44d15efa2018592437c1dce30c5))

### Features


## [38.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.28) (2023-12-14)

### Bug Fixes

### Features
* **system:** add custom parameters node XT-56271 ([XT-56271](https://jira.sage.com/browse/XT-56271)) ([#15643](https://github.com/Sage-ERP-X3/xtrem/issues/15643))   ([559191f](https://github.com/Sage-ERP-X3/xtrem/commit/559191fa3bacff5000e0fbea257b51d21b74953f))


## [38.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.27) (2023-12-13)

### Bug Fixes

### Features


## [38.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.26) (2023-12-13)

### Bug Fixes

### Features


## [38.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.25) (2023-12-12)

### Bug Fixes

### Features


## [38.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.24) (2023-12-12)

### Bug Fixes

### Features


## [38.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.23) (2023-12-11)

### Bug Fixes

### Features


## [38.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.22) (2023-12-11)

### Bug Fixes

### Features
* XT-59054 Custom SQL scripts ([XT-59054](https://jira.sage.com/browse/XT-59054)) ([#15947](https://github.com/Sage-ERP-X3/xtrem/issues/15947))   ([44ac1b4](https://github.com/Sage-ERP-X3/xtrem/commit/44ac1b486fb90e9fdcfcd37927afbf9fd1236234))


## [38.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.21) (2023-12-10)

### Bug Fixes

### Features


## [38.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.20) (2023-12-09)

### Bug Fixes

### Features


## [38.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.19) (2023-12-08)

### Bug Fixes

### Features


## [38.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.18) (2023-12-07)

### Bug Fixes

### Features


## [38.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.17) (2023-12-06)

### Bug Fixes

### Features


## [38.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.16) (2023-12-05)

### Bug Fixes

### Features


## [38.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.15) (2023-12-04)

### Bug Fixes

### Features


## [38.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.14) (2023-12-03)

### Bug Fixes

### Features


## [38.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.13) (2023-12-02)

### Bug Fixes

### Features


## [38.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.12) (2023-12-01)

### Bug Fixes

### Features


## [38.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.11) (2023-12-01)

### Bug Fixes

### Features


## [38.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.10) (2023-11-30)

### Bug Fixes

### Features


## [38.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.9) (2023-11-30)

### Bug Fixes

### Features


## [38.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.8) (2023-11-29)

### Bug Fixes

### Features
* **custom-parameters:** add BaseParameter node XT-56271 ([XT-56271](https://jira.sage.com/browse/XT-56271)) ([#15804](https://github.com/Sage-ERP-X3/xtrem/issues/15804))   ([c591ced](https://github.com/Sage-ERP-X3/xtrem/commit/c591ced4e475e25585e80a8b28ab228885129e7e))


## [38.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.7) (2023-11-28)

### Bug Fixes

### Features


## [38.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.6) (2023-11-28)

### Bug Fixes

### Features


## [38.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.5) (2023-11-27)

### Bug Fixes

### Features


## [38.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.4) (2023-11-26)

### Bug Fixes

### Features


## [38.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.3) (2023-11-25)

### Bug Fixes

### Features


## [38.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.2) (2023-11-24)

### Bug Fixes

### Features


## [38.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.1) (2023-11-24)

### Bug Fixes

### Features


## [38.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@38.0.0) (2023-11-23)

### Bug Fixes

### Features


## [37.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.35) (2023-11-22)

### Bug Fixes

### Features


## [37.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.34) (2023-11-22)

### Bug Fixes

### Features
* XT-56265 Make upgrader of cli-layers compatible with SQL files ([XT-56265](https://jira.sage.com/browse/XT-56265)) ([#15631](https://github.com/Sage-ERP-X3/xtrem/issues/15631))   ([fc6c748](https://github.com/Sage-ERP-X3/xtrem/commit/fc6c74888fb64ee8ac7ec289bb725562fae5b6bf))


## [37.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.33) (2023-11-20)

### Bug Fixes

### Features


## [37.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.32) (2023-11-19)

### Bug Fixes

### Features


## [37.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.31) (2023-11-18)

### Bug Fixes

### Features


## [37.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.30) (2023-11-17)

### Bug Fixes

### Features


## [37.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.29) (2023-11-16)

### Bug Fixes

### Features
* XT-55911 GraphQL lookup access security ([XT-55911](https://jira.sage.com/browse/XT-55911)) ([#15366](https://github.com/Sage-ERP-X3/xtrem/issues/15366))   ([2ccc1a9](https://github.com/Sage-ERP-X3/xtrem/commit/2ccc1a9a4318c6a46b026af9bffcdd84dfc3ae6d))


## [37.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.28) (2023-11-16)

### Bug Fixes

### Features


## [37.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.27) (2023-11-16)

### Bug Fixes

### Features


## [37.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.26) (2023-11-15)

### Bug Fixes

### Features


## [37.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.25) (2023-11-15)

### Bug Fixes

### Features


## [37.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.24) (2023-11-14)

### Bug Fixes

### Features


## [37.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.23) (2023-11-13)

### Bug Fixes

### Features


## [37.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.22) (2023-11-12)

### Bug Fixes

### Features


## [37.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.21) (2023-11-11)

### Bug Fixes

### Features


## [37.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.20) (2023-11-10)

### Bug Fixes

### Features


## [37.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.19) (2023-11-10)

### Bug Fixes

### Features


## [37.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.18) (2023-11-10)

### Bug Fixes

### Features


## [37.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.17) (2023-11-09)

### Bug Fixes

### Features
* XT-56625 reformat SQL files ([XT-56625](https://jira.sage.com/browse/XT-56625)) ([#15501](https://github.com/Sage-ERP-X3/xtrem/issues/15501))   ([d704877](https://github.com/Sage-ERP-X3/xtrem/commit/d704877375976b973163ce4d9cd5f81522622c91))


## [37.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.16) (2023-11-08)

### Bug Fixes

### Features


## [37.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.15) (2023-11-08)

### Bug Fixes

### Features
* add turborepo & pnpm   ([#12887](https://github.com/Sage-ERP-X3/xtrem/issues/12887))   ([19f784f](https://github.com/Sage-ERP-X3/xtrem/commit/19f784f88afe1868d4dd4e3c7da235ddc5a37a08))


## [37.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.14) (2023-11-05)

### Bug Fixes

### Features


## [37.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.13) (2023-11-04)

### Bug Fixes

### Features


## [37.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.12) (2023-11-03)

### Bug Fixes

### Features


## [37.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.11) (2023-11-03)

### Bug Fixes

### Features


## [37.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.10) (2023-11-01)

### Bug Fixes

### Features


## [37.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.9) (2023-10-31)

### Bug Fixes

### Features


## [37.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.8) (2023-10-31)

### Bug Fixes

### Features


## [37.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.7) (2023-10-29)

### Bug Fixes

### Features


## [37.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.6) (2023-10-28)

### Bug Fixes

### Features


## [37.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.5) (2023-10-27)

### Bug Fixes

### Features


## [37.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.4) (2023-10-27)

### Bug Fixes

### Features


## [37.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.3) (2023-10-27)

### Bug Fixes

### Features


## [37.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.2) (2023-10-26)

### Bug Fixes

### Features


## [37.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.1) (2023-10-26)

### Bug Fixes

### Features


## [37.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@37.0.0) (2023-10-26)

### Bug Fixes

### Features


## [36.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.32) (2023-10-25)

### Bug Fixes

### Features


## [36.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.31) (2023-10-24)

### Bug Fixes

### Features


## [36.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.30) (2023-10-23)

### Bug Fixes

### Features


## [36.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.29) (2023-10-22)

### Bug Fixes

### Features


## [36.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.28) (2023-10-21)

### Bug Fixes

### Features


## [36.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.27) (2023-10-20)

### Bug Fixes

### Features


## [36.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.26) (2023-10-19)

### Bug Fixes

### Features


## [36.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.25) (2023-10-18)

### Bug Fixes

### Features


## [36.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.24) (2023-10-17)

### Bug Fixes

### Features


## [36.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.23) (2023-10-16)

### Bug Fixes

### Features


## [36.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.22) (2023-10-15)

### Bug Fixes

### Features


## [36.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.21) (2023-10-14)

### Bug Fixes

### Features


## [36.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.20) (2023-10-13)

### Bug Fixes

### Features


## [36.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.19) (2023-10-12)

### Bug Fixes

### Features


## [36.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.18) (2023-10-11)

### Bug Fixes

### Features


## [36.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.17) (2023-10-11)

### Bug Fixes

### Features


## [36.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.16) (2023-10-11)

### Bug Fixes

### Features


## [36.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.15) (2023-10-10)

### Bug Fixes

### Features


## [36.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.14) (2023-10-09)

### Bug Fixes

### Features


## [36.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.13) (2023-10-08)

### Bug Fixes

### Features


## [36.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.12) (2023-10-07)

### Bug Fixes

### Features


## [36.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.11) (2023-10-06)

### Bug Fixes

### Features


## [36.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.10) (2023-10-05)

### Bug Fixes

### Features


## [36.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.9) (2023-10-05)

### Bug Fixes

### Features


## [36.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.8) (2023-10-04)

### Bug Fixes

### Features


## [36.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.7) (2023-10-03)

### Bug Fixes

### Features
* **notifications:** delete old notifications job XT-54151 ([XT-54151](https://jira.sage.com/browse/XT-54151)) ([#14878](https://github.com/Sage-ERP-X3/xtrem/issues/14878))   ([7d6be76](https://github.com/Sage-ERP-X3/xtrem/commit/7d6be76139a9a33beff7e9b996c8c38a4399f9c5))
* XT-50680 add queue attribute to mutations decorators ([XT-50680](https://jira.sage.com/browse/XT-50680)) ([#14734](https://github.com/Sage-ERP-X3/xtrem/issues/14734))   ([3786559](https://github.com/Sage-ERP-X3/xtrem/commit/378655964894058d1f5d1a69eb38d4fa8260582f))


## [36.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.6) (2023-10-02)

### Bug Fixes
* get func name redos XT-55681 ([XT-55681](https://jira.sage.com/browse/XT-55681)) ([#14871](https://github.com/Sage-ERP-X3/xtrem/issues/14871))   ([872ff77](https://github.com/Sage-ERP-X3/xtrem/commit/872ff77795cb1271d7135dbd01780139226ad179))

### Features


## [36.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.5) (2023-10-01)

### Bug Fixes

### Features


## [36.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.4) (2023-09-30)

### Bug Fixes

### Features


## [36.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.3) (2023-09-29)

### Bug Fixes

### Features


## [36.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.2) (2023-09-28)

### Bug Fixes

### Features


## [36.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.1) (2023-09-28)

### Bug Fixes

### Features


## [36.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@36.0.0) (2023-09-28)

### Bug Fixes

### Features


## [35.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.28) (2023-09-27)

### Bug Fixes

### Features


## [35.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.27) (2023-09-26)

### Bug Fixes

### Features


## [35.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.26) (2023-09-25)

### Bug Fixes

### Features


## [35.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.25) (2023-09-24)

### Bug Fixes

### Features


## [35.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.24) (2023-09-23)

### Bug Fixes

### Features


## [35.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.23) (2023-09-22)

### Bug Fixes

### Features


## [35.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.22) (2023-09-22)

### Bug Fixes

### Features


## [35.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.21) (2023-09-20)

### Bug Fixes

### Features


## [35.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.20) (2023-09-19)

### Bug Fixes

### Features


## [35.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.19) (2023-09-18)

### Bug Fixes

### Features


## [35.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.18) (2023-09-17)

### Bug Fixes

### Features


## [35.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.17) (2023-09-16)

### Bug Fixes

### Features


## [35.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.16) (2023-09-15)

### Bug Fixes

### Features


## [35.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.15) (2023-09-14)

### Bug Fixes

### Features


## [35.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.14) (2023-09-13)

### Bug Fixes

### Features


## [35.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.13) (2023-09-12)

### Bug Fixes

### Features


## [35.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.12) (2023-09-11)

### Bug Fixes

### Features


## [35.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.11) (2023-09-10)

### Bug Fixes

### Features


## [35.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.10) (2023-09-09)

### Bug Fixes

### Features


## [35.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.9) (2023-09-08)

### Bug Fixes

### Features
* **notification:** add new client notifications node XT-54100 ([XT-54100](https://jira.sage.com/browse/XT-54100)) ([#14445](https://github.com/Sage-ERP-X3/xtrem/issues/14445))   ([cb49e66](https://github.com/Sage-ERP-X3/xtrem/commit/cb49e660d4ea218abdaa43c436927c711f4d8787))


## [35.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.8) (2023-09-07)

### Bug Fixes

### Features


## [35.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.7) (2023-09-06)

### Bug Fixes

### Features


## [35.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.6) (2023-09-06)

### Bug Fixes

### Features


## [35.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.5) (2023-09-05)

### Bug Fixes

### Features
* **xtrem-services:** XT-53368 rename Sage Intacct Manufacturing to Sage DMO ([XT-53368](https://jira.sage.com/browse/XT-53368)) ([#14311](https://github.com/Sage-ERP-X3/xtrem/issues/14311))   ([151d477](https://github.com/Sage-ERP-X3/xtrem/commit/151d477fc6c51e9404aae42e628b13e7b91ad24b))


## [35.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.4) (2023-09-04)

### Bug Fixes

### Features


## [35.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.3) (2023-09-03)

### Bug Fixes

### Features


## [35.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.2) (2023-09-02)

### Bug Fixes

### Features


## [35.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.1) (2023-09-01)

### Bug Fixes

### Features


## [35.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@35.0.0) (2023-09-01)

### Bug Fixes

### Features


## [34.0.40](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.40) (2023-09-01)

### Bug Fixes

### Features


## [34.0.39](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.39) (2023-08-31)

### Bug Fixes

### Features


## [34.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.38) (2023-08-31)

### Bug Fixes

### Features


## [34.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.37) (2023-08-30)

### Bug Fixes

### Features


## [34.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.36) (2023-08-29)

### Bug Fixes

### Features


## [34.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.35) (2023-08-28)

### Bug Fixes

### Features


## [34.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.34) (2023-08-28)

### Bug Fixes

### Features


## [34.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.33) (2023-08-27)

### Bug Fixes

### Features


## [34.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.32) (2023-08-26)

### Bug Fixes

### Features
* nyc to c8 migration   ([#14259](https://github.com/Sage-ERP-X3/xtrem/issues/14259))   ([1ce3941](https://github.com/Sage-ERP-X3/xtrem/commit/1ce3941ab544f6f7f42239c25932fa6c5265eefa))


## [34.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.31) (2023-08-25)

### Bug Fixes

### Features


## [34.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.30) (2023-08-25)

### Bug Fixes

### Features


## [34.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.29) (2023-08-23)

### Bug Fixes

### Features


## [34.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.28) (2023-08-22)

### Bug Fixes

### Features


## [34.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.27) (2023-08-21)

### Bug Fixes

### Features


## [34.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.26) (2023-08-20)

### Bug Fixes

### Features


## [34.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.25) (2023-08-19)

### Bug Fixes

### Features


## [34.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.24) (2023-08-18)

### Bug Fixes

### Features


## [34.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.23) (2023-08-17)

### Bug Fixes

### Features


## [34.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.22) (2023-08-17)

### Bug Fixes

### Features


## [34.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.21) (2023-08-16)

### Bug Fixes

### Features


## [34.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.20) (2023-08-15)

### Bug Fixes

### Features


## [34.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.19) (2023-08-14)

### Bug Fixes

### Features


## [34.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.18) (2023-08-13)

### Bug Fixes

### Features


## [34.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.17) (2023-08-12)

### Bug Fixes

### Features


## [34.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.16) (2023-08-11)

### Bug Fixes

### Features


## [34.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.15) (2023-08-10)

### Bug Fixes

### Features


## [34.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.14) (2023-08-09)

### Bug Fixes

### Features


## [34.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.13) (2023-08-08)

### Bug Fixes
* package and activity title localization XT-52100 ([XT-52100](https://jira.sage.com/browse/XT-52100)) ([#13942](https://github.com/Sage-ERP-X3/xtrem/issues/13942))   ([891aa52](https://github.com/Sage-ERP-X3/xtrem/commit/891aa527d1044334a4b9ce230bcecadbcc03509a))

### Features


## [34.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.12) (2023-08-06)

### Bug Fixes

### Features


## [34.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.11) (2023-08-05)

### Bug Fixes

### Features


## [34.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.10) (2023-08-04)

### Bug Fixes
* generate metadata and localization for activities (back-end side) XT-52100 ([XT-52100](https://jira.sage.com/browse/XT-52100)) ([#13925](https://github.com/Sage-ERP-X3/xtrem/issues/13925))   ([e58a105](https://github.com/Sage-ERP-X3/xtrem/commit/e58a105c5882161fff4aca916320edf24d808300))

### Features


## [34.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.9) (2023-08-03)

### Bug Fixes

### Features


## [34.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.8) (2023-08-02)

### Bug Fixes

### Features


## [34.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.7) (2023-08-01)

### Bug Fixes

### Features


## [34.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.6) (2023-07-31)

### Bug Fixes

### Features


## [34.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.5) (2023-07-30)

### Bug Fixes

### Features


## [34.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.4) (2023-07-29)

### Bug Fixes

### Features


## [34.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.3) (2023-07-28)

### Bug Fixes

### Features


## [34.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.2) (2023-07-27)

### Bug Fixes

### Features


## [34.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.1) (2023-07-27)

### Bug Fixes

### Features


## [34.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@34.0.0) (2023-07-27)

### Bug Fixes

### Features


## [33.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.28) (2023-07-26)

### Bug Fixes

### Features


## [33.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.27) (2023-07-25)

### Bug Fixes
* XT-48450 fixed capitalization of metadata titles in base.json files ([XT-48450](https://jira.sage.com/browse/XT-48450)) ([#13728](https://github.com/Sage-ERP-X3/xtrem/issues/13728))   ([3767009](https://github.com/Sage-ERP-X3/xtrem/commit/376700936005589831230a7c9fb1987a9615bca6))

### Features


## [33.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.26) (2023-07-24)

### Bug Fixes

### Features


## [33.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.25) (2023-07-23)

### Bug Fixes

### Features


## [33.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.24) (2023-07-22)

### Bug Fixes

### Features


## [33.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.23) (2023-07-21)

### Bug Fixes

### Features


## [33.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.22) (2023-07-20)

### Bug Fixes

### Features
* XT-48450 create i18n entries for service option names during build ([XT-48450](https://jira.sage.com/browse/XT-48450)) ([#13719](https://github.com/Sage-ERP-X3/xtrem/issues/13719))   ([31c8756](https://github.com/Sage-ERP-X3/xtrem/commit/31c8756e96e8cbba52351d9f31ad07cd1770687c))


## [33.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.21) (2023-07-19)

### Bug Fixes

### Features


## [33.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.20) (2023-07-18)

### Bug Fixes

### Features


## [33.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.19) (2023-07-17)

### Bug Fixes

### Features
* XT-37845 manage upgrade from prod backups ([XT-37845](https://jira.sage.com/browse/XT-37845)) ([#13613](https://github.com/Sage-ERP-X3/xtrem/issues/13613))   ([d71a4ed](https://github.com/Sage-ERP-X3/xtrem/commit/d71a4ed26eaa48016aa980ec8c8b55092b9664c4))


## [33.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.18) (2023-07-16)

### Bug Fixes

### Features


## [33.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.17) (2023-07-15)

### Bug Fixes

### Features


## [33.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.16) (2023-07-13)

### Bug Fixes

### Features


## [33.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.15) (2023-07-12)

### Bug Fixes

### Features


## [33.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.14) (2023-07-11)

### Bug Fixes
* **master-data:** XT-50244 fix access control ([XT-50244](https://jira.sage.com/browse/XT-50244)) ([#13510](https://github.com/Sage-ERP-X3/xtrem/issues/13510))   ([b0bb7af](https://github.com/Sage-ERP-X3/xtrem/commit/b0bb7af3eda273aa04ee58c9c3381f08faf99622))

### Features
* XT-46023 modify getNodeExportTemplates return type and rename asyncExport parameter ([XT-46023](https://jira.sage.com/browse/XT-46023)) ([#13588](https://github.com/Sage-ERP-X3/xtrem/issues/13588))   ([cac9108](https://github.com/Sage-ERP-X3/xtrem/commit/cac910868164d55186a58776f28a24bbfd33b15e))


## [33.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.13) (2023-07-10)

### Bug Fixes

### Features


## [33.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.12) (2023-07-09)

### Bug Fixes

### Features


## [33.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.11) (2023-07-08)

### Bug Fixes

### Features


## [33.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.10) (2023-07-07)

### Bug Fixes

### Features


## [33.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.9) (2023-07-06)

### Bug Fixes

### Features
* XT-46023 settle async export frame ([XT-46023](https://jira.sage.com/browse/XT-46023)) ([#13437](https://github.com/Sage-ERP-X3/xtrem/issues/13437))   ([7fd6fd8](https://github.com/Sage-ERP-X3/xtrem/commit/7fd6fd8d72609d3c8e073cb2ce461d2f8c187551))


## [33.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.8) (2023-07-05)

### Bug Fixes

### Features


## [33.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.7) (2023-07-04)

### Bug Fixes

### Features


## [33.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.6) (2023-07-03)

### Bug Fixes

### Features
* **scheduler:** Scheduler page & wizard   XT-48282 ([XT-48282](https://jira.sage.com/browse/XT-48282)) ([#13433](https://github.com/Sage-ERP-X3/xtrem/issues/13433))   ([12ba2e1](https://github.com/Sage-ERP-X3/xtrem/commit/12ba2e1cc357dd4b6dcbd5c5924137341e46b153))


## [33.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.5) (2023-07-02)

### Bug Fixes

### Features


## [33.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.4) (2023-07-01)

### Bug Fixes

### Features


## [33.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.3) (2023-06-30)

### Bug Fixes

### Features


## [33.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.2) (2023-06-29)

### Bug Fixes

### Features


## [33.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.1) (2023-06-29)

### Bug Fixes

### Features


## [33.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@33.0.0) (2023-06-29)

### Bug Fixes

### Features


## [32.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.37) (2023-06-28)

### Bug Fixes

### Features


## [32.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.36) (2023-06-27)

### Bug Fixes

### Features


## [32.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.35) (2023-06-26)

### Bug Fixes

### Features


## [32.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.34) (2023-06-25)

### Bug Fixes

### Features


## [32.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.33) (2023-06-24)

### Bug Fixes

### Features


## [32.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.32) (2023-06-23)

### Bug Fixes

### Features


## [32.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.31) (2023-06-22)

### Bug Fixes

### Features


## [32.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.30) (2023-06-21)

### Bug Fixes
* XT-48923 Remove setup data ([XT-48923](https://jira.sage.com/browse/XT-48923)) ([#13243](https://github.com/Sage-ERP-X3/xtrem/issues/13243))   ([154506f](https://github.com/Sage-ERP-X3/xtrem/commit/154506f9c89a09225237d2725c0701e85ffdb4b6))

### Features


## [32.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.29) (2023-06-20)

### Bug Fixes

### Features


## [32.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.28) (2023-06-19)

### Bug Fixes

### Features


## [32.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.27) (2023-06-18)

### Bug Fixes

### Features


## [32.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.26) (2023-06-17)

### Bug Fixes

### Features


## [32.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.25) (2023-06-16)

### Bug Fixes

### Features


## [32.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.24) (2023-06-15)

### Bug Fixes

### Features


## [32.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.23) (2023-06-14)

### Bug Fixes

### Features


## [32.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.22) (2023-06-13)

### Bug Fixes

### Features


## [32.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.21) (2023-06-12)

### Bug Fixes
* do not disable certificate validation in x3 gateway XT-43869 ([XT-43869](https://jira.sage.com/browse/XT-43869)) ([#13145](https://github.com/Sage-ERP-X3/xtrem/issues/13145))   ([d5faad5](https://github.com/Sage-ERP-X3/xtrem/commit/d5faad5e4b70e91002893dfdcf776ad1ecc4d482))

### Features


## [32.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.20) (2023-06-11)

### Bug Fixes

### Features


## [32.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.19) (2023-06-10)

### Bug Fixes

### Features


## [32.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.18) (2023-06-09)

### Bug Fixes

### Features


## [32.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.17) (2023-06-08)

### Bug Fixes

### Features


## [32.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.16) (2023-06-07)

### Bug Fixes

### Features


## [32.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.15) (2023-06-07)

### Bug Fixes

### Features
* **scheduler:** POC Notification center XT-47813 ([XT-47813](https://jira.sage.com/browse/XT-47813)) ([#13010](https://github.com/Sage-ERP-X3/xtrem/issues/13010))   ([4880261](https://github.com/Sage-ERP-X3/xtrem/commit/4880261bf922c142dd5383eade34d79f51e4361c))


## [32.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.14) (2023-06-06)

### Bug Fixes

### Features


## [32.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.13) (2023-06-05)

### Bug Fixes

### Features


## [32.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.12) (2023-06-04)

### Bug Fixes

### Features


## [32.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.11) (2023-06-03)

### Bug Fixes

### Features


## [32.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.10) (2023-06-02)

### Bug Fixes

### Features


## [32.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.9) (2023-06-01)

### Bug Fixes

### Features


## [32.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.8) (2023-05-31)

### Bug Fixes

### Features


## [32.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.7) (2023-05-30)

### Bug Fixes

### Features


## [32.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.6) (2023-05-29)

### Bug Fixes

### Features


## [32.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.5) (2023-05-28)

### Bug Fixes

### Features


## [32.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.4) (2023-05-27)

### Bug Fixes

### Features


## [32.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.3) (2023-05-26)

### Bug Fixes

### Features
* **batch:** Can delete notification state   ([#12893](https://github.com/Sage-ERP-X3/xtrem/issues/12893))   ([acf1cf6](https://github.com/Sage-ERP-X3/xtrem/commit/acf1cf6e5a84d0e3c89ef5148ace30f080c483d4))


## [32.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.2) (2023-05-25)

### Bug Fixes

### Features


## [32.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.1) (2023-05-25)

### Bug Fixes

### Features


## [32.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@32.0.0) (2023-05-25)

### Bug Fixes

### Features


## [31.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.32) (2023-05-24)

### Bug Fixes

### Features


## [31.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.31) (2023-05-24)

### Bug Fixes

### Features


## [31.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.30) (2023-05-22)

### Bug Fixes

### Features


## [31.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.29) (2023-05-21)

### Bug Fixes

### Features


## [31.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.28) (2023-05-20)

### Bug Fixes

### Features


## [31.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.27) (2023-05-19)

### Bug Fixes

### Features


## [31.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.26) (2023-05-18)

### Bug Fixes

### Features


## [31.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.25) (2023-05-17)

### Bug Fixes

### Features


## [31.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.24) (2023-05-17)

### Bug Fixes

### Features


## [31.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.23) (2023-05-15)

### Bug Fixes

### Features


## [31.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.22) (2023-05-15)

### Bug Fixes
* XT-46395 for widget_category set id to old setup_id ([XT-46395](https://jira.sage.com/browse/XT-46395)) ([#12746](https://github.com/Sage-ERP-X3/xtrem/issues/12746))   ([c8ecc2f](https://github.com/Sage-ERP-X3/xtrem/commit/c8ecc2f2b66c310c86a6c71f9f3eb2e96dec0794))
* context without email must throw an error XT-46443 ([XT-46443](https://jira.sage.com/browse/XT-46443)) ([#12697](https://github.com/Sage-ERP-X3/xtrem/issues/12697))   ([04c9700](https://github.com/Sage-ERP-X3/xtrem/commit/04c9700274db7952d91b1373cfa04f65ef0781aa))

### Features


## [31.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.21) (2023-05-15)

### Bug Fixes

### Features


## [31.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.20) (2023-05-13)

### Bug Fixes

### Features


## [31.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.19) (2023-05-12)

### Bug Fixes

### Features


## [31.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.18) (2023-05-12)

### Bug Fixes

### Features


## [31.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.17) (2023-05-11)

### Bug Fixes

### Features


## [31.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.16) (2023-05-11)

### Bug Fixes

### Features


## [31.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.15) (2023-05-10)

### Bug Fixes

### Features


## [31.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.14) (2023-05-09)

### Bug Fixes

### Features


## [31.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.13) (2023-05-08)

### Bug Fixes

### Features


## [31.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.12) (2023-05-07)

### Bug Fixes

### Features


## [31.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.11) (2023-05-06)

### Bug Fixes

### Features


## [31.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.10) (2023-05-05)

### Bug Fixes

### Features


## [31.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.9) (2023-05-04)

### Bug Fixes

### Features


## [31.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.8) (2023-05-03)

### Bug Fixes

### Features
* batching/XT-41162 operation localisation ([XT-41162](https://jira.sage.com/browse/XT-41162)) ([#12516](https://github.com/Sage-ERP-X3/xtrem/issues/12516))   ([76482f5](https://github.com/Sage-ERP-X3/xtrem/commit/76482f509af9c5c3c11ef5ab27faa9ab74a76aed))


## [31.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.7) (2023-05-02)

### Bug Fixes

### Features


## [31.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.6) (2023-05-01)

### Bug Fixes

### Features


## [31.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.5) (2023-04-30)

### Bug Fixes

### Features


## [31.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.4) (2023-04-29)

### Bug Fixes
* XT-46227 fixed misused promises ([XT-46227](https://jira.sage.com/browse/XT-46227)) ([#12570](https://github.com/Sage-ERP-X3/xtrem/issues/12570))   ([23a8e10](https://github.com/Sage-ERP-X3/xtrem/commit/23a8e10e4f70c32cc6b36ee614e01b24ddd7249a))

### Features


## [31.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.3) (2023-04-28)

### Bug Fixes

### Features
* **scheduler:** Mutation for purging notification history XT-45920 ([XT-45920](https://jira.sage.com/browse/XT-45920)) ([#12487](https://github.com/Sage-ERP-X3/xtrem/issues/12487))   ([0f02fdc](https://github.com/Sage-ERP-X3/xtrem/commit/0f02fdce93a540c86f4bca5ad4c27fd9f671d113))
* **scheduler:** confirm stop   ([#12535](https://github.com/Sage-ERP-X3/xtrem/issues/12535))   ([ed453d6](https://github.com/Sage-ERP-X3/xtrem/commit/ed453d61a131e9e35a7f924156964c8a919aa1a5))


## [31.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.2) (2023-04-27)

### Bug Fixes

### Features


## [31.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.1) (2023-04-27)

### Bug Fixes

### Features


## [31.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@31.0.0) (2023-04-27)

### Bug Fixes

### Features


## [30.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.32) (2023-04-27)

### Bug Fixes

### Features


## [30.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.31) (2023-04-26)

### Bug Fixes

### Features


## [30.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.30) (2023-04-25)

### Bug Fixes

### Features


## [30.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.29) (2023-04-24)

### Bug Fixes

### Features


## [30.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.28) (2023-04-23)

### Bug Fixes

### Features


## [30.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.27) (2023-04-22)

### Bug Fixes

### Features


## [30.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.26) (2023-04-21)

### Bug Fixes

### Features


## [30.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.25) (2023-04-20)

### Bug Fixes

### Features


## [30.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.24) (2023-04-20)

### Bug Fixes

### Features


## [30.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.23) (2023-04-20)

### Bug Fixes
* menu item and message verification XT-44766 ([XT-44766](https://jira.sage.com/browse/XT-44766)) ([#12369](https://github.com/Sage-ERP-X3/xtrem/issues/12369))   ([8d06746](https://github.com/Sage-ERP-X3/xtrem/commit/8d06746c44fe51b93bfa5a9510824df86ff44be4))

### Features


## [30.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.22) (2023-04-18)

### Bug Fixes

### Features


## [30.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.21) (2023-04-17)

### Bug Fixes

### Features


## [30.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.20) (2023-04-16)

### Bug Fixes

### Features


## [30.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.19) (2023-04-15)

### Bug Fixes

### Features


## [30.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.18) (2023-04-14)

### Bug Fixes

### Features


## [30.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.17) (2023-04-13)

### Bug Fixes

### Features


## [30.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.16) (2023-04-12)

### Bug Fixes

### Features


## [30.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.15) (2023-04-11)

### Bug Fixes

### Features


## [30.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.14) (2023-04-10)

### Bug Fixes

### Features


## [30.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.13) (2023-04-09)

### Bug Fixes

### Features


## [30.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.12) (2023-04-08)

### Bug Fixes

### Features


## [30.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.11) (2023-04-07)

### Bug Fixes

### Features


## [30.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.10) (2023-04-07)

### Bug Fixes

### Features


## [30.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.9) (2023-04-06)

### Bug Fixes

### Features


## [30.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.8) (2023-04-05)

### Bug Fixes

### Features


## [30.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.7) (2023-04-04)

### Bug Fixes
* XT-44762 fix class name in topic of async mutation defined in an extension node ([XT-44762](https://jira.sage.com/browse/XT-44762)) ([#12187](https://github.com/Sage-ERP-X3/xtrem/issues/12187))   ([8ba1465](https://github.com/Sage-ERP-X3/xtrem/commit/8ba146553d26a6e6f8feb873933e70c088881389))

### Features


## [30.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.6) (2023-04-03)

### Bug Fixes

### Features


## [30.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.5) (2023-04-02)

### Bug Fixes

### Features


## [30.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.4) (2023-04-01)

### Bug Fixes

### Features


## [30.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.3) (2023-03-31)

### Bug Fixes

### Features
* **communication:** new node SysNotificationState XT-41930 ([XT-41930](https://jira.sage.com/browse/XT-41930)) ([#11406](https://github.com/Sage-ERP-X3/xtrem/issues/11406))   ([5173174](https://github.com/Sage-ERP-X3/xtrem/commit/51731748b06c4cbde137e5c21093b3f31b053358))


## [30.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.2) (2023-03-30)

### Bug Fixes

### Features


## [30.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.1) (2023-03-30)

### Bug Fixes

### Features


## [30.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@30.0.0) (2023-03-30)

### Bug Fixes

### Features


## [29.0.39](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.39) (2023-03-30)

### Bug Fixes

### Features


## [29.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.38) (2023-03-29)

### Bug Fixes

### Features


## [29.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.37) (2023-03-28)

### Bug Fixes

### Features


## [29.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.36) (2023-03-27)

### Bug Fixes

### Features


## [29.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.35) (2023-03-27)

### Bug Fixes

### Features


## [29.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.34) (2023-03-24)

### Bug Fixes

### Features


## [29.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.33) (2023-03-23)

### Bug Fixes

### Features


## [29.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.32) (2023-03-23)

### Bug Fixes

### Features


## [29.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.31) (2023-03-22)

### Bug Fixes

### Features
* npm 9, workspaces and lerna 6 (XT-34450) ([XT-34450](https://jira.sage.com/browse/XT-34450)) ([#11665](https://github.com/Sage-ERP-X3/xtrem/issues/11665))   ([030197a](https://github.com/Sage-ERP-X3/xtrem/commit/030197af8498950fe5721a5165def8b3792ff789))


## [29.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.30) (2023-03-21)

### Bug Fixes

### Features
* XT-43722 Add simulated async mutation ([XT-43722](https://jira.sage.com/browse/XT-43722)) ([#11950](https://github.com/Sage-ERP-X3/xtrem/issues/11950))   ([a375fef](https://github.com/Sage-ERP-X3/xtrem/commit/a375fef8c5ea6a457dac15a5f681c482315a0d3b))


## [29.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.29) (2023-03-20)

### Bug Fixes

### Features


## [29.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.28) (2023-03-19)

### Bug Fixes

### Features


## [29.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.27) (2023-03-18)

### Bug Fixes

### Features


## [29.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.26) (2023-03-17)

### Bug Fixes

### Features


## [29.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.25) (2023-03-16)

### Bug Fixes

### Features
* **manufacturing:** X3-289599 add widget ([X3-289599](https://jira.sage.com/browse/X3-289599)) ([#11771](https://github.com/Sage-ERP-X3/xtrem/issues/11771))   ([720c854](https://github.com/Sage-ERP-X3/xtrem/commit/720c85484804d49c586e339fcaea4cc9472de576))


## [29.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.24) (2023-03-15)

### Bug Fixes

### Features
* XT-2582 first pass on async mutation stop ([XT-2582](https://jira.sage.com/browse/XT-2582)) ([#11809](https://github.com/Sage-ERP-X3/xtrem/issues/11809))   ([ca3cab7](https://github.com/Sage-ERP-X3/xtrem/commit/ca3cab75301e65a2801d77c9e9338dc0ecede6cd))


## [29.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.23) (2023-03-15)

### Bug Fixes

### Features
* dedicated package for upload (XT-42599) ([XT-42599](https://jira.sage.com/browse/XT-42599)) ([#11721](https://github.com/Sage-ERP-X3/xtrem/issues/11721))   ([c5b596f](https://github.com/Sage-ERP-X3/xtrem/commit/c5b596f8a1e6e030764742ce20ff918d3eab0556))


## [29.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.22) (2023-03-13)

### Bug Fixes

### Features
* XT-37312 convert bulk mutation to async mutation ([XT-37312](https://jira.sage.com/browse/XT-37312)) ([#11772](https://github.com/Sage-ERP-X3/xtrem/issues/11772))   ([f4118a9](https://github.com/Sage-ERP-X3/xtrem/commit/f4118a9621c2be23ade0cd7dc38ba110c7295e37))


## [29.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.21) (2023-03-12)

### Bug Fixes

### Features


## [29.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.20) (2023-03-11)

### Bug Fixes

### Features


## [29.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.19) (2023-03-10)

### Bug Fixes

### Features


## [29.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.18) (2023-03-10)

### Bug Fixes

### Features


## [29.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.17) (2023-03-08)

### Bug Fixes

### Features
* **metaData:** Pages & replace jobDefinition / scheduler  XT-18415 ([XT-18415](https://jira.sage.com/browse/XT-18415)) ([#11568](https://github.com/Sage-ERP-X3/xtrem/issues/11568))   ([ead2ee4](https://github.com/Sage-ERP-X3/xtrem/commit/ead2ee46cfaa975167da94d5969e123b1380960d))


## [29.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.16) (2023-03-07)

### Bug Fixes

### Features


## [29.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.15) (2023-03-07)

### Bug Fixes

### Features


## [29.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.14) (2023-03-07)

### Bug Fixes

### Features
* **data-management:** fix load vendor setup data XT-42193 ([XT-42193](https://jira.sage.com/browse/XT-42193)) ([#11578](https://github.com/Sage-ERP-X3/xtrem/issues/11578))   ([4bd7ff1](https://github.com/Sage-ERP-X3/xtrem/commit/4bd7ff1a6954d7d0e79e0974e6536d5a96c762c9))


## [29.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.13) (2023-03-06)

### Bug Fixes

### Features


## [29.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.12) (2023-03-05)

### Bug Fixes

### Features


## [29.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.11) (2023-03-04)

### Bug Fixes

### Features


## [29.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.10) (2023-03-03)

### Bug Fixes

### Features


## [29.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.9) (2023-03-02)

### Bug Fixes

### Features


## [29.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.8) (2023-03-01)

### Bug Fixes

### Features


## [29.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.7) (2023-02-28)

### Bug Fixes

### Features


## [29.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.6) (2023-02-27)

### Bug Fixes

### Features


## [29.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.5) (2023-02-26)

### Bug Fixes

### Features


## [29.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.4) (2023-02-25)

### Bug Fixes

### Features


## [29.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.3) (2023-02-24)

### Bug Fixes

### Features
* **shopfloor:** X3-288588 shop floor create time tracking x3 ([X3-288588](https://jira.sage.com/browse/X3-288588)) ([#11481](https://github.com/Sage-ERP-X3/xtrem/issues/11481))   ([e19b95b](https://github.com/Sage-ERP-X3/xtrem/commit/e19b95b79d45daa79a1a441295e2eb024cca2f62))


## [29.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.2) (2023-02-23)

### Bug Fixes

### Features
* XT-42146 fix release-patch ([XT-42146](https://jira.sage.com/browse/XT-42146)) ([#11474](https://github.com/Sage-ERP-X3/xtrem/issues/11474))   ([bdef4b0](https://github.com/Sage-ERP-X3/xtrem/commit/bdef4b06fa1849b65f44da7fc71ef13217db12e4))


## [29.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.1) (2023-02-23)

### Bug Fixes

### Features
* XT-42146 fix release-patch ([XT-42146](https://jira.sage.com/browse/XT-42146)) ([#11466](https://github.com/Sage-ERP-X3/xtrem/issues/11466))   ([45e5676](https://github.com/Sage-ERP-X3/xtrem/commit/45e5676e39dc6a78d562126591bc5eb80d80bde4))


## [29.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@29.0.0) (2023-02-23)

### Bug Fixes

### Features


## [28.0.46](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.46) (2023-02-22)

### Bug Fixes

### Features


## [28.0.45](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.45) (2023-02-22)

### Bug Fixes

### Features


## [28.0.44](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.44) (2023-02-21)

### Bug Fixes

### Features


## [28.0.43](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.43) (2023-02-21)

### Bug Fixes

### Features


## [28.0.42](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.42) (2023-02-20)

### Bug Fixes

### Features


## [28.0.41](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.41) (2023-02-19)

### Bug Fixes

### Features


## [28.0.40](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.40) (2023-02-18)

### Bug Fixes

### Features


## [28.0.39](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.39) (2023-02-17)

### Bug Fixes

### Features


## [28.0.38](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.38) (2023-02-16)

### Bug Fixes

### Features


## [28.0.37](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.37) (2023-02-15)

### Bug Fixes

### Features


## [28.0.36](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.36) (2023-02-14)

### Bug Fixes

### Features


## [28.0.35](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.35) (2023-02-13)

### Bug Fixes

### Features
* carbon update to 114.11 XT-37144 ([XT-37144](https://jira.sage.com/browse/XT-37144)) ([#11199](https://github.com/Sage-ERP-X3/xtrem/issues/11199))   ([bb3f1cd](https://github.com/Sage-ERP-X3/xtrem/commit/bb3f1cdc64b5c089fc04911d3b1972bd27fd43c0))


## [28.0.34](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.34) (2023-02-12)

### Bug Fixes

### Features


## [28.0.33](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.33) (2023-02-11)

### Bug Fixes

### Features


## [28.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.32) (2023-02-10)

### Bug Fixes

### Features


## [28.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.31) (2023-02-09)

### Bug Fixes

### Features


## [28.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.30) (2023-02-08)

### Bug Fixes

### Features


## [28.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.29) (2023-02-07)

### Bug Fixes

### Features


## [28.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.28) (2023-02-06)

### Bug Fixes

### Features


## [28.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.27) (2023-02-05)

### Bug Fixes

### Features


## [28.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.26) (2023-02-04)

### Bug Fixes

### Features


## [28.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.25) (2023-02-03)

### Bug Fixes

### Features


## [28.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.24) (2023-02-03)

### Bug Fixes

### Features


## [28.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.23) (2023-02-02)

### Bug Fixes

### Features
* **shopfloor:** X3-288589 identify tracking record sent to tenant ([X3-288589](https://jira.sage.com/browse/X3-288589)) ([#11013](https://github.com/Sage-ERP-X3/xtrem/issues/11013))   ([416631f](https://github.com/Sage-ERP-X3/xtrem/commit/416631fff3f225766127b78ed18283fb16bb046c))


## [28.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.22) (2023-02-01)

### Bug Fixes

### Features


## [28.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.21) (2023-02-01)

### Bug Fixes

### Features


## [28.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.20) (2023-01-31)

### Bug Fixes

### Features
* XT-39495 refactor system upgrades ([XT-39495](https://jira.sage.com/browse/XT-39495)) ([#10971](https://github.com/Sage-ERP-X3/xtrem/issues/10971))   ([16fc65e](https://github.com/Sage-ERP-X3/xtrem/commit/16fc65eacd96d03354ca442b39a6d5b4f1099645))


## [28.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.19) (2023-01-30)

### Bug Fixes

### Features


## [28.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.18) (2023-01-29)

### Bug Fixes

### Features


## [28.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.17) (2023-01-28)

### Bug Fixes

### Features


## [28.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.16) (2023-01-27)

### Bug Fixes

### Features


## [28.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.15) (2023-01-26)

### Bug Fixes

### Features


## [28.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.14) (2023-01-25)

### Bug Fixes

### Features
* **shopfloor:** X3-293072 allow summary and detail tracking in sim ([X3-293072](https://jira.sage.com/browse/X3-293072)) ([#10918](https://github.com/Sage-ERP-X3/xtrem/issues/10918))   ([e0916bc](https://github.com/Sage-ERP-X3/xtrem/commit/e0916bcf5d3b18059e65d998ae34e41f96eb32eb))


## [28.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.13) (2023-01-24)

### Bug Fixes
* typo in UploadedFile/InfrastructureComplete topic   ([#10891](https://github.com/Sage-ERP-X3/xtrem/issues/10891))   ([2c32010](https://github.com/Sage-ERP-X3/xtrem/commit/2c320102c6ff5ae2b259115b048f445c070edd50))

### Features


## [28.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.12) (2023-01-23)

### Bug Fixes

### Features


## [28.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.11) (2023-01-22)

### Bug Fixes

### Features


## [28.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.10) (2023-01-21)

### Bug Fixes

### Features


## [28.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.9) (2023-01-20)

### Bug Fixes

### Features
* **shopfloor:** X3-291161 prepare data for mutation ([X3-291161](https://jira.sage.com/browse/X3-291161)) ([#10777](https://github.com/Sage-ERP-X3/xtrem/issues/10777))   ([9f00818](https://github.com/Sage-ERP-X3/xtrem/commit/9f00818ea3e1b78d6281cf36655ed0d14ac7aa91))


## [28.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.8) (2023-01-19)

### Bug Fixes

### Features


## [28.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.7) (2023-01-18)

### Bug Fixes

### Features


## [28.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.6) (2023-01-17)

### Bug Fixes
* i18n newline at end XT-39254 ([XT-39254](https://jira.sage.com/browse/XT-39254)) ([#10732](https://github.com/Sage-ERP-X3/xtrem/issues/10732))   ([83d79fc](https://github.com/Sage-ERP-X3/xtrem/commit/83d79fce1f6f7c65bd5222ff3129fad8a765940e))

### Features


## [28.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.5) (2023-01-16)

### Bug Fixes

### Features
* **shopfloor:** X3-288766 Shop floor populate shop floor create time tracking SIM ([X3-288766](https://jira.sage.com/browse/X3-288766)) ([#10708](https://github.com/Sage-ERP-X3/xtrem/issues/10708))   ([5c47f19](https://github.com/Sage-ERP-X3/xtrem/commit/5c47f1981e6fd4e9ca86c32f535e2d70fd8b5ade))


## [28.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.4) (2023-01-15)

### Bug Fixes

### Features


## [28.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.3) (2023-01-14)

### Bug Fixes

### Features


## [28.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.2) (2023-01-13)

### Bug Fixes

### Features


## [28.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.1) (2023-01-12)

### Bug Fixes

### Features
* row actions renamed to dropdown actions XT-37646 ([XT-37646](https://jira.sage.com/browse/XT-37646)) ([#10666](https://github.com/Sage-ERP-X3/xtrem/issues/10666))   ([d63ce88](https://github.com/Sage-ERP-X3/xtrem/commit/d63ce88e68ae8456da3b22beade9f17d32bd059e))


## [28.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@28.0.0) (2023-01-12)

### Bug Fixes

### Features


## [27.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.21) (2023-01-12)

### Bug Fixes

### Features


## [27.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.20) (2023-01-11)

### Bug Fixes

### Features


## [27.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.19) (2023-01-10)

### Bug Fixes

### Features


## [27.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.18) (2023-01-10)

### Bug Fixes

### Features


## [27.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.17) (2023-01-09)

### Bug Fixes

### Features


## [27.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.16) (2023-01-08)

### Bug Fixes

### Features


## [27.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.15) (2023-01-07)

### Bug Fixes

### Features


## [27.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.14) (2023-01-06)

### Bug Fixes

### Features


## [27.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.13) (2023-01-05)

### Bug Fixes

### Features


## [27.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.12) (2023-01-04)

### Bug Fixes

### Features
* **shopfloor:** X3-291162 EOD ([X3-291162](https://jira.sage.com/browse/X3-291162)) ([#10524](https://github.com/Sage-ERP-X3/xtrem/issues/10524))   ([5ec7cc0](https://github.com/Sage-ERP-X3/xtrem/commit/5ec7cc02e47333c117388c7ffe3ca08c1b23f6a3))


## [27.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.11) (2023-01-03)

### Bug Fixes
* security patches XT-38525 ([XT-38525](https://jira.sage.com/browse/XT-38525)) ([#10489](https://github.com/Sage-ERP-X3/xtrem/issues/10489))   ([d2b29fd](https://github.com/Sage-ERP-X3/xtrem/commit/d2b29fd2a6cb4dd1c87d75135a06b1997740e4a7))

### Features


## [27.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.10) (2023-01-02)

### Bug Fixes

### Features


## [27.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.9) (2023-01-01)

### Bug Fixes

### Features


## [27.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.8) (2022-12-31)

### Bug Fixes

### Features


## [27.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.7) (2022-12-30)

### Bug Fixes

### Features


## [27.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.6) (2022-12-29)

### Bug Fixes

### Features


## [27.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.5) (2022-12-28)

### Bug Fixes

### Features


## [27.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.4) (2022-12-27)

### Bug Fixes

### Features


## [27.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.3) (2022-12-26)

### Bug Fixes

### Features


## [27.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.2) (2022-12-26)

### Bug Fixes

### Features


## [27.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.1) (2022-12-23)

### Bug Fixes

### Features


## [27.0.0](https://github.com/compare/...@sage/xtrem~shopfloor@27.0.0) (2022-12-23)

### Bug Fixes

### Features


## [26.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.28) (2022-12-22)

### Bug Fixes

### Features


## [26.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.27) (2022-12-21)

### Bug Fixes

### Features


## [26.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.26) (2022-12-20)

### Bug Fixes

### Features
* added crud action state updates   ([#10377](https://github.com/Sage-ERP-X3/xtrem/issues/10377))   ([b0c46c7](https://github.com/Sage-ERP-X3/xtrem/commit/b0c46c7638f0ff2c36e598951be5c552149ab0fe))


## [26.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.25) (2022-12-20)

### Bug Fixes

### Features


## [26.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.24) (2022-12-18)

### Bug Fixes

### Features


## [26.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.23) (2022-12-17)

### Bug Fixes

### Features


## [26.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.22) (2022-12-16)

### Bug Fixes

### Features


## [26.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.21) (2022-12-15)

### Bug Fixes

### Features


## [26.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.20) (2022-12-14)

### Bug Fixes

### Features
* **shopfloor:** X3-290455 Shop floor populate operation data from X3 ([X3-290455](https://jira.sage.com/browse/X3-290455)) ([#10322](https://github.com/Sage-ERP-X3/xtrem/issues/10322))   ([d3ceaa6](https://github.com/Sage-ERP-X3/xtrem/commit/d3ceaa66494d158836321f5c342b41d630b8922c))
* **core:** XT-36830 - Tsconfig-paths version upgrade. ([XT-36830](https://jira.sage.com/browse/XT-36830)) ([#10287](https://github.com/Sage-ERP-X3/xtrem/issues/10287))   ([47ca0b4](https://github.com/Sage-ERP-X3/xtrem/commit/47ca0b4061872b9212b736035f3f9944b28e382d))


## [26.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.19) (2022-12-13)

### Bug Fixes

### Features
* **shopfloor:** X3-290455 Shop floor populate operation data from SIM ([X3-290455](https://jira.sage.com/browse/X3-290455)) ([#10285](https://github.com/Sage-ERP-X3/xtrem/issues/10285))   ([cbfde65](https://github.com/Sage-ERP-X3/xtrem/commit/cbfde657db94df6abd320d0101df8e135b05f042))
* **sim-gateway:** X3-289599 add mock data requests ([X3-289599](https://jira.sage.com/browse/X3-289599)) ([#10279](https://github.com/Sage-ERP-X3/xtrem/issues/10279))   ([eb1178d](https://github.com/Sage-ERP-X3/xtrem/commit/eb1178de6330c8340d45dab7739cdb3d31e5dc9f))


## [26.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.18) (2022-12-12)

### Bug Fixes
* **scripts:** XT-34673 - improve changelog update speed ([XT-34673](https://jira.sage.com/browse/XT-34673)) ([#10038](https://github.com/Sage-ERP-X3/xtrem/issues/10038))   ([863033a](https://github.com/Sage-ERP-X3/xtrem/commit/863033a16edab142c8d7d9e6f14c5554979964a0))

### Features
* **shopfloor:** X3-289791 labour resource ([X3-289791](https://jira.sage.com/browse/X3-289791)) ([#10263](https://github.com/Sage-ERP-X3/xtrem/issues/10263))   ([a547e6c](https://github.com/Sage-ERP-X3/xtrem/commit/a547e6ccb90b0e5c472bc547889357fcfc85bdb4))
* new action structure XT-28176 ([XT-28176](https://jira.sage.com/browse/XT-28176)) ([#10215](https://github.com/Sage-ERP-X3/xtrem/issues/10215))   ([4de5cbf](https://github.com/Sage-ERP-X3/xtrem/commit/4de5cbff5c2c9b9889515a86bc9ac080fcc59909))


## [26.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.17) (2022-12-11)

### Bug Fixes

### Features

## [26.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.16) (2022-12-10)

### Bug Fixes

### Features

## [26.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.15) (2022-12-09)

### Bug Fixes
* XT-37290 refactor xtrem-communication and xtrem-routing ([XT-37290](https://jira.sage.com/browse/XT-37290)) ([#10239](https://github.com/Sage-ERP-X3/xtrem/issues/10239))   ([9100674](https://github.com/Sage-ERP-X3/xtrem/commit/9100674d0f939b2b2c369d47e53fe5871d7fc1f9))

### Features

## [26.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.14) (2022-12-08)

### Bug Fixes

### Features

## [26.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.13) (2022-12-08)

### Bug Fixes

### Features
* XT-33494 split sql files for shopfloor ([XT-33494](https://jira.sage.com/browse/XT-33494)) ([#10196](https://github.com/Sage-ERP-X3/xtrem/issues/10196))   ([567016e](https://github.com/Sage-ERP-X3/xtrem/commit/567016ef82f4abc015e4328cca191a3598c0302f))
* **shopfloor:** X3-289599 add mock data requests ([X3-289599](https://jira.sage.com/browse/X3-289599)) ([#10141](https://github.com/Sage-ERP-X3/xtrem/issues/10141))   ([8a8fb74](https://github.com/Sage-ERP-X3/xtrem/commit/8a8fb74fb9be5a1fc7a635da200d91e126a40d19))

## [26.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.12) (2022-12-06)

### Bug Fixes

### Features
* **shopfloor:** X3-289933 new elapse time ([X3-289933](https://jira.sage.com/browse/X3-289933)) ([#10179](https://github.com/Sage-ERP-X3/xtrem/issues/10179))   ([d361fd0](https://github.com/Sage-ERP-X3/xtrem/commit/d361fd093a5bf64296f059fc18a675d1b73c15d9))

## [26.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.11) (2022-12-05)

### Bug Fixes

### Features

## [26.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.10) (2022-12-04)

### Bug Fixes

### Features

## [26.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.9) (2022-12-03)

### Bug Fixes

### Features

## [26.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.8) (2022-12-02)

### Bug Fixes

### Features

## [26.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.7) (2022-12-02)

### Bug Fixes

### Features

## [26.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.6) (2022-12-01)

### Bug Fixes

### Features

## [26.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.5) (2022-11-30)

### Bug Fixes

### Features

## [26.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.4) (2022-11-29)

### Bug Fixes

### Features

## [26.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.3) (2022-11-29)

### Bug Fixes

### Features

## [26.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.2) (2022-11-28)

### Bug Fixes

### Features

## [26.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@26.0.1) (2022-11-28)

### Bug Fixes

### Features

## [25.0.32](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.32) (2022-11-28)

### Bug Fixes

### Features

## [25.0.31](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.31) (2022-11-27)

### Bug Fixes

### Features

## [25.0.30](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.30) (2022-11-26)

### Bug Fixes

### Features

## [25.0.29](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.29) (2022-11-25)

### Bug Fixes

### Features

## [25.0.28](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.28) (2022-11-25)

### Bug Fixes

### Features

## [25.0.27](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.27) (2022-11-24)

### Bug Fixes

### Features
* **shopfloor:** X3-289599 get data from sim and x3 ([X3-289599](https://jira.sage.com/browse/X3-289599)) ([#10009](https://github.com/Sage-ERP-X3/xtrem/issues/10009))   ([e40505e](https://github.com/Sage-ERP-X3/xtrem/commit/e40505e40cb679a1c2a03066f6c029cb76daf16a))

## [25.0.26](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.26) (2022-11-22)

### Bug Fixes

### Features

## [25.0.25](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.25) (2022-11-21)

### Bug Fixes

### Features
* **shopfloor:** X3-289252 update shopfloor screen ([X3-289252](https://jira.sage.com/browse/X3-289252)) ([#9962](https://github.com/Sage-ERP-X3/xtrem/issues/9962))   ([2a98521](https://github.com/Sage-ERP-X3/xtrem/commit/2a98521cb4853de1de22cbab4da30a2994a87e5a))

## [25.0.24](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.24) (2022-11-20)

### Bug Fixes

### Features

## [25.0.23](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.23) (2022-11-19)

### Bug Fixes

### Features

## [25.0.22](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.22) (2022-11-18)

### Bug Fixes

### Features
* **shopfloor:** X3-289727  setting buttons setup start stop and run start stop after op was selected ([X3-289727](https://jira.sage.com/browse/X3-289727)) ([#9938](https://github.com/Sage-ERP-X3/xtrem/issues/9938))   ([2e6629a](https://github.com/Sage-ERP-X3/xtrem/commit/2e6629a46d8a4ad96f3eb73480e778e9ca661a77))
* **shopfloor:** X3-289264 new node machine ([X3-289264](https://jira.sage.com/browse/X3-289264)) ([#9924](https://github.com/Sage-ERP-X3/xtrem/issues/9924))   ([a9c599a](https://github.com/Sage-ERP-X3/xtrem/commit/a9c599a161882d136ab7630d431851e0e44c9717))

## [25.0.21](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.21) (2022-11-17)

### Bug Fixes

### Features
* **shopfloor:** X3-289239 new operator node ([X3-289239](https://jira.sage.com/browse/X3-289239)) ([#9915](https://github.com/Sage-ERP-X3/xtrem/issues/9915))   ([60ed3d0](https://github.com/Sage-ERP-X3/xtrem/commit/60ed3d099449b96fc2bb6568d94a929d939c12d4))

## [25.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.20) (2022-11-16)

### Bug Fixes

### Features
* **shopfloor:** X3-288585 records when user pushed "start"  button ([X3-288585](https://jira.sage.com/browse/X3-288585)) ([#9898](https://github.com/Sage-ERP-X3/xtrem/issues/9898))   ([6715455](https://github.com/Sage-ERP-X3/xtrem/commit/671545556e7593d8035e4b771101e934746479e5))

## [25.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.19) (2022-11-15)

### Bug Fixes

### Features

## [25.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.18) (2022-11-14)

### Bug Fixes

### Features
* **shopfloor:** X3-288561 Shop floor populate fields with values from selected operation ([X3-288561](https://jira.sage.com/browse/X3-288561)) ([#9847](https://github.com/Sage-ERP-X3/xtrem/issues/9847))   ([db2aeee](https://github.com/Sage-ERP-X3/xtrem/commit/db2aeeedc50d12df9e6b371c022fb89570f5e963))

## [25.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.17) (2022-11-13)

### Bug Fixes

### Features

## [25.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.16) (2022-11-12)

### Bug Fixes

### Features

## [25.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.15) (2022-11-11)

### Bug Fixes

### Features

## [25.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.14) (2022-11-10)

### Bug Fixes

### Features
* **shopfloor:** X3-288583 create record tracking nodes part 2 ([X3-288583](https://jira.sage.com/browse/X3-288583)) ([#9786](https://github.com/Sage-ERP-X3/xtrem/issues/9786))   ([c5bafb9](https://github.com/Sage-ERP-X3/xtrem/commit/c5bafb9ae1beadcc8adb7fac7828dbbc823308be))
* **shopfloor:** X3-288538 Shop floor operation field selection ([X3-288538](https://jira.sage.com/browse/X3-288538)) ([#9793](https://github.com/Sage-ERP-X3/xtrem/issues/9793))   ([7927fc8](https://github.com/Sage-ERP-X3/xtrem/commit/7927fc85ae919cd96358a5e5099a4d7218a81bc3))
* node names and properties extracted to translation files   ([#4933](https://github.com/Sage-ERP-X3/xtrem/issues/4933))   ([8e857d8](https://github.com/Sage-ERP-X3/xtrem/commit/8e857d841afb7a6f4a79000d21eb2e762c6c1c22))

## [25.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.13) (2022-11-08)

### Bug Fixes

### Features

## [25.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.12) (2022-11-07)

### Bug Fixes

### Features
* **shopfloor:** X3-288583 create node to record data ([X3-288583](https://jira.sage.com/browse/X3-288583)) ([#9710](https://github.com/Sage-ERP-X3/xtrem/issues/9710))   ([c14cbd6](https://github.com/Sage-ERP-X3/xtrem/commit/c14cbd69a9397dd87e41301a0702fe37dcc56790))

## [25.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.11) (2022-11-06)

### Bug Fixes

### Features

## [25.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.10) (2022-11-05)

### Bug Fixes

### Features

## [25.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.9) (2022-11-04)

### Bug Fixes

### Features
* **shopfloor:** X3-288538 Shop floor screen design with all fields and buttons ([X3-288538](https://jira.sage.com/browse/X3-288538)) ([#9697](https://github.com/Sage-ERP-X3/xtrem/issues/9697))   ([e7512cd](https://github.com/Sage-ERP-X3/xtrem/commit/e7512cdc2b11ef3b1d40d74e3457be73780eadc2))

## [25.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.8) (2022-11-03)

### Bug Fixes

### Features
* **shopfloor:** X3-288536 operation node ([X3-288536](https://jira.sage.com/browse/X3-288536)) ([#9663](https://github.com/Sage-ERP-X3/xtrem/issues/9663))   ([7bdbf78](https://github.com/Sage-ERP-X3/xtrem/commit/7bdbf78dd797d7c8b8a9d34455aa6cce1ab9e9ae))

## [25.0.7](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.7) (2022-11-02)

### Bug Fixes

### Features

## [25.0.6](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.6) (2022-11-01)

### Bug Fixes

### Features

## [25.0.5](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.5) (2022-10-31)

### Bug Fixes

### Features

## [25.0.4](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.4) (2022-10-31)

### Bug Fixes

### Features
* **shopfloor:** X3-288155 base image for dev ([X3-288155](https://jira.sage.com/browse/X3-288155)) ([#9539](https://github.com/Sage-ERP-X3/xtrem/issues/9539))   ([d795244](https://github.com/Sage-ERP-X3/xtrem/commit/d7952442969e72553b6fa170bf2d5bd5e8161ae2))

## [25.0.3](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.3) (2022-10-30)

### Bug Fixes

### Features

## [25.0.2](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.2) (2022-10-29)

### Bug Fixes

### Features

## [25.0.1](https://github.com/compare/...@sage/xtrem~shopfloor@25.0.1) (2022-10-28)

### Bug Fixes

### Features
* XT-34292 fix shopfloor dependencies ([XT-34292](https://jira.sage.com/browse/XT-34292)) ([#9613](https://github.com/Sage-ERP-X3/xtrem/issues/9613))   ([acfb51e](https://github.com/Sage-ERP-X3/xtrem/commit/acfb51e56707acbc154cc6e847a8f86800ce420b))

## [24.0.20](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.20) (2022-10-27)

### Bug Fixes
* realign mocha package.json (XT-34837) ([XT-34837](https://jira.sage.com/browse/XT-34837)) ([#9579](https://github.com/Sage-ERP-X3/xtrem/issues/9579))   ([cd5d2b4](https://github.com/Sage-ERP-X3/xtrem/commit/cd5d2b4c8d402e96cdc84dda790facc617780058))

### Features

## [24.0.19](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.19) (2022-10-26)

### Bug Fixes

### Features

## [24.0.18](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.18) (2022-10-25)

### Bug Fixes

### Features

## [24.0.17](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.17) (2022-10-24)

### Bug Fixes

### Features
* **shopfloor:** X3-288185 create shopfloor pipelines ([X3-288185](https://jira.sage.com/browse/X3-288185)) ([#9412](https://github.com/Sage-ERP-X3/xtrem/issues/9412))   ([42e20c3](https://github.com/Sage-ERP-X3/xtrem/commit/42e20c3116c8738ac1a2f308a8e0069fc8c3bad9))

## [24.0.16](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.16) (2022-10-23)

### Bug Fixes

### Features

## [24.0.15](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.15) (2022-10-22)

### Bug Fixes

### Features

## [24.0.14](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.14) (2022-10-21)

### Bug Fixes

### Features

## [24.0.13](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.13) (2022-10-20)

### Bug Fixes

### Features

## [24.0.12](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.12) (2022-10-20)

### Bug Fixes

### Features

## [24.0.11](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.11) (2022-10-19)

### Bug Fixes

### Features

## [24.0.10](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.10) (2022-10-19)

### Bug Fixes

### Features

## [24.0.9](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.9) (2022-10-18)

### Bug Fixes
* XT-34634 fix shopfloor packages and realign packages from 24.0.7 to 24.0.8 ([XT-34634](https://jira.sage.com/browse/XT-34634)) ([#9406](https://github.com/Sage-ERP-X3/xtrem/issues/9406))   ([f4713c3](https://github.com/Sage-ERP-X3/xtrem/commit/f4713c3c56f794f8e663fe49881c93c96f941478))

### Features
* **xtrem-x3-manufacturing:** X3-281736 add sft package ([X3-281736](https://jira.sage.com/browse/X3-281736)) ([#9338](https://github.com/Sage-ERP-X3/xtrem/issues/9338))   ([ccac27b](https://github.com/Sage-ERP-X3/xtrem/commit/ccac27bdfb6b5b178d917b976b0428aa5692d53a))

## [24.0.8](https://github.com/compare/...@sage/xtrem~shopfloor@24.0.8) (2022-10-18)

### Bug Fixes

-   XT-34634 fix shopfloor packages and realign packages from 24.0.7 to 24.0.8 ([#9406](https://github.com/issues/9406)) ([f4713c3](https://github.com/commit/f4713c3c56f794f8e663fe49881c93c96f941478))

### Features

-   **xtrem-x3-manufacturing:** X3-281736 add sft package ([#9338](https://github.com/issues/9338)) ([ccac27b](https://github.com/commit/ccac27bdfb6b5b178d917b976b0428aa5692d53a))
