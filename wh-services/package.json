{"name": "@sage/xtrem~wh-services", "description": "Umbrella project for Xtrem Warehouse Services", "version": "54.0.58", "license": "UNLICENSED", "private": true, "scripts": {"build:wh-services": "cd .. && pnpm build:wh-services", "generate": "xtrem x3-dev generate --create-main --scopes @sage --service-name wh && pnpm ts:references:fix", "generate-translations": "xtrem x3-dev generate --translations --scopes @sage --service-name wh", "lint:wh:services": "cd .. && pnpm lint:wh-services", "start": "cd ./main/wh-main && pnpm start", "test:wh:services": "cd .. && pnpm test:wh-services", "ts:references:fix": "cd .. && pnpm ts:references:fix", "test:functional": "../scripts/lerna-cache/test-functional.sh", "test:functional:report": "../scripts/lerna-cache/test-functional-report.sh"}, "devDependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*"}}