# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [54.0.58](https://github.com/compare/...@sage/xtrem~wh-services@54.0.58) (2025-06-19)

### Bug Fixes

### Features


## [54.0.57](https://github.com/compare/...@sage/xtrem~wh-services@54.0.57) (2025-06-16)

### Bug Fixes

### Features


## [54.0.56](https://github.com/compare/...@sage/xtrem~wh-services@54.0.56) (2025-06-12)

### Bug Fixes

### Features


## [54.0.55](https://github.com/compare/...@sage/xtrem~wh-services@54.0.55) (2025-06-05)

### Bug Fixes

### Features


## [54.0.54](https://github.com/compare/...@sage/xtrem~wh-services@54.0.54) (2025-06-03)

### Bug Fixes

### Features


## [54.0.53](https://github.com/compare/...@sage/xtrem~wh-services@54.0.53) (2025-05-21)

### Bug Fixes

### Features


## [54.0.52](https://github.com/compare/...@sage/xtrem~wh-services@54.0.52) (2025-05-20)

### Bug Fixes

### Features


## [54.0.51](https://github.com/compare/...@sage/xtrem~wh-services@54.0.51) (2025-05-16)

### Bug Fixes

### Features


## [54.0.50](https://github.com/compare/...@sage/xtrem~wh-services@54.0.50) (2025-05-15)

### Bug Fixes

### Features


## [54.0.49](https://github.com/compare/...@sage/xtrem~wh-services@54.0.49) (2025-05-14)

### Bug Fixes

### Features


## [54.0.48](https://github.com/compare/...@sage/xtrem~wh-services@54.0.48) (2025-05-14)

### Bug Fixes

### Features


## [54.0.47](https://github.com/compare/...@sage/xtrem~wh-services@54.0.47) (2025-05-12)

### Bug Fixes

### Features


## [54.0.46](https://github.com/compare/...@sage/xtrem~wh-services@54.0.46) (2025-05-07)

### Bug Fixes

### Features


## [54.0.45](https://github.com/compare/...@sage/xtrem~wh-services@54.0.45) (2025-05-07)

### Bug Fixes

### Features


## [54.0.44](https://github.com/compare/...@sage/xtrem~wh-services@54.0.44) (2025-05-07)

### Bug Fixes

### Features


## [54.0.43](https://github.com/compare/...@sage/xtrem~wh-services@54.0.43) (2025-05-06)

### Bug Fixes

### Features


## [54.0.42](https://github.com/compare/...@sage/xtrem~wh-services@54.0.42) (2025-05-06)

### Bug Fixes

### Features


## [54.0.41](https://github.com/compare/...@sage/xtrem~wh-services@54.0.41) (2025-05-06)

### Bug Fixes

### Features


## [54.0.40](https://github.com/compare/...@sage/xtrem~wh-services@54.0.40) (2025-05-06)

### Bug Fixes

### Features


## [54.0.39](https://github.com/compare/...@sage/xtrem~wh-services@54.0.39) (2025-05-02)

### Bug Fixes

### Features


## [54.0.38](https://github.com/compare/...@sage/xtrem~wh-services@54.0.38) (2025-04-29)

### Bug Fixes

### Features


## [54.0.37](https://github.com/compare/...@sage/xtrem~wh-services@54.0.37) (2025-04-28)

### Bug Fixes

### Features


## [54.0.36](https://github.com/compare/...@sage/xtrem~wh-services@54.0.36) (2025-04-24)

### Bug Fixes

### Features


## [54.0.35](https://github.com/compare/...@sage/xtrem~wh-services@54.0.35) (2025-04-18)

### Bug Fixes

### Features


## [54.0.34](https://github.com/compare/...@sage/xtrem~wh-services@54.0.34) (2025-04-17)

### Bug Fixes

### Features


## [54.0.33](https://github.com/compare/...@sage/xtrem~wh-services@54.0.33) (2025-04-16)

### Bug Fixes

### Features


## [54.0.32](https://github.com/compare/...@sage/xtrem~wh-services@54.0.32) (2025-04-15)

### Bug Fixes

### Features


## [54.0.31](https://github.com/compare/...@sage/xtrem~wh-services@54.0.31) (2025-04-14)

### Bug Fixes

### Features


## [54.0.30](https://github.com/compare/...@sage/xtrem~wh-services@54.0.30) (2025-04-13)

### Bug Fixes

### Features


## [54.0.29](https://github.com/compare/...@sage/xtrem~wh-services@54.0.29) (2025-04-12)

### Bug Fixes

### Features


## [54.0.28](https://github.com/compare/...@sage/xtrem~wh-services@54.0.28) (2025-04-11)

### Bug Fixes

### Features


## [54.0.27](https://github.com/compare/...@sage/xtrem~wh-services@54.0.27) (2025-04-10)

### Bug Fixes

### Features


## [54.0.26](https://github.com/compare/...@sage/xtrem~wh-services@54.0.26) (2025-04-09)

### Bug Fixes

### Features
* **wh-system:** X3-322492 - Add updated lost functions ([X3-322492](https://jira.sage.com/browse/X3-322492)) ([#25210](https://github.com/Sage-ERP-X3/xtrem/issues/25210))   ([e03b5f3](https://github.com/Sage-ERP-X3/xtrem/commit/e03b5f36d6d4749f9ea7a4ea188ba50076801d99))


## [54.0.25](https://github.com/compare/...@sage/xtrem~wh-services@54.0.25) (2025-04-08)

### Bug Fixes

### Features


## [54.0.24](https://github.com/compare/...@sage/xtrem~wh-services@54.0.24) (2025-04-07)

### Bug Fixes

### Features


## [54.0.23](https://github.com/compare/...@sage/xtrem~wh-services@54.0.23) (2025-04-06)

### Bug Fixes

### Features


## [54.0.22](https://github.com/compare/...@sage/xtrem~wh-services@54.0.22) (2025-04-05)

### Bug Fixes

### Features


## [54.0.21](https://github.com/compare/...@sage/xtrem~wh-services@54.0.21) (2025-04-04)

### Bug Fixes

### Features


## [54.0.20](https://github.com/compare/...@sage/xtrem~wh-services@54.0.20) (2025-04-03)

### Bug Fixes

### Features


## [54.0.19](https://github.com/compare/...@sage/xtrem~wh-services@54.0.19) (2025-04-02)

### Bug Fixes

### Features


## [54.0.18](https://github.com/compare/...@sage/xtrem~wh-services@54.0.18) (2025-04-02)

### Bug Fixes

### Features
* line4 introduced XT-89817 ([XT-89817](https://jira.sage.com/browse/XT-89817)) ([#24935](https://github.com/Sage-ERP-X3/xtrem/issues/24935))   ([7abd976](https://github.com/Sage-ERP-X3/xtrem/commit/7abd97669c40344936d8280ef2594572f6c0c4f1))


## [54.0.17](https://github.com/compare/...@sage/xtrem~wh-services@54.0.17) (2025-04-01)

### Bug Fixes

### Features


## [54.0.16](https://github.com/compare/...@sage/xtrem~wh-services@54.0.16) (2025-03-31)

### Bug Fixes

### Features


## [54.0.15](https://github.com/compare/...@sage/xtrem~wh-services@54.0.15) (2025-03-31)

### Bug Fixes

### Features


## [54.0.14](https://github.com/compare/...@sage/xtrem~wh-services@54.0.14) (2025-03-30)

### Bug Fixes

### Features


## [54.0.13](https://github.com/compare/...@sage/xtrem~wh-services@54.0.13) (2025-03-29)

### Bug Fixes

### Features


## [54.0.12](https://github.com/compare/...@sage/xtrem~wh-services@54.0.12) (2025-03-28)

### Bug Fixes

### Features
* X3-329023 custom mutation written in TS ([X3-329023](https://jira.sage.com/browse/X3-329023)) ([#24872](https://github.com/Sage-ERP-X3/xtrem/issues/24872))   ([789ffb4](https://github.com/Sage-ERP-X3/xtrem/commit/789ffb436c11a729fdb0773e382c082d6889d703))


## [54.0.11](https://github.com/compare/...@sage/xtrem~wh-services@54.0.11) (2025-03-27)

### Bug Fixes

### Features


## [54.0.10](https://github.com/compare/...@sage/xtrem~wh-services@54.0.10) (2025-03-27)

### Bug Fixes

### Features


## [54.0.9](https://github.com/compare/...@sage/xtrem~wh-services@54.0.9) (2025-03-27)

### Bug Fixes

### Features
* **x3-system:** X3-327682 - Refactorisation composite barcode with fabricator patterns ([X3-327682](https://jira.sage.com/browse/X3-327682)) ([#23872](https://github.com/Sage-ERP-X3/xtrem/issues/23872))   ([cfeaa25](https://github.com/Sage-ERP-X3/xtrem/commit/cfeaa259d7a586ddcc42a1648ecc03eaf34d06c6))
* **gx:** complete supervisor nodes used in graphql query for ai   ([#24943](https://github.com/Sage-ERP-X3/xtrem/issues/24943))   ([dd78b13](https://github.com/Sage-ERP-X3/xtrem/commit/dd78b13be55daadb28c48b7dc47a9a237fd3224d))


## [54.0.8](https://github.com/compare/...@sage/xtrem~wh-services@54.0.8) (2025-03-26)

### Bug Fixes

### Features


## [54.0.7](https://github.com/compare/...@sage/xtrem~wh-services@54.0.7) (2025-03-25)

### Bug Fixes

### Features


## [54.0.6](https://github.com/compare/...@sage/xtrem~wh-services@54.0.6) (2025-03-24)

### Bug Fixes

### Features


## [54.0.5](https://github.com/compare/...@sage/xtrem~wh-services@54.0.5) (2025-03-23)

### Bug Fixes

### Features


## [54.0.4](https://github.com/compare/...@sage/xtrem~wh-services@54.0.4) (2025-03-22)

### Bug Fixes

### Features


## [54.0.3](https://github.com/compare/...@sage/xtrem~wh-services@54.0.3) (2025-03-21)

### Bug Fixes

### Features


## [54.0.2](https://github.com/compare/...@sage/xtrem~wh-services@54.0.2) (2025-03-20)

### Bug Fixes

### Features


## [54.0.1](https://github.com/compare/...@sage/xtrem~wh-services@54.0.1) (2025-03-20)

### Bug Fixes

### Features


## [54.0.0](https://github.com/compare/...@sage/xtrem~wh-services@54.0.0) (2025-03-20)

### Bug Fixes

### Features


## [53.0.29](https://github.com/compare/...@sage/xtrem~wh-services@53.0.29) (2025-03-19)

### Bug Fixes

### Features


## [53.0.28](https://github.com/compare/...@sage/xtrem~wh-services@53.0.28) (2025-03-18)

### Bug Fixes

### Features


## [53.0.27](https://github.com/compare/...@sage/xtrem~wh-services@53.0.27) (2025-03-17)

### Bug Fixes

### Features


## [53.0.26](https://github.com/compare/...@sage/xtrem~wh-services@53.0.26) (2025-03-17)

### Bug Fixes

### Features
* **dropdown-actions:** ensure menu separators can be extended XT-88304 ([XT-88304](https://jira.sage.com/browse/XT-88304)) ([#24562](https://github.com/Sage-ERP-X3/xtrem/issues/24562))   ([ff19723](https://github.com/Sage-ERP-X3/xtrem/commit/ff19723e08c256d927f0d991763a3829fa4e3ff1))


## [53.0.25](https://github.com/compare/...@sage/xtrem~wh-services@53.0.25) (2025-03-16)

### Bug Fixes

### Features


## [53.0.24](https://github.com/compare/...@sage/xtrem~wh-services@53.0.24) (2025-03-15)

### Bug Fixes

### Features


## [53.0.23](https://github.com/compare/...@sage/xtrem~wh-services@53.0.23) (2025-03-14)

### Bug Fixes

### Features


## [53.0.22](https://github.com/compare/...@sage/xtrem~wh-services@53.0.22) (2025-03-13)

### Bug Fixes

### Features


## [53.0.21](https://github.com/compare/...@sage/xtrem~wh-services@53.0.21) (2025-03-13)

### Bug Fixes

### Features


## [53.0.20](https://github.com/compare/...@sage/xtrem~wh-services@53.0.20) (2025-03-12)

### Bug Fixes

### Features


## [53.0.19](https://github.com/compare/...@sage/xtrem~wh-services@53.0.19) (2025-03-12)

### Bug Fixes

### Features


## [53.0.18](https://github.com/compare/...@sage/xtrem~wh-services@53.0.18) (2025-03-11)

### Bug Fixes

### Features


## [53.0.17](https://github.com/compare/...@sage/xtrem~wh-services@53.0.17) (2025-03-10)

### Bug Fixes

### Features


## [53.0.16](https://github.com/compare/...@sage/xtrem~wh-services@53.0.16) (2025-03-09)

### Bug Fixes

### Features


## [53.0.15](https://github.com/compare/...@sage/xtrem~wh-services@53.0.15) (2025-03-08)

### Bug Fixes

### Features


## [53.0.14](https://github.com/compare/...@sage/xtrem~wh-services@53.0.14) (2025-03-07)

### Bug Fixes
* **deps:** update dependency typescript to v5.8.2   ([#24407](https://github.com/Sage-ERP-X3/xtrem/issues/24407))   ([ef61f2f](https://github.com/Sage-ERP-X3/xtrem/commit/ef61f2ff5202e48ebf85d90690bf9a1052afc70c))
* **wh-product-data:** X3-330599 - Remove local menu #3391 ([X3-330599](https://jira.sage.com/browse/X3-330599)) ([#24521](https://github.com/Sage-ERP-X3/xtrem/issues/24521))   ([6263955](https://github.com/Sage-ERP-X3/xtrem/commit/62639551cd59d3308f9fed36e2e81bde143e3167))

### Features


## [53.0.13](https://github.com/compare/...@sage/xtrem~wh-services@53.0.13) (2025-03-06)

### Bug Fixes

### Features


## [53.0.12](https://github.com/compare/...@sage/xtrem~wh-services@53.0.12) (2025-03-05)

### Bug Fixes

### Features
* **wh-input:** X3-327803 - Add receipt line reference in movement ([X3-327803](https://jira.sage.com/browse/X3-327803)) ([#24458](https://github.com/Sage-ERP-X3/xtrem/issues/24458))   ([40d04f1](https://github.com/Sage-ERP-X3/xtrem/commit/40d04f16d6ba4f888518b6bf849be7ff39b93bf9))
* X3-322078 Classes mutation Operation mapping local menu update ([X3-322078](https://jira.sage.com/browse/X3-322078)) ([#24463](https://github.com/Sage-ERP-X3/xtrem/issues/24463))   ([de7b05c](https://github.com/Sage-ERP-X3/xtrem/commit/de7b05c770ad8097fe4c512ef4ee2f97ba0c36f7))


## [53.0.11](https://github.com/compare/...@sage/xtrem~wh-services@53.0.11) (2025-03-04)

### Bug Fixes

### Features


## [53.0.10](https://github.com/compare/...@sage/xtrem~wh-services@53.0.10) (2025-03-03)

### Bug Fixes

### Features


## [53.0.9](https://github.com/compare/...@sage/xtrem~wh-services@53.0.9) (2025-03-02)

### Bug Fixes

### Features


## [53.0.8](https://github.com/compare/...@sage/xtrem~wh-services@53.0.8) (2025-03-01)

### Bug Fixes

### Features


## [53.0.7](https://github.com/compare/...@sage/xtrem~wh-services@53.0.7) (2025-02-28)

### Bug Fixes

### Features


## [53.0.6](https://github.com/compare/...@sage/xtrem~wh-services@53.0.6) (2025-02-27)

### Bug Fixes

### Features


## [53.0.5](https://github.com/compare/...@sage/xtrem~wh-services@53.0.5) (2025-02-26)

### Bug Fixes

### Features


## [53.0.4](https://github.com/compare/...@sage/xtrem~wh-services@53.0.4) (2025-02-25)

### Bug Fixes

### Features


## [53.0.3](https://github.com/compare/...@sage/xtrem~wh-services@53.0.3) (2025-02-24)

### Bug Fixes

### Features


## [53.0.2](https://github.com/compare/...@sage/xtrem~wh-services@53.0.2) (2025-02-20)

### Bug Fixes

### Features


## [53.0.1](https://github.com/compare/...@sage/xtrem~wh-services@53.0.1) (2025-02-20)

### Bug Fixes

### Features


## [53.0.0](https://github.com/compare/...@sage/xtrem~wh-services@53.0.0) (2025-02-20)

### Bug Fixes

### Features


## [52.0.29](https://github.com/compare/...@sage/xtrem~wh-services@52.0.29) (2025-02-19)

### Bug Fixes

### Features


## [52.0.28](https://github.com/compare/...@sage/xtrem~wh-services@52.0.28) (2025-02-18)

### Bug Fixes

### Features


## [52.0.27](https://github.com/compare/...@sage/xtrem~wh-services@52.0.27) (2025-02-17)

### Bug Fixes

### Features


## [52.0.26](https://github.com/compare/...@sage/xtrem~wh-services@52.0.26) (2025-02-16)

### Bug Fixes

### Features


## [52.0.25](https://github.com/compare/...@sage/xtrem~wh-services@52.0.25) (2025-02-15)

### Bug Fixes

### Features


## [52.0.24](https://github.com/compare/...@sage/xtrem~wh-services@52.0.24) (2025-02-14)

### Bug Fixes

### Features


## [52.0.23](https://github.com/compare/...@sage/xtrem~wh-services@52.0.23) (2025-02-12)

### Bug Fixes
* **wh-master-data:** X3-328612 - Fix functions names in wh-master-data ([X3-328612](https://jira.sage.com/browse/X3-328612)) ([#24064](https://github.com/Sage-ERP-X3/xtrem/issues/24064))   ([25bb5bc](https://github.com/Sage-ERP-X3/xtrem/commit/25bb5bcd1f09a53d51c1fb3e33dd5175e5013803))

### Features
* redos eslint plugin XT-89040 ([XT-89040](https://jira.sage.com/browse/XT-89040)) ([#24054](https://github.com/Sage-ERP-X3/xtrem/issues/24054))   ([6641e00](https://github.com/Sage-ERP-X3/xtrem/commit/6641e00faabfe33acf8a0f2d2b9fae49beb4b5f7))
* **gx:** generate_all_enums   ([#24061](https://github.com/Sage-ERP-X3/xtrem/issues/24061))   ([5b881ce](https://github.com/Sage-ERP-X3/xtrem/commit/5b881ce2a7534c1bd38890ae2eebefebe8157b98))


## [52.0.22](https://github.com/compare/...@sage/xtrem~wh-services@52.0.22) (2025-02-11)

### Bug Fixes

### Features


## [52.0.21](https://github.com/compare/...@sage/xtrem~wh-services@52.0.21) (2025-02-10)

### Bug Fixes

### Features


## [52.0.20](https://github.com/compare/...@sage/xtrem~wh-services@52.0.20) (2025-02-09)

### Bug Fixes

### Features


## [52.0.19](https://github.com/compare/...@sage/xtrem~wh-services@52.0.19) (2025-02-08)

### Bug Fixes

### Features


## [52.0.18](https://github.com/compare/...@sage/xtrem~wh-services@52.0.18) (2025-02-07)

### Bug Fixes

### Features


## [52.0.17](https://github.com/compare/...@sage/xtrem~wh-services@52.0.17) (2025-02-06)

### Bug Fixes

### Features


## [52.0.16](https://github.com/compare/...@sage/xtrem~wh-services@52.0.16) (2025-02-05)

### Bug Fixes

### Features


## [52.0.15](https://github.com/compare/...@sage/xtrem~wh-services@52.0.15) (2025-02-04)

### Bug Fixes
* XT-88557 lower mocha version ([XT-88557](https://jira.sage.com/browse/XT-88557)) ([#23860](https://github.com/Sage-ERP-X3/xtrem/issues/23860))   ([e7a8c25](https://github.com/Sage-ERP-X3/xtrem/commit/e7a8c25a690ee560879f4299be1f70ad71cbc0b8))

### Features


## [52.0.14](https://github.com/compare/...@sage/xtrem~wh-services@52.0.14) (2025-02-03)

### Bug Fixes

### Features


## [52.0.13](https://github.com/compare/...@sage/xtrem~wh-services@52.0.13) (2025-02-01)

### Bug Fixes

### Features


## [52.0.12](https://github.com/compare/...@sage/xtrem~wh-services@52.0.12) (2025-01-31)

### Bug Fixes

### Features


## [52.0.11](https://github.com/compare/...@sage/xtrem~wh-services@52.0.11) (2025-01-30)

### Bug Fixes

### Features


## [52.0.10](https://github.com/compare/...@sage/xtrem~wh-services@52.0.10) (2025-01-29)

### Bug Fixes

### Features


## [52.0.9](https://github.com/compare/...@sage/xtrem~wh-services@52.0.9) (2025-01-29)

### Bug Fixes

### Features


## [52.0.8](https://github.com/compare/...@sage/xtrem~wh-services@52.0.8) (2025-01-28)

### Bug Fixes
* X3-319934 user parameter values ([X3-319934](https://jira.sage.com/browse/X3-319934)) ([#23753](https://github.com/Sage-ERP-X3/xtrem/issues/23753))   ([bf14469](https://github.com/Sage-ERP-X3/xtrem/commit/bf144692de0eb1764be97014d8c0891acf52ad32))

### Features


## [52.0.7](https://github.com/compare/...@sage/xtrem~wh-services@52.0.7) (2025-01-28)

### Bug Fixes

### Features


## [52.0.6](https://github.com/compare/...@sage/xtrem~wh-services@52.0.6) (2025-01-27)

### Bug Fixes

### Features


## [52.0.5](https://github.com/compare/...@sage/xtrem~wh-services@52.0.5) (2025-01-26)

### Bug Fixes

### Features


## [52.0.4](https://github.com/compare/...@sage/xtrem~wh-services@52.0.4) (2025-01-25)

### Bug Fixes

### Features


## [52.0.3](https://github.com/compare/...@sage/xtrem~wh-services@52.0.3) (2025-01-24)

### Bug Fixes

### Features


## [52.0.2](https://github.com/compare/...@sage/xtrem~wh-services@52.0.2) (2025-01-23)

### Bug Fixes
* **wh-stock:** X3-322665 - Check if stock is available before chain another page ([X3-322665](https://jira.sage.com/browse/X3-322665)) ([#23654](https://github.com/Sage-ERP-X3/xtrem/issues/23654))   ([2971f1d](https://github.com/Sage-ERP-X3/xtrem/commit/2971f1dacedf4cb1e997247389ee38cbb6705e96))

### Features


## [52.0.1](https://github.com/compare/...@sage/xtrem~wh-services@52.0.1) (2025-01-23)

### Bug Fixes

### Features


## [52.0.0](https://github.com/compare/...@sage/xtrem~wh-services@52.0.0) (2025-01-23)

### Bug Fixes

### Features


## [51.0.35](https://github.com/compare/...@sage/xtrem~wh-services@51.0.35) (2025-01-22)

### Bug Fixes

### Features


## [51.0.34](https://github.com/compare/...@sage/xtrem~wh-services@51.0.34) (2025-01-21)

### Bug Fixes

### Features


## [51.0.33](https://github.com/compare/...@sage/xtrem~wh-services@51.0.33) (2025-01-20)

### Bug Fixes

### Features


## [51.0.32](https://github.com/compare/...@sage/xtrem~wh-services@51.0.32) (2025-01-20)

### Bug Fixes

### Features


## [51.0.31](https://github.com/compare/...@sage/xtrem~wh-services@51.0.31) (2025-01-18)

### Bug Fixes

### Features


## [51.0.30](https://github.com/compare/...@sage/xtrem~wh-services@51.0.30) (2025-01-17)

### Bug Fixes

### Features


## [51.0.29](https://github.com/compare/...@sage/xtrem~wh-services@51.0.29) (2025-01-16)

### Bug Fixes

### Features
* **wh-input:** X3-323375 - Simplifies the filtering of the receiving lines in progress ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#23577](https://github.com/Sage-ERP-X3/xtrem/issues/23577))   ([a71b7d9](https://github.com/Sage-ERP-X3/xtrem/commit/a71b7d953b9458737b16153e6a0f2534e660c800))


## [51.0.28](https://github.com/compare/...@sage/xtrem~wh-services@51.0.28) (2025-01-15)

### Bug Fixes

### Features


## [51.0.27](https://github.com/compare/...@sage/xtrem~wh-services@51.0.27) (2025-01-14)

### Bug Fixes
* **wh-services:** X3-323375 - Fix platform reformating ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#23493](https://github.com/Sage-ERP-X3/xtrem/issues/23493))   ([81422d3](https://github.com/Sage-ERP-X3/xtrem/commit/81422d344c7c9eb403e9f1b7d90bf7fff7d4d2a2))

### Features


## [51.0.26](https://github.com/compare/...@sage/xtrem~wh-services@51.0.26) (2025-01-13)

### Bug Fixes

### Features


## [51.0.25](https://github.com/compare/...@sage/xtrem~wh-services@51.0.25) (2025-01-12)

### Bug Fixes

### Features


## [51.0.24](https://github.com/compare/...@sage/xtrem~wh-services@51.0.24) (2025-01-11)

### Bug Fixes

### Features


## [51.0.23](https://github.com/compare/...@sage/xtrem~wh-services@51.0.23) (2025-01-10)

### Bug Fixes

### Features


## [51.0.22](https://github.com/compare/...@sage/xtrem~wh-services@51.0.22) (2025-01-09)

### Bug Fixes

### Features


## [51.0.21](https://github.com/compare/...@sage/xtrem~wh-services@51.0.21) (2025-01-08)

### Bug Fixes
* **deps:** update dependency mocha to v11   ([#23267](https://github.com/Sage-ERP-X3/xtrem/issues/23267))   ([aad520e](https://github.com/Sage-ERP-X3/xtrem/commit/aad520e4f1226e18c486fe256ea4730dffc4bd91))

### Features


## [51.0.20](https://github.com/compare/...@sage/xtrem~wh-services@51.0.20) (2025-01-07)

### Bug Fixes

### Features
* **apps:** cleanup xtrem cli deps XT-85855 ([XT-85855](https://jira.sage.com/browse/XT-85855)) ([#23141](https://github.com/Sage-ERP-X3/xtrem/issues/23141))   ([6c0cdeb](https://github.com/Sage-ERP-X3/xtrem/commit/6c0cdeb31ec2c8c41eacc8703c2d1c56fbb991a0))


## [51.0.19](https://github.com/compare/...@sage/xtrem~wh-services@51.0.19) (2025-01-06)

### Bug Fixes

### Features


## [51.0.18](https://github.com/compare/...@sage/xtrem~wh-services@51.0.18) (2025-01-05)

### Bug Fixes

### Features


## [51.0.17](https://github.com/compare/...@sage/xtrem~wh-services@51.0.17) (2025-01-04)

### Bug Fixes

### Features


## [51.0.16](https://github.com/compare/...@sage/xtrem~wh-services@51.0.16) (2025-01-03)

### Bug Fixes

### Features


## [51.0.15](https://github.com/compare/...@sage/xtrem~wh-services@51.0.15) (2025-01-02)

### Bug Fixes

### Features


## [51.0.14](https://github.com/compare/...@sage/xtrem~wh-services@51.0.14) (2025-01-01)

### Bug Fixes

### Features


## [51.0.13](https://github.com/compare/...@sage/xtrem~wh-services@51.0.13) (2024-12-31)

### Bug Fixes
* **wh-product-data:** X3-261145 - getConsumptionUnit - Fix updated node name ([X3-261145](https://jira.sage.com/browse/X3-261145)) ([#23325](https://github.com/Sage-ERP-X3/xtrem/issues/23325))   ([567549c](https://github.com/Sage-ERP-X3/xtrem/commit/567549c88e85acb3c22b3fc0a50654c1ba264590))

### Features
* **wh-input:** X3-261145 - Add productContainers collection to Product node ([X3-261145](https://jira.sage.com/browse/X3-261145)) ([#23318](https://github.com/Sage-ERP-X3/xtrem/issues/23318))   ([b43349e](https://github.com/Sage-ERP-X3/xtrem/commit/b43349e5e986511530c0900facbd2a1b4ef4df63))


## [51.0.12](https://github.com/compare/...@sage/xtrem~wh-services@51.0.12) (2024-12-29)

### Bug Fixes

### Features


## [51.0.11](https://github.com/compare/...@sage/xtrem~wh-services@51.0.11) (2024-12-28)

### Bug Fixes

### Features


## [51.0.10](https://github.com/compare/...@sage/xtrem~wh-services@51.0.10) (2024-12-27)

### Bug Fixes

### Features


## [51.0.9](https://github.com/compare/...@sage/xtrem~wh-services@51.0.9) (2024-12-26)

### Bug Fixes

### Features


## [51.0.8](https://github.com/compare/...@sage/xtrem~wh-services@51.0.8) (2024-12-25)

### Bug Fixes

### Features


## [51.0.7](https://github.com/compare/...@sage/xtrem~wh-services@51.0.7) (2024-12-24)

### Bug Fixes

### Features


## [51.0.6](https://github.com/compare/...@sage/xtrem~wh-services@51.0.6) (2024-12-23)

### Bug Fixes

### Features
* **wh-product-data:** X3-261145 - Add suppliersUpcCodes collection to product node ([X3-261145](https://jira.sage.com/browse/X3-261145)) ([#23270](https://github.com/Sage-ERP-X3/xtrem/issues/23270))   ([0fddf70](https://github.com/Sage-ERP-X3/xtrem/commit/0fddf70cb31c9d0351c44d6cc7d9238659fadb60))


## [51.0.5](https://github.com/compare/...@sage/xtrem~wh-services@51.0.5) (2024-12-22)

### Bug Fixes

### Features


## [51.0.4](https://github.com/compare/...@sage/xtrem~wh-services@51.0.4) (2024-12-21)

### Bug Fixes

### Features


## [51.0.3](https://github.com/compare/...@sage/xtrem~wh-services@51.0.3) (2024-12-20)

### Bug Fixes

### Features


## [51.0.2](https://github.com/compare/...@sage/xtrem~wh-services@51.0.2) (2024-12-19)

### Bug Fixes

### Features
* **wh-product-data:** X3-323375 - Add Supplier Upc Code node ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#23193](https://github.com/Sage-ERP-X3/xtrem/issues/23193))   ([0e74ed8](https://github.com/Sage-ERP-X3/xtrem/commit/0e74ed8de8c847efa23e74d126a6361283b0acb5))


## [51.0.1](https://github.com/compare/...@sage/xtrem~wh-services@51.0.1) (2024-12-19)

### Bug Fixes

### Features


## [51.0.0](https://github.com/compare/...@sage/xtrem~wh-services@51.0.0) (2024-12-19)

### Bug Fixes

### Features


## [50.0.30](https://github.com/compare/...@sage/xtrem~wh-services@50.0.30) (2024-12-18)

### Bug Fixes

### Features


## [50.0.29](https://github.com/compare/...@sage/xtrem~wh-services@50.0.29) (2024-12-17)

### Bug Fixes

### Features


## [50.0.28](https://github.com/compare/...@sage/xtrem~wh-services@50.0.28) (2024-12-16)

### Bug Fixes

### Features


## [50.0.27](https://github.com/compare/...@sage/xtrem~wh-services@50.0.27) (2024-12-15)

### Bug Fixes

### Features


## [50.0.26](https://github.com/compare/...@sage/xtrem~wh-services@50.0.26) (2024-12-14)

### Bug Fixes

### Features


## [50.0.25](https://github.com/compare/...@sage/xtrem~wh-services@50.0.25) (2024-12-13)

### Bug Fixes

### Features
* **wh-stock:** X3-323375 - Reorganize output package ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#23096](https://github.com/Sage-ERP-X3/xtrem/issues/23096))   ([6da3a3e](https://github.com/Sage-ERP-X3/xtrem/commit/6da3a3eab854bd4c95b98e9f296b5231cdafe4eb))


## [50.0.24](https://github.com/compare/...@sage/xtrem~wh-services@50.0.24) (2024-12-12)

### Bug Fixes

### Features
* **wh-output:** X3-323375 - Reorganize output package ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#23076](https://github.com/Sage-ERP-X3/xtrem/issues/23076))   ([62eb54c](https://github.com/Sage-ERP-X3/xtrem/commit/62eb54cc00987afd39567fa7c8e996b7704081aa))
* **x3-stock:** X3-322492 - Update administrative receipt model for mutation ([X3-322492](https://jira.sage.com/browse/X3-322492)) ([#22481](https://github.com/Sage-ERP-X3/xtrem/issues/22481))   ([7f4fd78](https://github.com/Sage-ERP-X3/xtrem/commit/7f4fd780accd8c3b51f49d8ae4a558939f78543d))


## [50.0.23](https://github.com/compare/...@sage/xtrem~wh-services@50.0.23) (2024-12-11)

### Bug Fixes

### Features


## [50.0.22](https://github.com/compare/...@sage/xtrem~wh-services@50.0.22) (2024-12-10)

### Bug Fixes

### Features


## [50.0.21](https://github.com/compare/...@sage/xtrem~wh-services@50.0.21) (2024-12-09)

### Bug Fixes

### Features


## [50.0.20](https://github.com/compare/...@sage/xtrem~wh-services@50.0.20) (2024-12-08)

### Bug Fixes

### Features


## [50.0.19](https://github.com/compare/...@sage/xtrem~wh-services@50.0.19) (2024-12-07)

### Bug Fixes

### Features


## [50.0.18](https://github.com/compare/...@sage/xtrem~wh-services@50.0.18) (2024-12-06)

### Bug Fixes

### Features


## [50.0.17](https://github.com/compare/...@sage/xtrem~wh-services@50.0.17) (2024-12-05)

### Bug Fixes

### Features


## [50.0.16](https://github.com/compare/...@sage/xtrem~wh-services@50.0.16) (2024-12-04)

### Bug Fixes

### Features


## [50.0.15](https://github.com/compare/...@sage/xtrem~wh-services@50.0.15) (2024-12-03)

### Bug Fixes

### Features


## [50.0.14](https://github.com/compare/...@sage/xtrem~wh-services@50.0.14) (2024-12-02)

### Bug Fixes

### Features
* **x3-warehousing:** X3-323375 - Update packages artefacts ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#22754](https://github.com/Sage-ERP-X3/xtrem/issues/22754))   ([811a82a](https://github.com/Sage-ERP-X3/xtrem/commit/811a82a94b2547d04f86d13a506766a65d18f08f))


## [50.0.13](https://github.com/compare/...@sage/xtrem~wh-services@50.0.13) (2024-12-01)

### Bug Fixes

### Features


## [50.0.12](https://github.com/compare/...@sage/xtrem~wh-services@50.0.12) (2024-11-30)

### Bug Fixes

### Features


## [50.0.11](https://github.com/compare/...@sage/xtrem~wh-services@50.0.11) (2024-11-29)

### Bug Fixes

### Features


## [50.0.10](https://github.com/compare/...@sage/xtrem~wh-services@50.0.10) (2024-11-29)

### Bug Fixes

### Features


## [50.0.9](https://github.com/compare/...@sage/xtrem~wh-services@50.0.9) (2024-11-28)

### Bug Fixes

### Features
* **x3-warehousing:** X3-323375 - Merge initial packages reorganization ([X3-323375](https://jira.sage.com/browse/X3-323375)) ([#22681](https://github.com/Sage-ERP-X3/xtrem/issues/22681))   ([dfaa9fd](https://github.com/Sage-ERP-X3/xtrem/commit/dfaa9fd5acdfeb0161144231877d4b8a5cd9c8f5))


## [50.0.8](https://github.com/compare/...@sage/xtrem~wh-services@50.0.8) (2024-11-27)

### Bug Fixes
* X3-322574 wh-services scripts ([X3-322574](https://jira.sage.com/browse/X3-322574)) ([#22655](https://github.com/Sage-ERP-X3/xtrem/issues/22655))   ([50dab46](https://github.com/Sage-ERP-X3/xtrem/commit/50dab46dcbc81c14933e482e2997cef528e52452))

### Features
* **xtrem-cli-atp:** XT-80257 site & depositor control ([XT-80257](https://jira.sage.com/browse/XT-80257)) ([#22439](https://github.com/Sage-ERP-X3/xtrem/issues/22439))   ([c925320](https://github.com/Sage-ERP-X3/xtrem/commit/c9253205387b20abcde550ccd96696cee426a6b4))


## [50.0.7](https://github.com/compare/...@sage/xtrem~wh-services@50.0.7) (2024-11-26)

### Bug Fixes

### Features


## [50.0.6](https://github.com/compare/...@sage/xtrem~wh-services@50.0.6) (2024-11-26)

### Bug Fixes

### Features


## [50.0.5](https://github.com/compare/...@sage/xtrem~wh-services@50.0.5) (2024-11-25)

### Bug Fixes

### Features
* attachment section XT-84523 ([XT-84523](https://jira.sage.com/browse/XT-84523)) ([#22595](https://github.com/Sage-ERP-X3/xtrem/issues/22595))   ([c97afcc](https://github.com/Sage-ERP-X3/xtrem/commit/c97afcc95a8b23604a74160b68595a7c9abe107e))


## [50.0.4](https://github.com/compare/...@sage/xtrem~wh-services@50.0.4) (2024-11-24)

### Bug Fixes

### Features


## [50.0.3](https://github.com/compare/...@sage/xtrem~wh-services@50.0.3) (2024-11-23)

### Bug Fixes

### Features


## [50.0.2](https://github.com/compare/...@sage/xtrem~wh-services@50.0.2) (2024-11-22)

### Bug Fixes

### Features


## [50.0.1](https://github.com/compare/...@sage/xtrem~wh-services@50.0.1) (2024-11-22)

### Bug Fixes

### Features


## [50.0.0](https://github.com/compare/...@sage/xtrem~wh-services@50.0.0) (2024-11-22)

### Bug Fixes

### Features


## [49.0.30](https://github.com/compare/...@sage/xtrem~wh-services@49.0.30) (2024-11-22)

### Bug Fixes

### Features


## [49.0.29](https://github.com/compare/...@sage/xtrem~wh-services@49.0.29) (2024-11-21)

### Bug Fixes

### Features


## [49.0.28](https://github.com/compare/...@sage/xtrem~wh-services@49.0.28) (2024-11-20)

### Bug Fixes

### Features


## [49.0.27](https://github.com/compare/...@sage/xtrem~wh-services@49.0.27) (2024-11-19)

### Bug Fixes

### Features


## [49.0.26](https://github.com/compare/...@sage/xtrem~wh-services@49.0.26) (2024-11-18)

### Bug Fixes

### Features


## [49.0.25](https://github.com/compare/...@sage/xtrem~wh-services@49.0.25) (2024-11-17)

### Bug Fixes

### Features


## [49.0.24](https://github.com/compare/...@sage/xtrem~wh-services@49.0.24) (2024-11-16)

### Bug Fixes

### Features


## [49.0.23](https://github.com/compare/...@sage/xtrem~wh-services@49.0.23) (2024-11-15)

### Bug Fixes

### Features


## [49.0.22](https://github.com/compare/...@sage/xtrem~wh-services@49.0.22) (2024-11-14)

### Bug Fixes

### Features


## [49.0.21](https://github.com/compare/...@sage/xtrem~wh-services@49.0.21) (2024-11-13)

### Bug Fixes
* **wh-stock:** X3-322645 - Replace dynamic by evaluated title ([X3-322645](https://jira.sage.com/browse/X3-322645)) ([#22338](https://github.com/Sage-ERP-X3/xtrem/issues/22338))   ([41acc82](https://github.com/Sage-ERP-X3/xtrem/commit/41acc82e099b31ff4d971e055bf3c713ff0ec1bc))

### Features


## [49.0.20](https://github.com/compare/...@sage/xtrem~wh-services@49.0.20) (2024-11-12)

### Bug Fixes

### Features
* **tc:** properties starting by underscore must be removed from the graphql schema x3 322292   ([#22303](https://github.com/Sage-ERP-X3/xtrem/issues/22303))   ([c8b488a](https://github.com/Sage-ERP-X3/xtrem/commit/c8b488a139a43a5039a1a53b85f71258ec2f3454))


## [49.0.19](https://github.com/compare/...@sage/xtrem~wh-services@49.0.19) (2024-11-11)

### Bug Fixes

### Features


## [49.0.18](https://github.com/compare/...@sage/xtrem~wh-services@49.0.18) (2024-11-10)

### Bug Fixes

### Features


## [49.0.17](https://github.com/compare/...@sage/xtrem~wh-services@49.0.17) (2024-11-09)

### Bug Fixes

### Features


## [49.0.16](https://github.com/compare/...@sage/xtrem~wh-services@49.0.16) (2024-11-08)

### Bug Fixes

### Features


## [49.0.15](https://github.com/compare/...@sage/xtrem~wh-services@49.0.15) (2024-11-07)

### Bug Fixes

### Features


## [49.0.14](https://github.com/compare/...@sage/xtrem~wh-services@49.0.14) (2024-11-06)

### Bug Fixes

### Features


## [49.0.13](https://github.com/compare/...@sage/xtrem~wh-services@49.0.13) (2024-11-05)

### Bug Fixes

### Features


## [49.0.12](https://github.com/compare/...@sage/xtrem~wh-services@49.0.12) (2024-11-04)

### Bug Fixes

### Features


## [49.0.11](https://github.com/compare/...@sage/xtrem~wh-services@49.0.11) (2024-11-03)

### Bug Fixes

### Features


## [49.0.10](https://github.com/compare/...@sage/xtrem~wh-services@49.0.10) (2024-11-02)

### Bug Fixes

### Features


## [49.0.9](https://github.com/compare/...@sage/xtrem~wh-services@49.0.9) (2024-11-01)

### Bug Fixes

### Features


## [49.0.8](https://github.com/compare/...@sage/xtrem~wh-services@49.0.8) (2024-10-31)

### Bug Fixes

### Features


## [49.0.7](https://github.com/compare/...@sage/xtrem~wh-services@49.0.7) (2024-10-30)

### Bug Fixes

### Features


## [49.0.6](https://github.com/compare/...@sage/xtrem~wh-services@49.0.6) (2024-10-29)

### Bug Fixes

### Features


## [49.0.5](https://github.com/compare/...@sage/xtrem~wh-services@49.0.5) (2024-10-28)

### Bug Fixes

### Features


## [49.0.4](https://github.com/compare/...@sage/xtrem~wh-services@49.0.4) (2024-10-27)

### Bug Fixes

### Features


## [49.0.3](https://github.com/compare/...@sage/xtrem~wh-services@49.0.3) (2024-10-26)

### Bug Fixes

### Features


## [49.0.2](https://github.com/compare/...@sage/xtrem~wh-services@49.0.2) (2024-10-24)

### Bug Fixes

### Features


## [49.0.1](https://github.com/compare/...@sage/xtrem~wh-services@49.0.1) (2024-10-24)

### Bug Fixes

### Features


## [49.0.0](https://github.com/compare/...@sage/xtrem~wh-services@49.0.0) (2024-10-24)

### Bug Fixes

### Features


## [48.0.37](https://github.com/compare/...@sage/xtrem~wh-services@48.0.37) (2024-10-23)

### Bug Fixes

### Features


## [48.0.36](https://github.com/compare/...@sage/xtrem~wh-services@48.0.36) (2024-10-22)

### Bug Fixes

### Features


## [48.0.35](https://github.com/compare/...@sage/xtrem~wh-services@48.0.35) (2024-10-21)

### Bug Fixes
* X3-321713 date management ([X3-321713](https://jira.sage.com/browse/X3-321713)) ([#21798](https://github.com/Sage-ERP-X3/xtrem/issues/21798))   ([7d20707](https://github.com/Sage-ERP-X3/xtrem/commit/7d20707697cce46f2eba5ef9fb53d07001518f0d))

### Features


## [48.0.34](https://github.com/compare/...@sage/xtrem~wh-services@48.0.34) (2024-10-20)

### Bug Fixes

### Features


## [48.0.33](https://github.com/compare/...@sage/xtrem~wh-services@48.0.33) (2024-10-19)

### Bug Fixes

### Features


## [48.0.32](https://github.com/compare/...@sage/xtrem~wh-services@48.0.32) (2024-10-18)

### Bug Fixes

### Features


## [48.0.31](https://github.com/compare/...@sage/xtrem~wh-services@48.0.31) (2024-10-17)

### Bug Fixes

### Features


## [48.0.30](https://github.com/compare/...@sage/xtrem~wh-services@48.0.30) (2024-10-16)

### Bug Fixes

### Features


## [48.0.29](https://github.com/compare/...@sage/xtrem~wh-services@48.0.29) (2024-10-15)

### Bug Fixes

### Features


## [48.0.28](https://github.com/compare/...@sage/xtrem~wh-services@48.0.28) (2024-10-14)

### Bug Fixes

### Features


## [48.0.27](https://github.com/compare/...@sage/xtrem~wh-services@48.0.27) (2024-10-13)

### Bug Fixes

### Features


## [48.0.26](https://github.com/compare/...@sage/xtrem~wh-services@48.0.26) (2024-10-12)

### Bug Fixes

### Features


## [48.0.25](https://github.com/compare/...@sage/xtrem~wh-services@48.0.25) (2024-10-11)

### Bug Fixes
* **x3-system:** X3-321099 - Replace zero by empty default date ([X3-321099](https://jira.sage.com/browse/X3-321099)) ([#21557](https://github.com/Sage-ERP-X3/xtrem/issues/21557))   ([7d64476](https://github.com/Sage-ERP-X3/xtrem/commit/7d644763c7844cf980633527817e3a51df973452))

### Features


## [48.0.24](https://github.com/compare/...@sage/xtrem~wh-services@48.0.24) (2024-10-10)

### Bug Fixes

### Features


## [48.0.23](https://github.com/compare/...@sage/xtrem~wh-services@48.0.23) (2024-10-09)

### Bug Fixes

### Features
* **wh-services:** X3-320539 - Fix naming and update graphQL tests ([X3-320539](https://jira.sage.com/browse/X3-320539)) ([#21482](https://github.com/Sage-ERP-X3/xtrem/issues/21482))   ([da985c3](https://github.com/Sage-ERP-X3/xtrem/commit/da985c3fc1bdf44a76e0be791d59843a8766d3af))


## [48.0.22](https://github.com/compare/...@sage/xtrem~wh-services@48.0.22) (2024-10-08)

### Bug Fixes

### Features


## [48.0.21](https://github.com/compare/...@sage/xtrem~wh-services@48.0.21) (2024-10-07)

### Bug Fixes

### Features


## [48.0.20](https://github.com/compare/...@sage/xtrem~wh-services@48.0.20) (2024-10-06)

### Bug Fixes

### Features


## [48.0.19](https://github.com/compare/...@sage/xtrem~wh-services@48.0.19) (2024-10-05)

### Bug Fixes

### Features


## [48.0.18](https://github.com/compare/...@sage/xtrem~wh-services@48.0.18) (2024-10-04)

### Bug Fixes

### Features


## [48.0.17](https://github.com/compare/...@sage/xtrem~wh-services@48.0.17) (2024-10-03)

### Bug Fixes

### Features


## [48.0.16](https://github.com/compare/...@sage/xtrem~wh-services@48.0.16) (2024-10-02)

### Bug Fixes

### Features
* **wh-master-data:** X3-320539 - Remove improper node-functions located in root ([X3-320539](https://jira.sage.com/browse/X3-320539)) ([#21403](https://github.com/Sage-ERP-X3/xtrem/issues/21403))   ([de12495](https://github.com/Sage-ERP-X3/xtrem/commit/de124958c6280e4c01094809acd7d3cdd174570b))


## [48.0.15](https://github.com/compare/...@sage/xtrem~wh-services@48.0.15) (2024-10-01)

### Bug Fixes

### Features
* **wh-stock:** X3-320601 - Display stock nature code ([X3-320601](https://jira.sage.com/browse/X3-320601)) ([#21324](https://github.com/Sage-ERP-X3/xtrem/issues/21324))   ([31b99f4](https://github.com/Sage-ERP-X3/xtrem/commit/31b99f49b594631de03ff521848c077daefe4827))


## [48.0.14](https://github.com/compare/...@sage/xtrem~wh-services@48.0.14) (2024-09-30)

### Bug Fixes

### Features


## [48.0.13](https://github.com/compare/...@sage/xtrem~wh-services@48.0.13) (2024-09-29)

### Bug Fixes

### Features


## [48.0.12](https://github.com/compare/...@sage/xtrem~wh-services@48.0.12) (2024-09-29)

### Bug Fixes

### Features


## [48.0.11](https://github.com/compare/...@sage/xtrem~wh-services@48.0.11) (2024-09-28)

### Bug Fixes

### Features


## [48.0.10](https://github.com/compare/...@sage/xtrem~wh-services@48.0.10) (2024-09-27)

### Bug Fixes

### Features


## [48.0.9](https://github.com/compare/...@sage/xtrem~wh-services@48.0.9) (2024-09-26)

### Bug Fixes

### Features


## [48.0.8](https://github.com/compare/...@sage/xtrem~wh-services@48.0.8) (2024-09-25)

### Bug Fixes
* XT-78516 alias Operation in generation of api.d.ts ([XT-78516](https://jira.sage.com/browse/XT-78516)) ([#21185](https://github.com/Sage-ERP-X3/xtrem/issues/21185))   ([f0a7ac2](https://github.com/Sage-ERP-X3/xtrem/commit/f0a7ac2f0add2c3e60343b7c90d1c49e8ab2b2b7))

### Features


## [48.0.7](https://github.com/compare/...@sage/xtrem~wh-services@48.0.7) (2024-09-24)

### Bug Fixes

### Features
* **wh-stock:** X3-261140 - Update inventory reason and add decimals support ([X3-261140](https://jira.sage.com/browse/X3-261140)) ([#21199](https://github.com/Sage-ERP-X3/xtrem/issues/21199))   ([606d40c](https://github.com/Sage-ERP-X3/xtrem/commit/606d40c90aab7cbf0619682d8b7b85684ba611c5))
* **pipelines:** XT-73270 - wh mobile automation pipelines and architecture - v3 ([XT-73270](https://jira.sage.com/browse/XT-73270)) ([#21212](https://github.com/Sage-ERP-X3/xtrem/issues/21212))   ([33487ca](https://github.com/Sage-ERP-X3/xtrem/commit/33487cac715a83d5ed1e3e098885aca527298533))


## [48.0.6](https://github.com/compare/...@sage/xtrem~wh-services@48.0.6) (2024-09-23)

### Bug Fixes

### Features


## [48.0.5](https://github.com/compare/...@sage/xtrem~wh-services@48.0.5) (2024-09-22)

### Bug Fixes

### Features


## [48.0.4](https://github.com/compare/...@sage/xtrem~wh-services@48.0.4) (2024-09-21)

### Bug Fixes

### Features


## [48.0.3](https://github.com/compare/...@sage/xtrem~wh-services@48.0.3) (2024-09-20)

### Bug Fixes

### Features


## [48.0.2](https://github.com/compare/...@sage/xtrem~wh-services@48.0.2) (2024-09-19)

### Bug Fixes

### Features


## [48.0.1](https://github.com/compare/...@sage/xtrem~wh-services@48.0.1) (2024-09-19)

### Bug Fixes

### Features


## [48.0.0](https://github.com/compare/...@sage/xtrem~wh-services@48.0.0) (2024-09-19)

### Bug Fixes

### Features


## [47.0.30](https://github.com/compare/...@sage/xtrem~wh-services@47.0.30) (2024-09-19)

### Bug Fixes

### Features


## [47.0.29](https://github.com/compare/...@sage/xtrem~wh-services@47.0.29) (2024-09-18)

### Bug Fixes

### Features


## [47.0.28](https://github.com/compare/...@sage/xtrem~wh-services@47.0.28) (2024-09-17)

### Bug Fixes
* XT-78652 Modify filter to include extension properties ([XT-78652](https://jira.sage.com/browse/XT-78652)) ([#21110](https://github.com/Sage-ERP-X3/xtrem/issues/21110))   ([e466d9d](https://github.com/Sage-ERP-X3/xtrem/commit/e466d9df5db1711ffadee95bdf1a64b7d0df6544))

### Features


## [47.0.27](https://github.com/compare/...@sage/xtrem~wh-services@47.0.27) (2024-09-16)

### Bug Fixes

### Features


## [47.0.26](https://github.com/compare/...@sage/xtrem~wh-services@47.0.26) (2024-09-15)

### Bug Fixes

### Features


## [47.0.25](https://github.com/compare/...@sage/xtrem~wh-services@47.0.25) (2024-09-14)

### Bug Fixes

### Features


## [47.0.24](https://github.com/compare/...@sage/xtrem~wh-services@47.0.24) (2024-09-13)

### Bug Fixes

### Features
* **wh-stock:** X3-319946 - fix navigator filter ([X3-319946](https://jira.sage.com/browse/X3-319946)) ([#21047](https://github.com/Sage-ERP-X3/xtrem/issues/21047))   ([0d313ca](https://github.com/Sage-ERP-X3/xtrem/commit/0d313ca483f2cada16e85e0f96aa7e520c1b9d57))
* **wh-stock:** X3-319946 - Fix miscellaneous texts ([X3-319946](https://jira.sage.com/browse/X3-319946)) ([#21038](https://github.com/Sage-ERP-X3/xtrem/issues/21038))   ([6793d4e](https://github.com/Sage-ERP-X3/xtrem/commit/6793d4ea00b9a9b9b254d1755a94f805b2cc421f))


## [47.0.23](https://github.com/compare/...@sage/xtrem~wh-services@47.0.23) (2024-09-12)

### Bug Fixes

### Features


## [47.0.22](https://github.com/compare/...@sage/xtrem~wh-services@47.0.22) (2024-09-11)

### Bug Fixes

### Features
* **wh-services:** X3-317546 - Implement mutations for sticker site depositor ([X3-317546](https://jira.sage.com/browse/X3-317546)) ([#19915](https://github.com/Sage-ERP-X3/xtrem/issues/19915))   ([a57e86a](https://github.com/Sage-ERP-X3/xtrem/commit/a57e86a34ee08319fe0e22d7ccdd56a94fc65b37))


## [47.0.21](https://github.com/compare/...@sage/xtrem~wh-services@47.0.21) (2024-09-10)

### Bug Fixes

### Features


## [47.0.20](https://github.com/compare/...@sage/xtrem~wh-services@47.0.20) (2024-09-09)

### Bug Fixes

### Features
* **wh-stock:** X3-261140 - WH Mobile Automation - View Stock by location ([X3-261140](https://jira.sage.com/browse/X3-261140)) ([#20281](https://github.com/Sage-ERP-X3/xtrem/issues/20281))   ([299fc6c](https://github.com/Sage-ERP-X3/xtrem/commit/299fc6c39e358841ac26a6bf293f9cfda85ad053))


## [47.0.19](https://github.com/compare/...@sage/xtrem~wh-services@47.0.19) (2024-09-08)

### Bug Fixes

### Features


## [47.0.18](https://github.com/compare/...@sage/xtrem~wh-services@47.0.18) (2024-09-07)

### Bug Fixes

### Features


## [47.0.17](https://github.com/compare/...@sage/xtrem~wh-services@47.0.17) (2024-09-06)

### Bug Fixes

### Features


## [47.0.16](https://github.com/compare/...@sage/xtrem~wh-services@47.0.16) (2024-09-05)

### Bug Fixes

### Features


## [47.0.15](https://github.com/compare/...@sage/xtrem~wh-services@47.0.15) (2024-09-04)

### Bug Fixes

### Features
* **wh-stock-data:** X3-304144 ([X3-304144](https://jira.sage.com/browse/X3-304144)) ([#20302](https://github.com/Sage-ERP-X3/xtrem/issues/20302))   ([53df7ae](https://github.com/Sage-ERP-X3/xtrem/commit/53df7aee163f1d32ea5977fcc65f9c43d1005ef0))


## [47.0.14](https://github.com/compare/...@sage/xtrem~wh-services@47.0.14) (2024-09-03)

### Bug Fixes

### Features


## [47.0.13](https://github.com/compare/...@sage/xtrem~wh-services@47.0.13) (2024-09-02)

### Bug Fixes

### Features
* **wh-services:** X3-261140 - update artefacts ([X3-261140](https://jira.sage.com/browse/X3-261140)) ([#20752](https://github.com/Sage-ERP-X3/xtrem/issues/20752))   ([3298f7d](https://github.com/Sage-ERP-X3/xtrem/commit/3298f7d9f79266c08e914b7a024b087343284868))


## [47.0.12](https://github.com/compare/...@sage/xtrem~wh-services@47.0.12) (2024-09-01)

### Bug Fixes

### Features


## [47.0.11](https://github.com/compare/...@sage/xtrem~wh-services@47.0.11) (2024-08-31)

### Bug Fixes

### Features


## [47.0.10](https://github.com/compare/...@sage/xtrem~wh-services@47.0.10) (2024-08-30)

### Bug Fixes

### Features


## [47.0.9](https://github.com/compare/...@sage/xtrem~wh-services@47.0.9) (2024-08-29)

### Bug Fixes

### Features


## [47.0.8](https://github.com/compare/...@sage/xtrem~wh-services@47.0.8) (2024-08-28)

### Bug Fixes

### Features
* XT-75479 ServiceOption change callbacks ([XT-75479](https://jira.sage.com/browse/XT-75479)) ([#20447](https://github.com/Sage-ERP-X3/xtrem/issues/20447))   ([a1b787e](https://github.com/Sage-ERP-X3/xtrem/commit/a1b787e3da433b0eebc8a722db680530be6d1907))


## [47.0.7](https://github.com/compare/...@sage/xtrem~wh-services@47.0.7) (2024-08-27)

### Bug Fixes

### Features


## [47.0.6](https://github.com/compare/...@sage/xtrem~wh-services@47.0.6) (2024-08-26)

### Bug Fixes

### Features


## [47.0.5](https://github.com/compare/...@sage/xtrem~wh-services@47.0.5) (2024-08-25)

### Bug Fixes

### Features


## [47.0.4](https://github.com/compare/...@sage/xtrem~wh-services@47.0.4) (2024-08-24)

### Bug Fixes

### Features


## [47.0.3](https://github.com/compare/...@sage/xtrem~wh-services@47.0.3) (2024-08-23)

### Bug Fixes

### Features


## [47.0.2](https://github.com/compare/...@sage/xtrem~wh-services@47.0.2) (2024-08-22)

### Bug Fixes

### Features


## [47.0.1](https://github.com/compare/...@sage/xtrem~wh-services@47.0.1) (2024-08-22)

### Bug Fixes

### Features


## [47.0.0](https://github.com/compare/...@sage/xtrem~wh-services@47.0.0) (2024-08-22)

### Bug Fixes

### Features


## [46.0.38](https://github.com/compare/...@sage/xtrem~wh-services@46.0.38) (2024-08-22)

### Bug Fixes

### Features


## [46.0.37](https://github.com/compare/...@sage/xtrem~wh-services@46.0.37) (2024-08-21)

### Bug Fixes

### Features


## [46.0.36](https://github.com/compare/...@sage/xtrem~wh-services@46.0.36) (2024-08-20)

### Bug Fixes

### Features


## [46.0.35](https://github.com/compare/...@sage/xtrem~wh-services@46.0.35) (2024-08-18)

### Bug Fixes

### Features


## [46.0.34](https://github.com/compare/...@sage/xtrem~wh-services@46.0.34) (2024-08-17)

### Bug Fixes

### Features


## [46.0.33](https://github.com/compare/...@sage/xtrem~wh-services@46.0.33) (2024-08-16)

### Bug Fixes

### Features


## [46.0.32](https://github.com/compare/...@sage/xtrem~wh-services@46.0.32) (2024-08-15)

### Bug Fixes

### Features


## [46.0.31](https://github.com/compare/...@sage/xtrem~wh-services@46.0.31) (2024-08-14)

### Bug Fixes

### Features


## [46.0.30](https://github.com/compare/...@sage/xtrem~wh-services@46.0.30) (2024-08-13)

### Bug Fixes

### Features


## [46.0.29](https://github.com/compare/...@sage/xtrem~wh-services@46.0.29) (2024-08-12)

### Bug Fixes

### Features


## [46.0.28](https://github.com/compare/...@sage/xtrem~wh-services@46.0.28) (2024-08-11)

### Bug Fixes

### Features


## [46.0.27](https://github.com/compare/...@sage/xtrem~wh-services@46.0.27) (2024-08-10)

### Bug Fixes

### Features


## [46.0.26](https://github.com/compare/...@sage/xtrem~wh-services@46.0.26) (2024-08-09)

### Bug Fixes

### Features


## [46.0.25](https://github.com/compare/...@sage/xtrem~wh-services@46.0.25) (2024-08-08)

### Bug Fixes

### Features


## [46.0.24](https://github.com/compare/...@sage/xtrem~wh-services@46.0.24) (2024-08-07)

### Bug Fixes

### Features
* **wh-stock:** X3-316336 - Fix reason method blocked by inventory ([X3-316336](https://jira.sage.com/browse/X3-316336)) ([#20274](https://github.com/Sage-ERP-X3/xtrem/issues/20274))   ([76ebbbf](https://github.com/Sage-ERP-X3/xtrem/commit/76ebbbfd69a62a62ca4e15242dc288c8674418e3))
* x3 315126 refac npmignore to package json   ([#20276](https://github.com/Sage-ERP-X3/xtrem/issues/20276))   ([8f548d2](https://github.com/Sage-ERP-X3/xtrem/commit/8f548d280629e863e171e93ed807c0a64cfff9b6))


## [46.0.23](https://github.com/compare/...@sage/xtrem~wh-services@46.0.23) (2024-08-06)

### Bug Fixes

### Features
* **wh-stock:** X3-316336 - Add view stock by product page ([X3-316336](https://jira.sage.com/browse/X3-316336)) ([#20047](https://github.com/Sage-ERP-X3/xtrem/issues/20047))   ([3e1cee5](https://github.com/Sage-ERP-X3/xtrem/commit/3e1cee5ace4c2898b30d77b4035c218f1b60c2cf))


## [46.0.22](https://github.com/compare/...@sage/xtrem~wh-services@46.0.22) (2024-08-05)

### Bug Fixes

### Features


## [46.0.21](https://github.com/compare/...@sage/xtrem~wh-services@46.0.21) (2024-08-05)

### Bug Fixes

### Features


## [46.0.20](https://github.com/compare/...@sage/xtrem~wh-services@46.0.20) (2024-08-04)

### Bug Fixes

### Features


## [46.0.19](https://github.com/compare/...@sage/xtrem~wh-services@46.0.19) (2024-08-03)

### Bug Fixes

### Features


## [46.0.18](https://github.com/compare/...@sage/xtrem~wh-services@46.0.18) (2024-08-02)

### Bug Fixes

### Features


## [46.0.17](https://github.com/compare/...@sage/xtrem~wh-services@46.0.17) (2024-08-02)

### Bug Fixes

### Features


## [46.0.16](https://github.com/compare/...@sage/xtrem~wh-services@46.0.16) (2024-07-31)

### Bug Fixes

### Features


## [46.0.15](https://github.com/compare/...@sage/xtrem~wh-services@46.0.15) (2024-07-31)

### Bug Fixes

### Features


## [46.0.14](https://github.com/compare/...@sage/xtrem~wh-services@46.0.14) (2024-07-30)

### Bug Fixes

### Features


## [46.0.13](https://github.com/compare/...@sage/xtrem~wh-services@46.0.13) (2024-07-29)

### Bug Fixes

### Features


## [46.0.12](https://github.com/compare/...@sage/xtrem~wh-services@46.0.12) (2024-07-28)

### Bug Fixes

### Features


## [46.0.11](https://github.com/compare/...@sage/xtrem~wh-services@46.0.11) (2024-07-27)

### Bug Fixes

### Features


## [46.0.10](https://github.com/compare/...@sage/xtrem~wh-services@46.0.10) (2024-07-26)

### Bug Fixes

### Features


## [46.0.9](https://github.com/compare/...@sage/xtrem~wh-services@46.0.9) (2024-07-25)

### Bug Fixes

### Features


## [46.0.8](https://github.com/compare/...@sage/xtrem~wh-services@46.0.8) (2024-07-24)

### Bug Fixes

### Features
* **wh-master-data:** X3-317679 - Move wh-structure-data package and enums to wh-master-data ([X3-317679](https://jira.sage.com/browse/X3-317679)) ([#19927](https://github.com/Sage-ERP-X3/xtrem/issues/19927))   ([48d276e](https://github.com/Sage-ERP-X3/xtrem/commit/48d276e977598950d762cbc10d81ac73471d037f))


## [46.0.7](https://github.com/compare/...@sage/xtrem~wh-services@46.0.7) (2024-07-23)

### Bug Fixes

### Features


## [46.0.6](https://github.com/compare/...@sage/xtrem~wh-services@46.0.6) (2024-07-22)

### Bug Fixes

### Features
* **wh-system:** X3-317732 - add new parametersUserValues mutations ([X3-317732](https://jira.sage.com/browse/X3-317732)) ([#19975](https://github.com/Sage-ERP-X3/xtrem/issues/19975))   ([20e7773](https://github.com/Sage-ERP-X3/xtrem/commit/20e777392111c82efb48d0869d4a0d28ef7aed68))


## [46.0.5](https://github.com/compare/...@sage/xtrem~wh-services@46.0.5) (2024-07-21)

### Bug Fixes

### Features


## [46.0.4](https://github.com/compare/...@sage/xtrem~wh-services@46.0.4) (2024-07-20)

### Bug Fixes

### Features


## [46.0.3](https://github.com/compare/...@sage/xtrem~wh-services@46.0.3) (2024-07-19)

### Bug Fixes

### Features


## [46.0.2](https://github.com/compare/...@sage/xtrem~wh-services@46.0.2) (2024-07-18)

### Bug Fixes

### Features


## [46.0.1](https://github.com/compare/...@sage/xtrem~wh-services@46.0.1) (2024-07-18)

### Bug Fixes

### Features


## [46.0.0](https://github.com/compare/...@sage/xtrem~wh-services@46.0.0) (2024-07-18)

### Bug Fixes

### Features


## [45.0.30](https://github.com/compare/...@sage/xtrem~wh-services@45.0.30) (2024-07-17)

### Bug Fixes

### Features


## [45.0.29](https://github.com/compare/...@sage/xtrem~wh-services@45.0.29) (2024-07-16)

### Bug Fixes

### Features
* **wh-service:** X3-317191 - implement sticker site depositor ([X3-317191](https://jira.sage.com/browse/X3-317191)) ([#19785](https://github.com/Sage-ERP-X3/xtrem/issues/19785))   ([f2cf9a0](https://github.com/Sage-ERP-X3/xtrem/commit/f2cf9a05e3986d7066fefa02b373a4140d77b398))
* **wh-services:** X3-317364 - Implement supervisor functions ([X3-317364](https://jira.sage.com/browse/X3-317364)) ([#19800](https://github.com/Sage-ERP-X3/xtrem/issues/19800))   ([7fd36bf](https://github.com/Sage-ERP-X3/xtrem/commit/7fd36bf46763dc885501058ab31f5d02cc1165e1))


## [45.0.28](https://github.com/compare/...@sage/xtrem~wh-services@45.0.28) (2024-07-15)

### Bug Fixes

### Features


## [45.0.27](https://github.com/compare/...@sage/xtrem~wh-services@45.0.27) (2024-07-15)

### Bug Fixes

### Features


## [45.0.26](https://github.com/compare/...@sage/xtrem~wh-services@45.0.26) (2024-07-14)

### Bug Fixes

### Features


## [45.0.25](https://github.com/compare/...@sage/xtrem~wh-services@45.0.25) (2024-07-13)

### Bug Fixes

### Features


## [45.0.24](https://github.com/compare/...@sage/xtrem~wh-services@45.0.24) (2024-07-12)

### Bug Fixes

### Features


## [45.0.23](https://github.com/compare/...@sage/xtrem~wh-services@45.0.23) (2024-07-11)

### Bug Fixes

### Features
* **wh-service:** X3-317193 - implement sticker destination ([X3-317193](https://jira.sage.com/browse/X3-317193)) ([#19783](https://github.com/Sage-ERP-X3/xtrem/issues/19783))   ([51bb2e7](https://github.com/Sage-ERP-X3/xtrem/commit/51bb2e73db08041918990df9eebc6587abfaecf0))


## [45.0.22](https://github.com/compare/...@sage/xtrem~wh-services@45.0.22) (2024-07-10)

### Bug Fixes

### Features
* **wh-services:** X3-316866 - API GEODE - implement some collections ([X3-316866](https://jira.sage.com/browse/X3-316866)) ([#19666](https://github.com/Sage-ERP-X3/xtrem/issues/19666))   ([4a8271f](https://github.com/Sage-ERP-X3/xtrem/commit/4a8271f04a57df5ffd46f8411b770cd7a7886a5a))


## [45.0.21](https://github.com/compare/...@sage/xtrem~wh-services@45.0.21) (2024-07-09)

### Bug Fixes

### Features
* **main:** change eslintrc-base same as xtrem/ eslint-truth XT-74269 ([XT-74269](https://jira.sage.com/browse/XT-74269)) ([#19781](https://github.com/Sage-ERP-X3/xtrem/issues/19781))   ([bac91b0](https://github.com/Sage-ERP-X3/xtrem/commit/bac91b03c220498e059fc9481ec5722369836b0d))
* **wh-structure-data:** X3-317194 - Add menu-items ([X3-317194](https://jira.sage.com/browse/X3-317194)) ([#19776](https://github.com/Sage-ERP-X3/xtrem/issues/19776))   ([9167e05](https://github.com/Sage-ERP-X3/xtrem/commit/9167e05de4f16ac02dccd007b05587123ff663cd))


## [45.0.20](https://github.com/compare/...@sage/xtrem~wh-services@45.0.20) (2024-07-08)

### Bug Fixes

### Features


## [45.0.19](https://github.com/compare/...@sage/xtrem~wh-services@45.0.19) (2024-07-07)

### Bug Fixes

### Features


## [45.0.18](https://github.com/compare/...@sage/xtrem~wh-services@45.0.18) (2024-07-06)

### Bug Fixes

### Features


## [45.0.17](https://github.com/compare/...@sage/xtrem~wh-services@45.0.17) (2024-07-05)

### Bug Fixes

### Features


## [45.0.16](https://github.com/compare/...@sage/xtrem~wh-services@45.0.16) (2024-07-04)

### Bug Fixes

### Features
* **queues:** sort routing to avoid diff XT-72346 ([XT-72346](https://jira.sage.com/browse/XT-72346)) ([#19718](https://github.com/Sage-ERP-X3/xtrem/issues/19718))   ([6871c73](https://github.com/Sage-ERP-X3/xtrem/commit/6871c73b2e6e1124e1036467320619a3b0cad41a))
* XT-69536 generate api.d.ts files with _factory property ([XT-69536](https://jira.sage.com/browse/XT-69536)) ([#19715](https://github.com/Sage-ERP-X3/xtrem/issues/19715))   ([be8ec04](https://github.com/Sage-ERP-X3/xtrem/commit/be8ec04c82b99358c72487dfc609f9dcd4a88f16))


## [45.0.15](https://github.com/compare/...@sage/xtrem~wh-services@45.0.15) (2024-07-03)

### Bug Fixes

### Features
* **queues:** regen queue conf XT-72236 ([XT-72236](https://jira.sage.com/browse/XT-72236)) ([#19677](https://github.com/Sage-ERP-X3/xtrem/issues/19677))   ([c530021](https://github.com/Sage-ERP-X3/xtrem/commit/c53002183312eb6983c30cfdebe68d007f80743d))


## [45.0.14](https://github.com/compare/...@sage/xtrem~wh-services@45.0.14) (2024-07-02)

### Bug Fixes

### Features


## [45.0.13](https://github.com/compare/...@sage/xtrem~wh-services@45.0.13) (2024-07-01)

### Bug Fixes

### Features
* **wh-services:** X3-315712 - API GEODE - implement wh stock ([X3-315712](https://jira.sage.com/browse/X3-315712)) ([#19614](https://github.com/Sage-ERP-X3/xtrem/issues/19614))   ([e84c2ad](https://github.com/Sage-ERP-X3/xtrem/commit/e84c2ad9bbf394cffc470348680cbfdbde915ec9))


## [45.0.12](https://github.com/compare/...@sage/xtrem~wh-services@45.0.12) (2024-06-30)

### Bug Fixes

### Features


## [45.0.11](https://github.com/compare/...@sage/xtrem~wh-services@45.0.11) (2024-06-29)

### Bug Fixes

### Features


## [45.0.10](https://github.com/compare/...@sage/xtrem~wh-services@45.0.10) (2024-06-28)

### Bug Fixes

### Features


## [45.0.9](https://github.com/compare/...@sage/xtrem~wh-services@45.0.9) (2024-06-27)

### Bug Fixes

### Features


## [45.0.8](https://github.com/compare/...@sage/xtrem~wh-services@45.0.8) (2024-06-26)

### Bug Fixes

### Features


## [45.0.7](https://github.com/compare/...@sage/xtrem~wh-services@45.0.7) (2024-06-25)

### Bug Fixes

### Features


## [45.0.6](https://github.com/compare/...@sage/xtrem~wh-services@45.0.6) (2024-06-24)

### Bug Fixes

### Features
* **wh-services:** X3-315683 - Update artefacts ([X3-315683](https://jira.sage.com/browse/X3-315683)) ([#19514](https://github.com/Sage-ERP-X3/xtrem/issues/19514))   ([1f10a3c](https://github.com/Sage-ERP-X3/xtrem/commit/1f10a3c44be73d9d51bd5e0989ebc57ca9dcb899))


## [45.0.5](https://github.com/compare/...@sage/xtrem~wh-services@45.0.5) (2024-06-23)

### Bug Fixes

### Features


## [45.0.4](https://github.com/compare/...@sage/xtrem~wh-services@45.0.4) (2024-06-22)

### Bug Fixes

### Features


## [45.0.3](https://github.com/compare/...@sage/xtrem~wh-services@45.0.3) (2024-06-21)

### Bug Fixes

### Features


## [45.0.2](https://github.com/compare/...@sage/xtrem~wh-services@45.0.2) (2024-06-20)

### Bug Fixes

### Features


## [45.0.1](https://github.com/compare/...@sage/xtrem~wh-services@45.0.1) (2024-06-20)

### Bug Fixes

### Features


## [45.0.0](https://github.com/compare/...@sage/xtrem~wh-services@45.0.0) (2024-06-20)

### Bug Fixes

### Features


## [44.0.31](https://github.com/compare/...@sage/xtrem~wh-services@44.0.31) (2024-06-19)

### Bug Fixes

### Features


## [44.0.30](https://github.com/compare/...@sage/xtrem~wh-services@44.0.30) (2024-06-18)

### Bug Fixes

### Features


## [44.0.29](https://github.com/compare/...@sage/xtrem~wh-services@44.0.29) (2024-06-18)

### Bug Fixes

### Features
* **wh-input:** X3-315682 ([X3-315682](https://jira.sage.com/browse/X3-315682)) ([#19388](https://github.com/Sage-ERP-X3/xtrem/issues/19388))   ([a0811d4](https://github.com/Sage-ERP-X3/xtrem/commit/a0811d42b4b0635adda423eabf624705d8fc5d58))


## [44.0.28](https://github.com/compare/...@sage/xtrem~wh-services@44.0.28) (2024-06-17)

### Bug Fixes

### Features


## [44.0.27](https://github.com/compare/...@sage/xtrem~wh-services@44.0.27) (2024-06-16)

### Bug Fixes

### Features


## [44.0.26](https://github.com/compare/...@sage/xtrem~wh-services@44.0.26) (2024-06-15)

### Bug Fixes

### Features


## [44.0.25](https://github.com/compare/...@sage/xtrem~wh-services@44.0.25) (2024-06-14)

### Bug Fixes

### Features


## [44.0.24](https://github.com/compare/...@sage/xtrem~wh-services@44.0.24) (2024-06-13)

### Bug Fixes

### Features


## [44.0.23](https://github.com/compare/...@sage/xtrem~wh-services@44.0.23) (2024-06-12)

### Bug Fixes

### Features


## [44.0.22](https://github.com/compare/...@sage/xtrem~wh-services@44.0.22) (2024-06-11)

### Bug Fixes

### Features


## [44.0.21](https://github.com/compare/...@sage/xtrem~wh-services@44.0.21) (2024-06-10)

### Bug Fixes

### Features


## [44.0.20](https://github.com/compare/...@sage/xtrem~wh-services@44.0.20) (2024-06-09)

### Bug Fixes

### Features


## [44.0.19](https://github.com/compare/...@sage/xtrem~wh-services@44.0.19) (2024-06-08)

### Bug Fixes

### Features


## [44.0.18](https://github.com/compare/...@sage/xtrem~wh-services@44.0.18) (2024-06-07)

### Bug Fixes

### Features


## [44.0.17](https://github.com/compare/...@sage/xtrem~wh-services@44.0.17) (2024-06-06)

### Bug Fixes

### Features


## [44.0.16](https://github.com/compare/...@sage/xtrem~wh-services@44.0.16) (2024-06-05)

### Bug Fixes

### Features


## [44.0.15](https://github.com/compare/...@sage/xtrem~wh-services@44.0.15) (2024-06-04)

### Bug Fixes

### Features


## [44.0.14](https://github.com/compare/...@sage/xtrem~wh-services@44.0.14) (2024-06-03)

### Bug Fixes

### Features


## [44.0.13](https://github.com/compare/...@sage/xtrem~wh-services@44.0.13) (2024-06-02)

### Bug Fixes

### Features


## [44.0.12](https://github.com/compare/...@sage/xtrem~wh-services@44.0.12) (2024-06-01)

### Bug Fixes

### Features


## [44.0.11](https://github.com/compare/...@sage/xtrem~wh-services@44.0.11) (2024-05-31)

### Bug Fixes

### Features


## [44.0.10](https://github.com/compare/...@sage/xtrem~wh-services@44.0.10) (2024-05-30)

### Bug Fixes

### Features


## [44.0.9](https://github.com/compare/...@sage/xtrem~wh-services@44.0.9) (2024-05-30)

### Bug Fixes

### Features


## [44.0.8](https://github.com/compare/...@sage/xtrem~wh-services@44.0.8) (2024-05-29)

### Bug Fixes

### Features


## [44.0.7](https://github.com/compare/...@sage/xtrem~wh-services@44.0.7) (2024-05-29)

### Bug Fixes

### Features


## [44.0.6](https://github.com/compare/...@sage/xtrem~wh-services@44.0.6) (2024-05-27)

### Bug Fixes

### Features


## [44.0.5](https://github.com/compare/...@sage/xtrem~wh-services@44.0.5) (2024-05-26)

### Bug Fixes

### Features


## [44.0.4](https://github.com/compare/...@sage/xtrem~wh-services@44.0.4) (2024-05-25)

### Bug Fixes

### Features


## [44.0.3](https://github.com/compare/...@sage/xtrem~wh-services@44.0.3) (2024-05-24)

### Bug Fixes

### Features


## [44.0.2](https://github.com/compare/...@sage/xtrem~wh-services@44.0.2) (2024-05-23)

### Bug Fixes

### Features


## [44.0.1](https://github.com/compare/...@sage/xtrem~wh-services@44.0.1) (2024-05-23)

### Bug Fixes

### Features


## [44.0.0](https://github.com/compare/...@sage/xtrem~wh-services@44.0.0) (2024-05-23)

### Bug Fixes

### Features


## [43.0.34](https://github.com/compare/...@sage/xtrem~wh-services@43.0.34) (2024-05-22)

### Bug Fixes

### Features


## [43.0.33](https://github.com/compare/...@sage/xtrem~wh-services@43.0.33) (2024-05-21)

### Bug Fixes

### Features


## [43.0.32](https://github.com/compare/...@sage/xtrem~wh-services@43.0.32) (2024-05-20)

### Bug Fixes

### Features


## [43.0.31](https://github.com/compare/...@sage/xtrem~wh-services@43.0.31) (2024-05-19)

### Bug Fixes

### Features


## [43.0.30](https://github.com/compare/...@sage/xtrem~wh-services@43.0.30) (2024-05-18)

### Bug Fixes

### Features


## [43.0.29](https://github.com/compare/...@sage/xtrem~wh-services@43.0.29) (2024-05-17)

### Bug Fixes

### Features


## [43.0.28](https://github.com/compare/...@sage/xtrem~wh-services@43.0.28) (2024-05-16)

### Bug Fixes

### Features


## [43.0.27](https://github.com/compare/...@sage/xtrem~wh-services@43.0.27) (2024-05-15)

### Bug Fixes

### Features


## [43.0.26](https://github.com/compare/...@sage/xtrem~wh-services@43.0.26) (2024-05-14)

### Bug Fixes

### Features


## [43.0.25](https://github.com/compare/...@sage/xtrem~wh-services@43.0.25) (2024-05-13)

### Bug Fixes

### Features


## [43.0.24](https://github.com/compare/...@sage/xtrem~wh-services@43.0.24) (2024-05-12)

### Bug Fixes

### Features


## [43.0.23](https://github.com/compare/...@sage/xtrem~wh-services@43.0.23) (2024-05-11)

### Bug Fixes

### Features


## [43.0.22](https://github.com/compare/...@sage/xtrem~wh-services@43.0.22) (2024-05-10)

### Bug Fixes

### Features


## [43.0.21](https://github.com/compare/...@sage/xtrem~wh-services@43.0.21) (2024-05-09)

### Bug Fixes

### Features


## [43.0.20](https://github.com/compare/...@sage/xtrem~wh-services@43.0.20) (2024-05-08)

### Bug Fixes

### Features


## [43.0.19](https://github.com/compare/...@sage/xtrem~wh-services@43.0.19) (2024-05-07)

### Bug Fixes

### Features


## [43.0.18](https://github.com/compare/...@sage/xtrem~wh-services@43.0.18) (2024-05-06)

### Bug Fixes

### Features


## [43.0.17](https://github.com/compare/...@sage/xtrem~wh-services@43.0.17) (2024-05-06)

### Bug Fixes

### Features
* X3-313907 implement wh-services on repo ([X3-313907](https://jira.sage.com/browse/X3-313907)) ([#18824](https://github.com/Sage-ERP-X3/xtrem/issues/18824))   ([0a8b787](https://github.com/Sage-ERP-X3/xtrem/commit/0a8b7872fb9d38d5a141d8c32948735d1d61fb3a))

# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.
