{"name": "@sage/wh-main", "description": "WH Services main package", "xtrem": {"isMain": true, "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": [], "author": "Sage", "license": "UNLICENSED", "files": ["build", "data", "sql", "routing.json"], "scripts": {"start": "xtrem start", "xtrem": "xtrem"}, "version": "54.0.58", "dependencies": {"@sage/wh-input": "workspace:*", "@sage/wh-input-output": "workspace:*", "@sage/wh-input-stock": "workspace:*", "@sage/wh-output": "workspace:*", "@sage/wh-output-stock": "workspace:*", "@sage/wh-stock": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-x3-adc-ui": "workspace:*", "@sage/xtrem-x3-interop": "workspace:*", "@sage/xtrem-x3-pages-ui": "workspace:*"}}