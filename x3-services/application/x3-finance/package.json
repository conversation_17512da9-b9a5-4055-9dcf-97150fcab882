{"name": "@sage/x3-finance", "description": "Sage X3 Finance", "version": "54.0.58", "author": "sage", "license": "UNLICENSED", "xtrem": {"packageName": "@sage/x3-finance", "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": ["xtrem-x3-services-application-package"], "main": "build/index.js", "files": ["build", "api", "lib/pages", "lib/page-extensions", "lib/widgets", "lib/page-fragments", "lib/stickers", "lib/client-functions", "lib/shared-functions", "README.md", "CHANGELOG.md"], "typings": "build/package-definition.d.ts", "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "generate-package": "xtrem x3-dev generate-package", "generate-migrate-translations": "xtrem x3-dev generate-migrate-translations", "generate-translations": "xtrem x3-dev generate-translations", "lint": "xtrem lint", "start": "xtrem start", "test": "xtrem test --unit --graphql", "test:ci": "xtrem test --unit --ci", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}, "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "dependencies": {"@sage/x3-finance-data": "workspace:*", "@sage/x3-invoicing-data": "workspace:*", "@sage/x3-manufacturing-data": "workspace:*", "@sage/x3-master-data": "workspace:*", "@sage/x3-stock-data": "workspace:*", "@sage/x3-system": "workspace:*", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-x3-gateway": "workspace:*", "@sage/xtrem-x3-syracuse": "workspace:*", "@sage/xtrem-x3-system-utils": "workspace:*"}}