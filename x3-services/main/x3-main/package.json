{"name": "@sage/x3-main", "description": "X3 Services main package", "xtrem": {"isMain": true, "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "keywords": [], "author": "Sage", "license": "UNLICENSED", "files": ["build", "data", "sql", "routing.json"], "scripts": {"start": "xtrem start", "xtrem": "xtrem"}, "version": "54.0.58", "dependencies": {"@sage/x3-finance": "workspace:*", "@sage/x3-manufacturing": "workspace:*", "@sage/x3-project-management": "workspace:*", "@sage/x3-project-management-manufacturing": "workspace:*", "@sage/x3-project-management-stock": "workspace:*", "@sage/x3-purchasing": "workspace:*", "@sage/x3-purchasing-finance": "workspace:*", "@sage/x3-purchasing-manufacturing": "workspace:*", "@sage/x3-purchasing-sales": "workspace:*", "@sage/x3-sales": "workspace:*", "@sage/x3-sales-finance": "workspace:*", "@sage/x3-sales-manufacturing": "workspace:*", "@sage/x3-sales-project-management": "workspace:*", "@sage/x3-sales-stock": "workspace:*", "@sage/x3-stock": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-x3-adc-ui": "workspace:*", "@sage/xtrem-x3-interop": "workspace:*", "@sage/xtrem-x3-pages-ui": "workspace:*"}}