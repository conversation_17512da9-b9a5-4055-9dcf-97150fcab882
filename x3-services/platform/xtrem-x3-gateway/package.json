{"name": "@sage/xtrem-x3-gateway", "version": "54.0.58", "description": "Xtrem X3 Gateway", "files": ["build", "test/metadata"], "main": "build/index.js", "types": "build/index.d.ts", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "keywords": [], "author": "Sage", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ts-to-sql": "workspace:*", "@sage/xtrem-x3-sql": "workspace:*", "@sage/xtrem-x3-sql-manager": "workspace:*", "axios": "^1.8.4", "espree": "^10.1.0", "js-xml": "^2.0.0", "js-yaml": "^4.1.0", "json5": "^2.2.3", "lodash": "^4.17.21"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/estree": "1.0.6", "@types/js-yaml": "^4.0.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/source-map-support": "^0.5.7", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "mocha-jenkins-reporter": "^0.4.1", "source-map-support": "^0.5.12", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-core.xml JUNIT_REPORT_NAME='xtrem-x3-gateway' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "xtrem-bytenode": "xtrem-bytenode"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}