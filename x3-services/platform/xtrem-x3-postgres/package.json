{"name": "@sage/xtrem-x3-postgres", "description": "XTREM-X3 Postgres driver", "version": "54.0.58", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-postgres": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-x3-sql": "workspace:*"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@types/node": "^22.10.2", "@types/source-map-support": "^0.5.7", "eslint": "^8.49.0", "source-map-support": "^0.5.12", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "eslint -c .eslintrc.js --ext .ts lib", "test": "echo \"Error: no test specified for xtrem-config\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-config\" && exit 0", "xtrem-bytenode": "xtrem-bytenode"}}