{"name": "@sage/xtrem-x3-adc-ui", "version": "54.0.58", "description": "Standalone ADC Xtrem X3 deployment using Syracuse authentication services and request forwarding", "main": "build/bundle.js", "xtrem": {"isFrontEndApp": true, "isReleased": true, "sqlSchemaVersion": "54.0.33"}, "scripts": {"test": "cross-env NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-x3-adc-ui\" JEST_JUNIT_OUTPUT=\"./junit-report-xtrem-x3-adc-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" jest --config=jest.config.js", "build:tests": "tsc -b tsconfig.debug.json", "test:ci": "pnpm build:tests && cross-env NODE_ENV=\"test\" NO_CONSOLE=1 JEST_SUITE_NAME=\"xtrem-x3-adc-ui\" JEST_JUNIT_OUTPUT_NAME=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --config=jest.config.js --no-cache --coverage  --maxWorkers=3", "start": "npm run build && node webpack/syracuse-mock", "build": "rimraf build && cross-env NODE_OPTIONS=\"--max_old_space_size=4096 --openssl-legacy-provider\" webpack --config ./webpack/dev.js", "build:binary": "rimraf build && cross-env NODE_OPTIONS=\"--max_old_space_size=4096 --openssl-legacy-provider\" webpack --config ./webpack/prod.js", "lint": "eslint -c .eslintrc.js --ext .ts --ext .tsx lib", "lint:fix": "npm run prettier:write && npm run lint -- --fix", "prettier:check": "prettier --trailing-comma all --list-different \"lib/**/*.{ts,tsx}\"", "prettier:write": "prettier --trailing-comma all --write \"lib/**/*.{ts,tsx}\" package.json"}, "keywords": [], "author": "<EMAIL>", "license": "UNLICENSED", "files": ["build"], "dependencies": {"@sage/design-tokens": "4.35.0", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui": "workspace:*", "axios": "^1.8.4", "carbon-react": "146.2.1", "core-js": "^3.3.3", "draft-js": "^0.11.7", "fingerprintjs2": "^2.1.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-is": "^18.3.1", "react-redux": "^9.0.0", "redux": "^4.0.4", "redux-thunk": "^2.3.0", "stream-browserify": "^3.0.0", "styled-components": "^5.3.11", "timers-browserify": "^2.0.12", "use-fit-text": "^2.4.0", "uuid": "^9.0.1"}, "devDependencies": {"@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-static-shared": "workspace:*", "@side/jest-runtime": "^1.1.0", "@swc/core": "1.2.173", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.0.0", "@types/fingerprintjs2": "^2.0.0", "@types/jest": "^29.5.7", "@types/lodash": "^4.14.198", "@types/react": "^18.3.3", "@types/react-dom": "^18.0.0", "@types/react-redux": "^7.1.5", "@types/redux-mock-store": "^1.0.1", "@types/styled-components": "^5.1.29", "@types/uuid": "^10.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.0", "css-loader": "^6.8.1", "eslint": "^8.49.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "express": "^4.19.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "http-proxy-middleware": "^3.0.5", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.0.0", "prettier": "^3.3.3", "pretty-format": "29.7.0", "redux-mock-store": "^1.5.4", "resolve-url-loader": "^5.0.0", "sass": "^1.56.0", "sass-loader": "^16.0.0", "source-map-loader": "^5.0.0", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.1.1", "ts-jest": "^29.1.1", "ts-loader": "^9.4.2", "typescript": "~5.8.0", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0", "webpack-notifier": "^1.7.0"}}