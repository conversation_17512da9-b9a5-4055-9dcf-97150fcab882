{"name": "@sage/x3~0-prerequisites-test", "description": "X3 prerequisites test", "version": "54.0.58", "xtrem": {}, "author": "sage", "license": "UNLICENSED", "keywords": ["x3-application-package"], "main": "build/index.js", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "dependencies": {"@sage/x3-finance": "workspace:*", "@sage/x3-finance-data": "workspace:*", "@sage/x3-invoicing-data": "workspace:*", "@sage/x3-manufacturing": "workspace:*", "@sage/x3-manufacturing-data": "workspace:*", "@sage/x3-master-data": "workspace:*", "@sage/x3-physical-flows-data": "workspace:*", "@sage/x3-project-management": "workspace:*", "@sage/x3-project-management-data": "workspace:*", "@sage/x3-project-management-manufacturing": "workspace:*", "@sage/x3-project-management-stock": "workspace:*", "@sage/x3-purchasing": "workspace:*", "@sage/x3-purchasing-data": "workspace:*", "@sage/x3-purchasing-finance": "workspace:*", "@sage/x3-purchasing-manufacturing": "workspace:*", "@sage/x3-purchasing-sales": "workspace:*", "@sage/x3-sales": "workspace:*", "@sage/x3-sales-data": "workspace:*", "@sage/x3-sales-finance": "workspace:*", "@sage/x3-sales-manufacturing": "workspace:*", "@sage/x3-sales-project-management": "workspace:*", "@sage/x3-sales-stock": "workspace:*", "@sage/x3-stock": "workspace:*", "@sage/x3-stock-data": "workspace:*", "@sage/x3-structure": "workspace:*", "@sage/x3-system": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-ui": "workspace:*"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "typescript": "~5.8.0"}, "scripts": {"build": "tsc", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "xtrem lint", "load:qa:data": "xtrem layers --load qa", "start": "xtrem start", "test:functional": "xtrem test --integration", "test:functional:ci": "xtrem test --integration --ci", "view-report": "node ../../../scripts/allure/view-report.js"}}