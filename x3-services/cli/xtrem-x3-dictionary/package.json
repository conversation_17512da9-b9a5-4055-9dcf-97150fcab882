{"name": "@sage/xtrem-x3-dictionary", "description": "Code generator for client api", "version": "54.0.58", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@prettier/sync": "^0.5.0", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-toposort": "workspace:*", "@sage/xtrem-x3-gateway": "workspace:*", "@sage/xtrem-x3-oracle": "workspace:*", "@sage/xtrem-x3-sql": "workspace:*", "@sage/xtrem-x3-sql-manager": "workspace:*", "espree": "^10.1.0", "fs-extra": "^11.2.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "prettier": "^3.3.3", "semver": "^7.6.3", "typescript": "~5.8.0"}, "files": ["build", "README.md", "CHANGELOG.md"], "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/estree": "1.0.6", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/semver": "^7.5.2", "@types/source-map-support": "^0.5.7", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "mocha-jenkins-reporter": "^0.4.1", "source-map-support": "^0.5.12", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "prebuild": "node ./lib/functions/create-dependencies.mjs", "postbuild": "copyfiles test/**/*.json test/**/api.d.ts build", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-x3-dictionary.xml JUNIT_REPORT_NAME='xtrem-x3-dictionary' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}