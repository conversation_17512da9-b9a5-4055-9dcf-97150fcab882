{"name": "@sage/xtrem-x3-cli-dev", "description": "Xtrem X3 CLI dev", "version": "54.0.58", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "resources"], "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-x3-dictionary": "workspace:*", "lodash": "^4.17.21", "typescript": "~5.8.0", "yargs": "^17.7.2"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@types/chai": "^4.3.6", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/sinon": "^17.0.0", "@types/source-map-support": "^0.5.7", "@types/webpack": "5.28.5", "@types/yargs": "^17.0.24", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "pnpm clean && tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\" -\"build/lib/commands/utils/graphql-tests-entry-point.js\" -\"build/lib/commands/handlers/test/wdio.conf.js\" -\"build/cucumber-steps.js\" -\"build/lib/commands/utils/transformers.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "xtrem-bytenode": "xtrem-bytenode"}}