{"name": "@sage/xtrem-x3-agent-service", "version": "54.0.58", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli", "xtrem-x3-service-agent"], "description": "", "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "dependencies": {"@sage/xtrem-bytenode": "workspace:*", "@sage/xtrem-cli-compile": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-x3-dictionary": "workspace:*", "express": "^4.21.0", "fs-extra": "^11.2.0", "jszip": "^3.10.1"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.10.2", "@types/webpack": "5.28.5", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "mocha-jenkins-reporter": "^0.4.1", "source-map-support": "^0.5.12", "supertest": "^7.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && pnpm xtrem-bytenode -- -c -d -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "eslint -c .eslintrc.js --ext .ts lib", "xtrem-bytenode": "xtrem-bytenode"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}